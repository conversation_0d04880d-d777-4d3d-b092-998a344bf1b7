plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

apply from: "$rootProject.projectDir/common.gradle"
apply from: "$rootProject.projectDir/build_type.gradle"

android {
    namespace "com.solvibe.utils"

    buildFeatures {
        buildConfig true
    }
}

apply from: "$rootProject.projectDir/channel.gradle"

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.app.compat
    api libs.lifecycle.viewmodel.ktx
    api libs.lifecycle.runtime.ktx
    api libs.lifecycle.extensions
    implementation libs.fastjson
    api libs.gson
    api libs.mmkv
    api libs.timber

    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
}