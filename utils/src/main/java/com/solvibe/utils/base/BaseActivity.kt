package com.solvibe.utils.base

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import com.solvibe.utils.ext.configSystemBar

/**
 * @创作者：童岳洲
 * @日期：2021/8/23
 */
open class BaseActivity : AppCompatActivity() {
    @SuppressLint("InvalidAnalyticsName", "SourceLockedOrientationActivity")
    override fun onCreate(savedInstanceState: Bundle?) {
        enableEdgeToEdge()
        super.onCreate(savedInstanceState)

        configSystemBar {
            gestureNavigationTransparent = true
        }
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onPause() {
        super.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (supportFragmentManager.fragments.isNotEmpty()) {
            val fragments = supportFragmentManager.fragments
            for (mFragment in fragments) {
                mFragment.onActivityResult(requestCode, resultCode, data)
            }
        }
    }

    //******************************************************************************************************
    /**
     *
     * 方法一： 用于点击外部隐藏键盘实用性很高
     */
    //******************************************************************************************************
    /**
     * 对于所有的Activity都适用,所以定义在BaseActivity里面,被子类继承
     * 点击外部隐藏输入软键盘,获取到EditText的位置,做出点击判断
     */
    fun isClickEditText(view: View?, event: MotionEvent): Boolean {
        if (view != null && view is EditText) {
            val leftTop = intArrayOf(0, 0)
            // 获取输入框当前的 location 位置
            view.getLocationInWindow(leftTop)
            val left = leftTop[0]
            val top = leftTop[1]
            // 此处根据输入框左上位置和宽高获得右下位置
            val bottom = top + view.height
            val right = left + view.width
            return !(event.x > left && event.x < right && event.y > top && event.y < bottom)
        }
        return false
    }

    /**
     * 分发点击事件.点击外部键盘消失
     */
    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            // 获取当前获得当前焦点所在View
            val view = currentFocus
            if (isClickEditText(view, event)) {
                // 如果不是edittext，则隐藏键盘
                val inputMethodManager =
                    getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
                inputMethodManager.hideSoftInputFromWindow(view!!.windowToken, 0)
                whenEditLoseFocus()
            }
            return super.dispatchTouchEvent(event)
        }
        /**
         * 看源码可知superDispatchTouchEvent 是个抽象方法，用于自定义的Window
         * 此处目的是为了继续将事件由dispatchTouchEvent (MotionEvent event) 传递到onTouchEvent
         * (MotionEvent event) 必不可少，否则所有组件都不能触发 onTouchEvent (MotionEvent event)
         */
        return if (window.superDispatchTouchEvent(event)) {
            true
        } else onTouchEvent(event)
    }

    open fun whenEditLoseFocus() {
    }

    protected val rootView: ViewGroup
        protected get() = (findViewById<View>(android.R.id.content) as ViewGroup).getChildAt(0) as ViewGroup
    var fitSystemWindows: Boolean
        get() {
            val root = rootView
            return root.fitsSystemWindows
        }
        set(fitSystemWindows) {
            rootView.fitsSystemWindows = fitSystemWindows
        }
}