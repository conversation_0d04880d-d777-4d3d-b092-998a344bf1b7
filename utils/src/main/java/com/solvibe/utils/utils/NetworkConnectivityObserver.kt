package com.solvibe.utils.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow

class NetworkConnectivityObserver(context: Context) {
    private val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    /**
     * 通过 Flow 暴露网络连接状态。
     * @return 返回一个包含布尔值的 Flow，true 表示有网络，false 表示无网络。
     */
    fun observe(): Flow<Boolean> {
        return callbackFlow {
            val callback = object : ConnectivityManager.NetworkCallback() {
                // 当网络可用时调用
                override fun onAvailable(network: Network) {
                    super.onAvailable(network)
                    trySend(true)
                }

                // 当网络丢失时调用
                override fun onLost(network: Network) {
                    super.onLost(network)
                    trySend(false)
                }
            }

            // 创建网络请求
            val request = NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .build()

            // 注册网络回调
            connectivityManager.registerNetworkCallback(request, callback)

            // 发送初始网络状态
            val initialNetworkStatus = isNetworkAvailable()
            trySend(initialNetworkStatus)

            // 当 Flow 收集者取消时，注销回调
            awaitClose {
                connectivityManager.unregisterNetworkCallback(callback)
            }
        }
    }

    private fun isNetworkAvailable(): Boolean {
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }
}