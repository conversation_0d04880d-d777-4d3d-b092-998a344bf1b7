package com.solvibe.utils.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.os.Build
import android.text.TextUtils
import com.solvibe.utils.utils.TimeUtils.YMD_HMS2
import com.solvibe.utils.utils.TimeUtils.YMD_HMS_FORMAT2
import com.solvibe.utils.utils.TimeUtils.getTimeFMTStr
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone


object TimeUtils {
    const val YMD = "yyyy-MM-dd"
    const val YMD2 = "yyyyMMdd"
    const val YMD_HMS = "yyyyMMddHHmmss"
    const val YMD_HM = "yyyy-MM-dd HH:mm"
    const val YMD_HMS2 = "yyyy-MM-dd HH:mm:ss"
    const val YMD_H = "yyyy-MM-dd HH"
    const val YMD_HMS3 = "yyyy-MM-dd HH:mm:ss.SSS"
    const val HM = "HH:mm"
    private const val MD = "MM-dd"
    const val MD_HM = "M-d HH:mm"
    const val MD_CN = "M月d日"
    const val HMS = "HH:mm:ss"
    val YMD_FORMAT: DateFormat = SimpleDateFormat(YMD)
    val YMD_HMS_FORMAT: DateFormat = SimpleDateFormat(YMD_HMS)
    val YMD_HMS_FORMAT2: DateFormat = SimpleDateFormat(YMD_HMS2)
    val YMD_HM_FORMAT: DateFormat = SimpleDateFormat(YMD_HM)
    val HM_FORMAT: DateFormat = SimpleDateFormat(HM)
    val MD_FORMAT: DateFormat = SimpleDateFormat(MD)
    const val CONSTANT_SECOND_MS: Long = 1000
    const val CONSTANT_MINUTE_MS = 60 * CONSTANT_SECOND_MS
    const val CONSTANT_HOUR_MS = 2 * 60 * CONSTANT_MINUTE_MS
    const val CONSTANT_DAY_MS = (24 * 3600 * 1000).toLong()


    @JvmStatic
    fun long2Str(timemillis: Long, format: String?, mTimeZone: String): String {
        return date2Str(Date(timemillis), format, mTimeZone)
    }

    fun getTimeFMTStr(time: Long = System.currentTimeMillis(), format: String? = YMD_HMS2): String {
        return date2Str(Date(time), format, "")
    }

    fun date2Str(date: Date?, format: String?, mTimeZone: String): String {
        return if (date == null) "" else getFormat(format, mTimeZone)!!.format(date)
    }

    fun getFormat(format: String?, mTimeZone: String): DateFormat? {
        var df: DateFormat? = null
        df = when (format) {
            YMD -> YMD_FORMAT
            YMD_HMS -> YMD_HMS_FORMAT
            YMD_HMS2 -> YMD_HMS_FORMAT2
            YMD_HM -> YMD_HM_FORMAT
            HM -> HM_FORMAT
            MD -> MD_FORMAT
            else -> SimpleDateFormat(format)
        }
        //df.timeZone = TimeZone.getTimeZone(mTimeZone)
        return df
    }


    fun str2date(str: String?, format: String?, mTimeZone: String): Date {
        return Date(str2Long(str, format, mTimeZone))
    }

    fun str2Long(time: String?, format: String?, mTimeZone: String): Long {
        var tz: String? = null
        if (mTimeZone.isNullOrEmpty()) {
            tz = TimeZone.getDefault().getDisplayName(true, TimeZone.SHORT)
        } else {
            tz = mTimeZone
        }
        val s = getFormat(format, tz!!)
        try {
            return s!!.parse(time).time
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return 0
    }

    fun getFormatDate(millisecond: Long): String {
        val date = Date(millisecond)
        return YMD_HMS_FORMAT.format(date)
    }

    /**
     * 时分转成 秒
     *
     * @param time HH:mm
     * @return
     */
    fun Hm2Duration(time: String): Long {
        var totalSec: Long = 0
        val my = time.split(":").toTypedArray()
        if (my != null) {
            var multiply = 60
            for (i in my.indices.reversed()) {
                if (!TextUtils.isEmpty(my[i])) {
                    try {
                        totalSec += (my[i].toInt() * multiply).toLong()
                    } catch (e: Exception) {
                    }
                    multiply *= 60
                }
            }
        }
        return totalSec
    }

    fun getDayString(currentTimeMillis: Long): String {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = currentTimeMillis
        val dayOfMonth = calendar[Calendar.DAY_OF_MONTH]
        return if (dayOfMonth > 9) dayOfMonth.toString() else "0$dayOfMonth"
    }

    fun buildTimeStr(hour: Int, min: Int): String {
        return StringBuilder(if (hour < 10) "0" else "").append(hour.toString()).append(":")
            .append(if (min < 10) "0" else "").append(min.toString()).toString()
    }


    fun getGapTime(time: Long): String {
        val hours = time / (1000 * 60 * 60)
        val minutes = (time - hours * (1000 * 60 * 60)) / (1000 * 60)
        val diffTime = StringBuilder()
        return if (hours == 0L) {
            if (minutes == 0L) {
                diffTime.append("< 1 分钟").toString()
            } else {
                diffTime.append(minutes).append(" 分钟").toString()
            }
        } else {
            if (minutes == 0L) {
                diffTime.append(hours).append(" 小时").toString()
            } else {
                diffTime.append(hours).append(" 小时").append(minutes).append(" 分钟").toString()
            }
        }
    }


    fun isInChinaTimeZone(): Boolean {
        val tz = TimeZone.getDefault()
        val getID = tz.id //Asia/Shanghai
        val cal = Calendar.getInstance(Locale.getDefault())
        val zoneOffset = cal[Calendar.ZONE_OFFSET]
        val zone = zoneOffset / 60 / 60 / 1000 // 时区，东时区数字为正，西时区为负
        return zone == 8 && getID == "Asia/Shanghai" || zone == 6 && getID == "Asia/Urumqi"
    }

    fun isUsedBaidu(context: Activity?): Boolean {
//        if(isInChinaTimeZone()){
//            return true;
//        }else {
//            if(isGoogleServiceAvilable(context)){
//                return false;
//            }else {
//                return true;
//            }
//        }
        return false
    }

    fun isSameToday(nowTime: Long, sLastSyncPermissionList: Long): Boolean {
        return getStartTimeOfDay(nowTime) == getStartTimeOfDay(sLastSyncPermissionList)
    }

    @JvmStatic
    fun getStartTimeOfDay(currentTimeMillis: Long): Long {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = currentTimeMillis
        calendar[Calendar.HOUR_OF_DAY] = 0
        calendar[Calendar.MINUTE] = 0
        calendar[Calendar.SECOND] = 0
        calendar[Calendar.MILLISECOND] = 0
        return calendar.timeInMillis
    }

    /**
     * 显示时分<br></br>
     * 需要传入当天的时间，非date时间戳
     *
     * @param dayOfTime
     * @return
     */
    @JvmStatic
    fun showHHmm(dayOfTime: Long): String {
        var dayOfTime = dayOfTime
        dayOfTime /= 1000
        val hour = (dayOfTime / 3600).toInt()
        return String.format("%02d:%02d", hour, (dayOfTime - hour * 3600).toInt() / 60)
    }

    /**
     * 获取当前日期
     */
    fun showData(): String {
        return if (Build.VERSION.SDK_INT >= 26) {
            val current = LocalDateTime.now()
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
            current.format(formatter)
        } else {
            val tms = Calendar.getInstance()
            tms.get(Calendar.YEAR).toString() + "-" + tms.get(Calendar.MONTH)
                .toString() + "-" + (tms.get(Calendar.DAY_OF_MONTH) + 1).toString() + " " + tms.get(
                Calendar.HOUR_OF_DAY
            ).toString()
        }
    }

    /**
     * 时间戳转换时间
     */

    @SuppressLint("SimpleDateFormat")
    fun transToString(time: Long): String {
        return SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(time)
    }

    /**
     * 获取昨天日期
     * @return 时间戳
     */
    fun getYesterday(): Long {
        val dateFormat: DateFormat = SimpleDateFormat("yyyy-MM-dd")
        val cal = Calendar.getInstance()
        cal.add(Calendar.DATE, -1)
        return cal.time.time //your formatted date here
    }
}

fun String.date2timestamp(): Long {
    return YMD_HMS_FORMAT2.parse(this)?.time ?: 0
}

fun newTimeFMTStr(time: Long = System.currentTimeMillis(), format: String? = YMD_HMS2): String {
    return getTimeFMTStr(time, format)
}

fun Long.toDatetimeFMT(): String {
    return newLocalDateTimeFMT(this)
}

fun newLocalDateTimeFMT(time: Long = System.currentTimeMillis()): String {
    val dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault())

    // 获取今天的日期
    val today = LocalDate.now()

    return if (dateTime.toLocalDate() == today) {
        // 如果是今天，格式化为 1:19:45 PM
        dateTime.format(DateTimeFormatter.ofPattern("h:mm:ss a"))
    } else {
        // 如果是今天以前，格式化为 24/1/25
        dateTime.format(DateTimeFormatter.ofPattern("d/M/yy"))
    }
}