package com.solvibe.utils.ext

import com.google.gson.Gson
import com.google.gson.GsonBuilder

val gson by lazy { Gson() }

val fmtJson: Gson? by lazy { GsonBuilder().setPrettyPrinting().create() }

/**
 * json字符串转对象
 */
inline fun <reified T> String?.jsonAsOrNull(): T? {
    if (this == null) return null
    return try {
        gson.fromJson(this, T::class.java)
    } catch (e: Exception) {
        loge("json反序列化失败：${e.stackTraceToString()}")
        null
    }
}

/**
 * 对象转json字符串
 */
inline fun <reified T> T?.toJsonOrNull(): String? {
    if (this == null) return null
    return try {
        gson.toJson(this)
    } catch (e: Exception) {
        loge("json序列化失败：${e.stackTraceToString()}")
        null
    }
}

/**
 * 对象转格式化json字符串
 */
inline fun <reified T> T?.toFMTJsonOrNull(): String? {
    if (this == null) return ""
    return try {
        fmtJson?.toJson(this)
    } catch (e: Exception) {
        loge("json序列化失败：${e.stackTraceToString()}")
        ""
    }
}