package com.solvibe.utils.ext

import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow

/**
 * 收集并处理 Flow 中的每个元素，同时确保两次处理之间至少有一个最小间隔时间。
 * 如果处理事件本身花费的时间超过最小间隔，则不会额外延迟。
 * * @param minIntervalMillis 最小间隔时间，单位为毫秒。
 * @param action 对每个元素执行的操作。
 */
suspend fun <T> Flow<T>.collectWithMinInterval(
    minIntervalMillis: Long,
    action: suspend (T) -> Unit
) {
    collect { value ->
        val startTime = System.currentTimeMillis()

        // 执行处理操作
        action(value)

        // 计算并等待剩余的延迟时间
        val elapsedTime = System.currentTimeMillis() - startTime
        val remainingDelay = minIntervalMillis - elapsedTime

        if (remainingDelay > 0) {
            delay(remainingDelay)
        }
    }
}