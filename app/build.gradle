plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.imyfone.plugin'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
}

apply from: "$rootProject.projectDir/common.gradle"
apply from: "$rootProject.projectDir/channel.gradle"

android {
    namespace 'com.solvibe.base'

    defaultConfig {
        applicationId app_id
        versionCode app_ver_code as int
        versionName app_ver_name
    }

    signingConfigs {
        config {
            keyAlias 'solvibe'
            keyPassword 'solvibe666'
            storeFile file('./keys/solvibe.jks')
            storePassword 'solvibe666'

            // v2签名会确保正确的ZIP对齐
            v2SigningEnabled true
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.config
        }
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.config
        }
        rc {
            initWith release
        }
    }

    aaptOptions {
        cruncherEnabled = true
    }

    packagingOptions {
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
    }
}

android.buildTypes.release.ndk.debugSymbolLevel = 'FULL'

dependencies {
    api project(':net')
    api project(':main')
    // Import the Firebase BoM
    implementation platform(libs.firebase.bom)
    // When using the BoM, don't specify versions in Firebase dependencies
    implementation libs.firebase.analytics.ktx
    implementation libs.firebase.crashlytics.ktx
    implementation libs.androidx.core.ktx
    implementation libs.app.compat
    implementation libs.constraintlayout

    // 布局检查工具
    debugImplementation libs.codelocator.core
    debugImplementation libs.codelocator.lancet.all

    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
}
