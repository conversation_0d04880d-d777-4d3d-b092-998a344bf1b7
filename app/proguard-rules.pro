# 基础配置
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable
-keepattributes Exceptions,InnerClasses
-keepattributes RuntimeVisibleAnnotations
-ignorewarnings

# Kotlin相关
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }
-dontwarn kotlin.**
-keepclassmembers class **$WhenMappings {
    <fields>;
}

# Kotlinx Serialization
-keepclassmembers class kotlinx.serialization.json.** {
    *** Companion;
}
-keepclasseswithmembers class kotlinx.serialization.json.** {
    kotlinx.serialization.KSerializer serializer(...);
}

# AndroidX
-keep class androidx.** { *; }
-keep interface androidx.** { *; }
-dontwarn androidx.**
-keep class * implements androidx.viewbinding.ViewBinding { *; }

# Room
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *
-dontwarn androidx.room.paging.**

# Retrofit + OkHttp + Moshi
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**
-keep class com.squareup.moshi.** { *; }
-keep interface com.squareup.moshi.** { *; }
-dontwarn com.squareup.moshi.**
-keep class **JsonAdapter {
    <init>(...);
    <fields>;
}
-keepnames @com.squareup.moshi.JsonClass class *
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn javax.annotation.**
-dontwarn kotlin.Unit
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

# Glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
    <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
    **[] $VALUES;
    public *;
}
-dontwarn com.bumptech.glide.load.resource.bitmap.VideoDecoder

# Firebase & Google Services
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-keep class com.google.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**
-dontwarn com.google.ads.**
-keep public class com.google.ads.** { public protected *; }
-keep class com.solvibe.character.net.** {*;}

# Gson
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Protocol Buffers
-keep class * extends com.google.protobuf.GeneratedMessageLite { *; }
-keep class * extends com.google.protobuf.GeneratedMessage { *; }
-keepclassmembers class * extends com.google.protobuf.GeneratedMessage {
   <fields>;
}

# 如果使用 lite 版本
-keepclassmembers class * extends com.google.protobuf.GeneratedMessageLite {
   <fields>;
}

# 保护所有 proto 生成的类
-keep class com.solvibe.main.proto.** { *; }

# 保护枚举
-keepclassmembers class * extends com.google.protobuf.Internal$EnumLite {
    <fields>;
}
-keepclassmembers class * extends com.google.protobuf.Internal$EnumLiteMap {
    <fields>;
}

# 如果使用 protobuf-javalite
-keepclassmembers class * extends com.google.protobuf.GeneratedMessageLite {
    <fields>;
    <methods>;
}

# Coil
-dontwarn coil3.**
-keep class coil3.** { *; }

# MMKV
-keep class com.tencent.mmkv.** { *; }

# Lottie
-dontwarn com.airbnb.lottie.**
-keep class com.airbnb.lottie.** { *; }

# Media Players
-keep class androidx.media3.** { *; }
-dontwarn androidx.media3.**
-keep class xyz.doikki.videoplayer.** { *; }
-dontwarn xyz.doikki.videoplayer.**
-keep class tv.danmaku.ijk.** { *; }
-dontwarn tv.danmaku.ijk.**
-keep class com.google.android.exoplayer2.** { *; }
-dontwarn com.google.android.exoplayer2.**

# Billing
-keep class com.android.billingclient.** { *; }
-keep class com.android.vending.billing.** { *; }

# Koin
-keep class org.koin.** { *; }

# FastJson
-dontwarn com.alibaba.fastjson.**
-keep class com.alibaba.fastjson.** { *; }

# WorkManager
-keep class androidx.work.** { *; }
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.ListenableWorker

# App Specific
-keep class com.solvibe.main.bean.** { *; }
-keep class com.solvibe.main.model.** { *; }
-keep class com.solvibe.main.db.** { *; }
-keep class com.solvibe.base.** { *; }
-keep class com.solvibe.main.** { *; }
-keep class com.solvibe.main.fragment.** { *; }
-keep class com.solvibe.main.activity.** { *; }
-keep class com.solvibe.main.dialog.** { *; }
-keep class com.solvibe.main.api.** { *; }
-keep class com.solvibe.main.adapter.** { *; }
-keep class com.solvibe.main.dao.** { *; }

# Third Party Libraries
-keep class com.kelin.photoselector.** { *; }
-keep class com.kelin.okpermission.** { *; }
-keep class com.xwdz.download.** { *; }
-keep class com.appsflyer.** { *; }
-keep class org.apache.** { *; }
-keep public class com.android.installreferrer.** { *; }
-keep public class com.miui.referrer.** { *; }
-keep public class * extends android.app.Application

# Huawei
-keep class com.huawei.hianalytics.** { *; }
-keep class com.huawei.updatesdk.** { *; }
-keep class com.huawei.hms.** { *; }

# 通用规则
# WebView
-keep class android.webkit.** { *; }
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}
-keepclassmembers class * extends android.webkit.WebView {
    public <init>(android.content.Context, android.util.AttributeSet);
}

#友盟统计
####################umeng##################
-keep class com.umeng.** {*;}

-keep class org.repackage.** {*;}

-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}


# 枚举类
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Parcelable
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# fastjson混淆
-dontwarn com.alibaba.fastjson.**
-keep class com.alibaba.**{*;}
-keep class com.alibaba.fastjson.**{*; }

# kotlin-reflect
-keep class kotlin.reflect.**{*;}
