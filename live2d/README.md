# Live2D模块文档

## 概述

Live2D模块是基于Live2D Cubism SDK的Android实现，提供了完整的Live2D模型加载、渲染、交互和控制功能。

## 核心组件

### 1. Live2dView

主要的View组件，继承自GLSurfaceView，提供Live2D模型的显示界面。

**主要API：**

```kotlin
// 背景设置
fun setBackgroundImage(resourceId: Int)  // 设置背景图片
fun clearBackgroundImage()              // 清除背景图片

// 模型大小设置  
fun setModelSize(w: Int, h: Int)        // 设置固定宽高
fun setModelHeight(h: Int)              // 设置高度，宽度自适应保持比例

// 模型位置设置
fun setModelVerticalOffset(offsetPixels: Float)  // 设置垂直偏移
```

### 2. LAppDelegate

应用程序委托类，管理Live2D的生命周期和全局状态。

**主要功能：**

- OpenGL上下文管理
- 背景图片管理
- 模型大小和位置控制
- 生命周期管理

### 3. LAppLive2DManager

Live2D模型管理器，负责模型的加载、更新和渲染。

**主要功能：**

- 模型加载和切换
- 渲染循环管理
- 触摸事件处理
- 动作播放控制

### 4. LAppModel

单个Live2D模型的封装类，继承自CubismUserModel。

**主要功能：**

- 模型数据加载
- 动作和表情播放
- 物理效果处理
- 渲染参数管理

## 初始化流程

### 1. 框架初始化

```
LAppDelegate.init() 
  ↓
CubismFramework.startUp()
  ↓
设置日志输出
  ↓
创建纹理管理器
```

### 2. OpenGL初始化

```
Live2dView.onSurfaceCreated()
  ↓
LAppDelegate.onSurfaceCreated()
  ↓
OpenGL状态设置
  ↓
CubismFramework.initialize()
```

### 3. 模型加载

```
LAppLive2DManager.setUpModel()
  ↓
加载模型配置文件
  ↓
创建LAppModel实例
  ↓
LAppModel.setupModel()
  ↓
加载MOC3、纹理、动作等资源
```

## 渲染流程

### 每帧渲染循环

```
Live2dView.onDrawFrame()
  ↓
LAppDelegate.run()
  ↓
更新时间差
  ↓
渲染背景图片
  ↓
LAppLive2DManager.onUpdate()
  ↓
应用模型缩放和位置
  ↓
LAppModel.update()
  ↓
LAppModel.draw()
```

### 背景渲染

```
LAppDelegate.renderBackground()
  ↓
检查背景纹理是否存在
  ↓
设置OpenGL状态
  ↓
LAppSprite.render()
```

### 模型渲染

```
LAppModel.draw()
  ↓
应用模型矩阵变换
  ↓
CubismRenderer.drawModel()
  ↓
OpenGL绘制调用
```

## 交互处理

### 触摸事件流程

```
Live2dView.onTouchEvent()
  ↓
TouchManager处理触摸数据
  ↓
LAppDelegate.onTouchBegan/Moved()
  ↓
LAppLive2DManager.onTap/onDrag()
  ↓
LAppModel.setDragging/onTap()
```

### 动作播放

```
LAppModel.startMotion()
  ↓
检查动作优先级
  ↓
CubismMotionManager.startMotionPriority()
  ↓
动作数据应用到模型参数
```

## 主要类说明

| 类名                 | 功能    | 关键方法                                                  |
|--------------------|-------|-------------------------------------------------------|
| Live2dView         | 主视图组件 | setBackgroundImage(), setModelHeight()                |
| LAppDelegate       | 应用委托  | init(), run(), onSurfaceCreated()                     |
| LAppLive2DManager  | 模型管理器 | setUpModel(), onUpdate(), changeScene()               |
| LAppModel          | 模型封装  | setupModel(), update(), draw()                        |
| LAppTextureManager | 纹理管理  | createTextureFromPngFile(), createTextureFromBitmap() |
| LAppSprite         | 精灵渲染  | render(), setColor()                                  |
| TouchManager       | 触摸管理  | touchesBegan(), touchesMoved()                        |

## 使用示例

### 基本使用

```kotlin
// 在Activity中
val live2dView = Live2dView(this)
setContentView(live2dView)

// 设置模型高度
live2dView.setModelHeight(500)

// 设置背景
live2dView.setBackgroundImage(R.drawable.background)

// 设置位置
live2dView.setModelVerticalOffset(-100f)
```

### 在Compose中使用

```kotlin
@Composable
fun Live2DScreen() {
    AndroidView(
        factory = { context ->
            Live2dView(context).apply {
                setModelHeight(500)
                setBackgroundImage(R.drawable.bg)
                setModelVerticalOffset(-100f)
            }
        }
    )
}
```

## 配置说明

### LAppDefine常量

- `DEBUG_LOG_ENABLE`: 调试日志开关
- `MOC_CONSISTENCY_VALIDATION_ENABLE`: MOC3文件验证
- `PREMULTIPLIED_ALPHA_ENABLE`: 预乘Alpha开关

### 资源路径

- 模型文件：`assets/模型名/`
- 着色器：`assets/Shaders/`
- 配置文件：`*.model3.json`

## 文件结构说明

| 文件名                   | 功能描述                     |
|-----------------------|--------------------------|
| Live2dView.kt         | 主视图组件，提供用户API接口          |
| LAppDelegate.kt       | 应用委托，管理全局状态和生命周期         |
| LAppLive2DManager.kt  | 模型管理器，处理模型加载和渲染          |
| LAppModel.kt          | 单个模型封装，继承CubismUserModel |
| LAppView.kt           | Live2D视图处理，管理视图矩阵和渲染     |
| LAppTextureManager.kt | 纹理管理，处理纹理加载和缓存           |
| LAppSprite.kt         | 精灵渲染，用于背景等2D元素           |
| LAppSpriteShader.kt   | 精灵着色器管理                  |
| TouchManager.kt       | 触摸事件管理和处理                |
| LAppPal.kt            | 平台抽象层，提供文件加载等功能          |
| LAppDefine.kt         | 常量定义和枚举                  |
| LAppWavFileHandler.kt | WAV音频文件处理                |

## 注意事项

1. **生命周期管理**：确保在Activity/Fragment销毁时正确清理资源
2. **OpenGL上下文**：所有OpenGL操作必须在GL线程中执行
3. **内存管理**：大型纹理和模型文件需要注意内存使用
4. **线程安全**：UI操作和GL操作需要在正确的线程中执行
5. **资源路径**：模型文件必须放在assets目录下
6. **Context引用**：避免长期持有Activity Context引用

## 性能优化建议

1. 合理设置模型大小，避免过大的纹理
2. 及时释放不需要的资源
3. 避免频繁的模型切换
4. 使用合适的动作优先级
5. 复用矩阵对象，减少GC压力
6. 缓存计算结果，避免重复计算

## 已知问题和限制

1. **内存泄漏风险**：Context引用可能导致内存泄漏
2. **频繁对象创建**：每帧创建临时对象影响性能
3. **线程安全**：部分操作缺少同步机制
4. **资源管理**：纹理资源可能未及时清理
