plugins {
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
}

apply(from = "$rootDir/common.gradle")
apply(from = "$rootDir/build_type.gradle")

android {
    namespace = "com.solvibe.live2d"
}

apply(from = "$rootDir/channel.gradle")

dependencies {
    implementation(project(":utils"))

    api(fileTree("libs") {
        include("*.aar", "*.jar")
    })
    implementation(libs.androidx.core.ktx)
    implementation(libs.app.compat)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}