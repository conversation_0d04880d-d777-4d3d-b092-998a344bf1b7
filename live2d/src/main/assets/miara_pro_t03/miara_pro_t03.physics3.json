{"Version": 3, "Meta": {"PhysicsSettingCount": 19, "TotalInputCount": 82, "TotalOutputCount": 63, "VertexCount": 94, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "Twin tail"}, {"Id": "PhysicsSetting2", "Name": "Front hair"}, {"Id": "PhysicsSetting3", "Name": "Side hair"}, {"Id": "PhysicsSetting4", "Name": "Back hair"}, {"Id": "PhysicsSetting5", "Name": "Aho hair sway"}, {"Id": "PhysicsSetting6", "Name": "Left behind the back"}, {"Id": "PhysicsSetting7", "Name": "<PERSON>leeve <PERSON>"}, {"Id": "PhysicsSetting8", "Name": "Sleeve right"}, {"Id": "PhysicsSetting9", "Name": "loincloth"}, {"Id": "PhysicsSetting10", "Name": "B<PERSON><PERSON> accessory"}, {"Id": "PhysicsSetting11", "Name": "Move bust Y"}, {"Id": "PhysicsSetting12", "Name": "Left cloth"}, {"Id": "PhysicsSetting13", "Name": "Left cloth twist"}, {"Id": "PhysicsSetting14", "Name": "Right cloth"}, {"Id": "PhysicsSetting15", "Name": "Right cloth twist"}, {"Id": "PhysicsSetting16", "Name": "Right rear wings"}, {"Id": "PhysicsSetting17", "Name": "Fairy wings fluctuate"}, {"Id": "PhysicsSetting18", "Name": "Move Bust X"}, {"Id": "PhysicsSetting19", "Name": "Right hair accessory"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairTail"}, "VertexIndex": 1, "Scale": 1.175, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairTail2"}, "VertexIndex": 2, "Scale": 1.175, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.99, "Delay": 0.9, "Acceleration": 1.5, "Radius": 30}, {"Position": {"X": 0, "Y": 53.6}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 23.6}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -28.2, "Default": -1.95, "Maximum": 24.3}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFront"}, "VertexIndex": 1, "Scale": 2.211, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairFront2"}, "VertexIndex": 2, "Scale": 2.211, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 20}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -30, "Default": 0, "Maximum": 30}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairSide"}, "VertexIndex": 1, "Scale": 2.24, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairSide2"}, "VertexIndex": 2, "Scale": 2.24, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 30}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.5, "Radius": 25}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -30, "Default": 0, "Maximum": 30}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 55, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 35, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairBack"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairBack2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.85, "Delay": 0.8, "Acceleration": 1.5, "Radius": 40}, {"Position": {"X": 0, "Y": 80}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 40}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -30, "Default": 0, "Maximum": 30}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 55, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 10, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairAho1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairAho2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 40}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 35}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -30, "Default": 0, "Maximum": 30}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh109"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh109"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh109"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh109"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh109"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh109"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh109"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 120}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 135}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 150}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamArmL1"}, "Weight": 35, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamArmL2"}, "Weight": 35, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamSleeveL"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSleeveL2"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSleeveRibbonL"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.96, "Delay": 0.9, "Acceleration": 0.8, "Radius": 20}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.95, "Delay": 0.9, "Acceleration": 0.8, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 1, "Delay": 0.9, "Acceleration": 0.75, "Radius": 10}], "Normalization": {"Position": {"Minimum": -16.8, "Default": 0.65, "Maximum": 18.1}, "Angle": {"Minimum": -30, "Default": 0, "Maximum": 30}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamArmR1"}, "Weight": 35, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamArmR2"}, "Weight": 35, "Type": "X", "Reflect": true}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamSleeve"}, "VertexIndex": 1, "Scale": 4.061, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSleeve2"}, "VertexIndex": 2, "Scale": 4.136, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamSleeveRibbon"}, "VertexIndex": 3, "Scale": 1.809, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 12}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.5, "Radius": 12}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.91, "Delay": 0.88, "Acceleration": 0.8, "Radius": 13}, {"Position": {"X": 0, "Y": 30.1}, "Mobility": 1, "Delay": 0.9, "Acceleration": 0.8, "Radius": 5.1}], "Normalization": {"Position": {"Minimum": -16.8, "Default": 0.65, "Maximum": 18.1}, "Angle": {"Minimum": -31.9, "Default": 0, "Maximum": 30.2}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamPepram1"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamPepram2"}, "VertexIndex": 1, "Scale": 1, "Weight": 80, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 14}, "Mobility": 0.9, "Delay": 1, "Acceleration": 1.5, "Radius": 14}, {"Position": {"X": 0, "Y": 28}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 14}], "Normalization": {"Position": {"Minimum": -16.8, "Default": 0.65, "Maximum": 18.1}, "Angle": {"Minimum": -30, "Default": 0, "Maximum": 30}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 80, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamChestAccessory"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.8, "Radius": 10}], "Normalization": {"Position": {"Minimum": -16.8, "Default": 0.65, "Maximum": 18.1}, "Angle": {"Minimum": -30, "Default": 0, "Maximum": 30}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAllY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBust"}, "VertexIndex": 1, "Scale": 4, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh76"}, "VertexIndex": 1, "Scale": 10, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh76"}, "VertexIndex": 2, "Scale": 10, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh76"}, "VertexIndex": 3, "Scale": 40, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh76"}, "VertexIndex": 4, "Scale": 40, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh76"}, "VertexIndex": 5, "Scale": 40, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh76"}, "VertexIndex": 6, "Scale": 40, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh76"}, "VertexIndex": 7, "Scale": 40, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 120}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 135}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 150}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamCloth1"}, "VertexIndex": 1, "Scale": 20, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 40, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh77"}, "VertexIndex": 1, "Scale": 10, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh77"}, "VertexIndex": 2, "Scale": 40, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh77"}, "VertexIndex": 3, "Scale": 40, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh77"}, "VertexIndex": 4, "Scale": 40, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh77"}, "VertexIndex": 5, "Scale": 40, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh77"}, "VertexIndex": 6, "Scale": 40, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh77"}, "VertexIndex": 7, "Scale": 40, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 120}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 135}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 150}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -21.1, "Default": 0, "Maximum": 0}}}, {"Id": "PhysicsSetting15", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 100, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamCloth2"}, "VertexIndex": 1, "Scale": 20, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting16", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 60, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 0, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh108"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh108"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh108"}, "VertexIndex": 3, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh108"}, "VertexIndex": 4, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh108"}, "VertexIndex": 5, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh108"}, "VertexIndex": 6, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh108"}, "VertexIndex": 7, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 120}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 135}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 150}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting17", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamMoveX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamMoveY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh179"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh179"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh179"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh179"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh179"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh181"}, "VertexIndex": 1, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh181"}, "VertexIndex": 2, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh181"}, "VertexIndex": 3, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh181"}, "VertexIndex": 4, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh181"}, "VertexIndex": 5, "Scale": 50, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 60}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 75}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 90}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 105}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 120}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 135}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 150}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -50, "Default": 0, "Maximum": 50}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting18", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamBustX"}, "VertexIndex": 1, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting19", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 30, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAllRotate"}, "Weight": 20, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairCloth"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamHairCloth2"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 15}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}