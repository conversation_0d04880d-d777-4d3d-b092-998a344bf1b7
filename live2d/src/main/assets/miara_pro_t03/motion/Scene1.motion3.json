{"Version": 3, "Meta": {"Duration": 2.633, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 70, "TotalSegmentCount": 355, "TotalPointCount": 1055, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamMoveX", "Segments": [0, 31.4, 1, 0.178, 31.4, 0.356, 22.2, 0.533, 22.2, 1, 0.756, 22.2, 0.978, 27.129, 1.2, 31.4, 1, 1.467, 36.526, 1.733, 37.6, 2, 37.6, 1, 2.211, 37.6, 2.422, 32.976, 2.633, 31.714]}, {"Target": "Parameter", "Id": "ParamMoveY", "Segments": [0, 64.6, 1, 0.178, 64.6, 0.356, 52, 0.533, 52, 1, 0.756, 52, 0.978, 63.4, 1.2, 63.4, 1, 1.467, 63.4, 1.733, 50.2, 2, 50.2, 1, 2.211, 50.2, 2.422, 60.94, 2.633, 63.87]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, -3, 1, 0.2, -3, 0.4, 4, 0.6, 4, 1, 0.878, 4, 1.156, 0.716, 1.433, -3, 1, 1.678, -6.27, 1.922, -7, 2.167, -7, 1, 2.322, -7, 2.478, -3.516, 2.633, -3.051]}, {"Target": "Parameter", "Id": "ParamScaling", "Segments": [0, 0, 1, 0.178, 0, 0.356, -4, 0.533, -4, 1, 0.756, -4, 0.978, -2.404, 1.2, 0, 1, 1.467, 2.884, 1.733, 4, 2, 4, 1, 2.211, 4, 2.422, 0.39, 2.633, 0.029]}, {"Target": "Parameter", "Id": "ParamEffect1", "Segments": [0, 0, 1, 0.067, 0, 0.133, 4, 0.2, 4, 1, 0.278, 4, 0.356, -1, 0.433, -1, 1, 0.5, -1, 0.567, 3, 0.633, 3, 1, 0.711, 3, 0.789, -2, 0.867, -2, 1, 0.933, -2, 1, 3, 1.067, 3, 1, 1.156, 3, 1.244, 0, 1.333, 0, 1, 1.4, 0, 1.467, 4, 1.533, 4, 1, 1.611, 4, 1.689, -1, 1.767, -1, 1, 1.833, -1, 1.9, 3, 1.967, 3, 1, 2.044, 3, 2.122, -2, 2.2, -2, 1, 2.267, -2, 2.333, 3, 2.4, 3, 1, 2.478, 3, 2.556, 0.703, 2.633, 0.129]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, -30, 1, 0.111, -30, 0.222, -25, 0.333, -25, 1, 0.444, -25, 0.556, -30, 0.667, -30, 1, 0.8, -30, 0.933, -23, 1.067, -23, 1, 1.222, -23, 1.378, -30, 1.533, -30, 1, 1.733, -30, 1.933, -25, 2.133, -25, 1, 2.3, -25, 2.467, -29.395, 2.633, -29.944]}, {"Target": "Parameter", "Id": "ParamFlapping8", "Segments": [0, 0, 0, 0.433, -30, 0, 0.767, 0, 0, 1.1, 30, 0, 1.433, 0, 0, 1.767, -30, 0, 2.1, 0, 0, 2.433, 30, 0, 2.633, 3.855]}, {"Target": "Parameter", "Id": "ParamFlapping7", "Segments": [0, -30, 0, 0.333, 0, 0, 0.667, 30, 0, 1, 0, 0, 1.333, -30, 0, 1.667, 0, 0, 2, 30, 0, 2.333, 0, 0, 2.633, -27]}, {"Target": "Parameter", "Id": "ParamFlapping4", "Segments": [0, 0, 0, 0.467, 30, 0, 0.8, 0, 0, 1.133, -30, 0, 1.467, 0, 0, 1.833, 30, 0, 2.133, 0, 0, 2.467, -30, 0, 2.633, -5]}, {"Target": "Parameter", "Id": "ParamFlapping3", "Segments": [0, -30, 0, 0.333, 0, 0, 0.667, 30, 0, 1, 0, 0, 1.333, -30, 0, 1.7, 0, 0, 2, 30, 0, 2.333, 0, 0, 2.633, -27]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLight1", "Segments": [0, 0, 0, 2.633, 9.875]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLight2", "Segments": [0, 0, 0, 2.633, 9.875]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLight3", "Segments": [0, 0, 0, 2.633, 9.875]}, {"Target": "Parameter", "Id": "ParamWaterSurface1", "Segments": [0, 0, 1, 0.878, 0, 1.756, 39.006, 2.633, 39.981]}, {"Target": "Parameter", "Id": "ParamWaterSurface2", "Segments": [0, 0, 0, 2.633, 39.5]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLower1", "Segments": [0, 0, 0, 2.633, 39.5]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLower2", "Segments": [0, 0, 0, 2.633, 39.5]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceBack1", "Segments": [0, 0, 0, 2.633, 39.5]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceBack2", "Segments": [0, 0, 0, 2.633, 39.5]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -16, 1, 0.222, -16, 0.444, -30, 0.667, -30, 1, 0.889, -30, 1.111, -16, 1.333, -16, 1, 1.556, -16, 1.778, -30, 2, -30, 1, 2.211, -30, 2.422, -17.365, 2.633, -16.101]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -25, 1, 0.111, -25, 0.222, -19, 0.333, -19, 1, 0.556, -19, 0.778, -19.057, 1, -20, 1, 1.111, -20.472, 1.222, -25, 1.333, -25, 1, 1.444, -25, 1.556, -19, 1.667, -19, 1, 1.889, -19, 2.111, -19.057, 2.333, -20, 1, 2.433, -20.425, 2.533, -24.135, 2.633, -24.873]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -7, 1, 0.233, -7, 0.467, -30, 0.7, -30, 1, 0.911, -30, 1.122, -7, 1.333, -7, 1, 1.567, -7, 1.8, -30, 2.033, -30, 1, 2.233, -30, 2.433, -9.357, 2.633, -7.184]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.144, 1, 0.289, 1, 0.433, 1, 1, 0.5, 1, 0.567, 0, 0.633, 0, 1, 0.667, 0, 0.7, 0, 0.733, 0, 1, 0.822, 0, 0.911, 1, 1, 1, 1, 1.544, 1, 2.089, 1, 2.633, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.144, 1, 0.289, 1, 0.433, 1, 1, 0.5, 1, 0.567, 0, 0.633, 0, 1, 0.667, 0, 0.7, 0, 0.733, 0, 1, 0.822, 0, 0.911, 1, 1, 1, 1, 1.544, 1, 2.089, 1, 2.633, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0.7, 1, 0.222, 0.7, 0.444, 1, 0.667, 1, 1, 0.889, 1, 1.111, 0.7, 1.333, 0.7, 1, 1.556, 0.7, 1.778, 1, 2, 1, 1, 2.211, 1, 2.422, 0.729, 2.633, 0.702]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0.3, 1, 0.222, 0.3, 0.444, -0.3, 0.667, -0.3, 1, 0.889, -0.3, 1.111, 0.3, 1.333, 0.3, 1, 1.556, 0.3, 1.778, -0.3, 2, -0.3, 1, 2.211, -0.3, 2.422, 0.242, 2.633, 0.296]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.478, 0, 0.522, -1, 0.567, -1, 1, 0.689, -1, 0.811, 0, 0.933, 0, 1, 1.067, 0, 1.2, 0, 1.333, 0, 1, 1.511, 0, 1.689, 0, 1.867, 0, 1, 1.933, 0, 2, 0.3, 2.067, 0.3, 1, 2.122, 0.3, 2.178, 0, 2.233, 0, 1, 2.367, 0, 2.5, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.511, 0, 1.689, 0, 1.867, 0, 1, 2.122, 0, 2.378, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.478, 0, 0.522, -1, 0.567, -1, 1, 0.689, -1, 0.811, 0, 0.933, 0, 1, 1.067, 0, 1.2, 0, 1.333, 0, 1, 1.511, 0, 1.689, 0, 1.867, 0, 1, 1.933, 0, 2, 0.3, 2.067, 0.3, 1, 2.122, 0.3, 2.178, 0, 2.233, 0, 1, 2.367, 0, 2.5, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.511, 0, 1.689, 0, 1.867, 0, 1, 2.122, 0, 2.378, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, -0.2, 1, 0.222, -0.2, 0.444, -0.2, 0.667, -0.2, 1, 0.889, -0.2, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, -0.2, 2, -0.2, 1, 2.211, -0.2, 2.422, -0.2, 2.633, -0.2]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, -0.2, 1, 0.222, -0.2, 0.444, -0.2, 0.667, -0.2, 1, 0.889, -0.2, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, -0.2, 2, -0.2, 1, 2.211, -0.2, 2.422, -0.2, 2.633, -0.2]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 1, 1, 0.222, 1, 0.444, 1, 0.667, 1, 1, 0.889, 1, 1.111, 1, 1.333, 1, 1, 1.556, 1, 1.778, 1, 2, 1, 1, 2.211, 1, 2.422, 1, 2.633, 1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 1, 1, 0.222, 1, 0.444, 1, 0.667, 1, 1, 0.889, 1, 1.111, 1, 1.333, 1, 1, 1.556, 1, 1.778, 1, 2, 1, 1, 2.211, 1, 2.422, 1, 2.633, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 8, 1, 0.222, 8, 0.444, -4, 0.667, -4, 1, 0.889, -4, 1.111, 8, 1.333, 8, 1, 1.556, 8, 1.778, -4, 2, -4, 1, 2.211, -4, 2.422, 6.83, 2.633, 7.913]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, -1, 1, 0.222, -1, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, -1, 1.333, -1, 1, 1.556, -1, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, -0.903, 2.633, -0.993]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamArmL1", "Segments": [0, 0, 1, 0.1, -0.904, 0.2, -2, 0.3, -2, 1, 0.522, -2, 0.744, 3, 0.967, 3, 1, 1.089, 3, 1.211, 0, 1.333, 0, 1, 1.433, 0, 1.533, -2, 1.633, -2, 1, 1.856, -2, 2.078, 3, 2.3, 3, 1, 2.411, 3, 2.522, 0.521, 2.633, 0.07]}, {"Target": "Parameter", "Id": "ParamArmL2", "Segments": [0, 0, 1, 0.111, 1.939, 0.222, 3, 0.333, 3, 1, 0.533, 3, 0.733, -4.735, 0.933, -4.735, 1, 1.067, -4.735, 1.2, 0, 1.333, 0, 1, 1.444, 0, 1.556, 3, 1.667, 3, 1, 1.867, 3, 2.067, -4.735, 2.267, -4.735, 1, 2.389, -4.735, 2.511, -0.756, 2.633, -0.093]}, {"Target": "Parameter", "Id": "ParamArmL3", "Segments": [0, 0, 1, 0.122, -3.749, 0.244, -7.357, 0.367, -7.357, 1, 0.567, -7.357, 0.767, 9.921, 0.967, 9.921, 1, 1.089, 9.921, 1.211, 0, 1.333, 0, 1, 1.456, 0, 1.578, -7.357, 1.7, -7.357, 1, 1.9, -7.357, 2.1, 9.921, 2.3, 9.921, 1, 2.411, 9.921, 2.522, 1.722, 2.633, 0.231]}, {"Target": "Parameter", "Id": "ParamFingerL1X", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamFingerL1Z", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamFingerL2", "Segments": [0, -8, 1, 0.133, -8, 0.267, 28, 0.4, 28, 1, 0.511, 28, 0.622, 22.164, 0.733, 14.142, 1, 0.844, 6.121, 0.956, -5.969, 1.067, -7.218, 1, 1.156, -8.216, 1.244, -8, 1.333, -8, 1, 1.467, -8, 1.6, 28, 1.733, 28, 1, 1.844, 28, 1.956, 22.164, 2.067, 14.142, 1, 2.178, 6.121, 2.289, -4.71, 2.4, -7.218, 1, 2.478, -8.973, 2.556, -9.021, 2.633, -9.006]}, {"Target": "Parameter", "Id": "ParamFingerL3", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamFingerL4", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamArmR1", "Segments": [0, -17, 1, 0.222, -17, 0.444, -30, 0.667, -30, 1, 0.889, -30, 1.111, -17, 1.333, -17, 1, 1.556, -17, 1.778, -30, 2, -30, 1, 2.211, -30, 2.422, -18.267, 2.633, -17.094]}, {"Target": "Parameter", "Id": "ParamArmR2", "Segments": [0, -14, 1, 0.233, -14, 0.467, -18, 0.7, -18, 1, 0.911, -18, 1.122, -14, 1.333, -14, 1, 1.567, -14, 1.8, -18, 2.033, -18, 1, 2.233, -18, 2.433, -14.41, 2.633, -14.032]}, {"Target": "Parameter", "Id": "ParamArmR3", "Segments": [0, -8, 1, 0.233, -8, 0.467, -14, 0.7, -14, 1, 0.911, -14, 1.122, -8, 1.333, -8, 1, 1.567, -8, 1.8, -14, 2.033, -14, 1, 2.233, -14, 2.433, -8.615, 2.633, -8.048]}, {"Target": "Parameter", "Id": "ParamFingerR1", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamFingerR2", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamFingerR3", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamFingerR4", "Segments": [0, 0, 1, 0.222, 0, 0.444, 0, 0.667, 0, 1, 0.889, 0, 1.111, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.211, 0, 2.422, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamLegL1X", "Segments": [0, -28, 1, 0.222, -28, 0.444, -30, 0.667, -30, 1, 0.778, -30, 0.889, -21, 1, -21, 1, 1.111, -21, 1.222, -28, 1.333, -28, 1, 1.556, -28, 1.778, -30, 2, -30, 1, 2.111, -30, 2.222, -21, 2.333, -21, 1, 2.433, -21, 2.533, -26.67, 2.633, -27.804]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, -4, 1, 0.222, -4, 0.444, -30, 0.667, -30, 1, 0.778, -30, 0.889, -26.608, 1, -18, 1, 1.111, -9.392, 1.222, -4, 1.333, -4, 1, 1.556, -4, 1.778, -30, 2, -30, 1, 2.111, -30, 2.222, -26.608, 2.333, -18, 1, 2.433, -10.253, 2.533, -5.111, 2.633, -4.16]}, {"Target": "Parameter", "Id": "ParamLegL2X", "Segments": [0, -18, 1, 0.1, -18, 0.2, -16.054, 0.3, -12.456, 1, 0.422, -8.059, 0.544, -6, 0.667, -6, 1, 0.778, -6, 0.889, -5.88, 1, -8, 1, 1.111, -10.12, 1.222, -18, 1.333, -18, 1, 1.433, -18, 1.533, -16.054, 1.633, -12.456, 1, 1.756, -8.059, 1.878, -6, 2, -6, 1, 2.111, -6, 2.222, -8, 2.333, -8, 1, 2.433, -8, 2.533, -16.1, 2.633, -17.72]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 20, 1, 0.222, -1.683, 0.444, -24.049, 0.667, -28.368, 1, 0.778, -30.889, 0.889, -30, 1, -30, 1, 1.111, -30, 1.222, 20, 1.333, 20, 1, 1.556, 20, 1.778, -28.368, 2, -28.368, 1, 2.111, -30.889, 2.222, -30, 2.333, -30, 1, 2.433, -30, 2.533, 10.5, 2.633, 18.6]}, {"Target": "Parameter", "Id": "ParamLegL3X", "Segments": [0, 30, 1, 0.222, 30, 0.444, 30, 0.667, 30, 1, 0.778, 30, 0.889, 30, 1, 30, 1, 1.111, 30, 1.222, 30, 1.333, 30, 1, 1.556, 30, 1.778, 30, 2, 30, 1, 2.111, 30, 2.222, 30, 2.333, 30, 1, 2.433, 30, 2.533, 30, 2.633, 30]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, 14, 1, 0.111, 14, 0.222, 14.238, 0.333, 6, 1, 0.444, -2.238, 0.556, -30, 0.667, -30, 1, 0.733, -30, 0.8, -30, 0.867, -30, 1, 0.978, -30, 1.089, 20.125, 1.2, 20.125, 1, 1.244, 20.125, 1.289, 15.312, 1.333, 14, 1, 1.444, 10.719, 1.556, 10.354, 1.667, 6, 1, 1.778, 1.646, 1.889, -30, 2, -30, 1, 2.067, -30, 2.133, -30, 2.2, -30, 1, 2.311, -30, 2.422, 20.125, 2.533, 20.125, 1, 2.567, 20.125, 2.6, 16.68, 2.633, 14.957]}, {"Target": "Parameter", "Id": "ParamLegLDO", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.244, 0, 0.256, -1, 0.267, -1, 1, 0.322, -1, 0.378, -1, 0.433, -1, 1, 0.444, -1, 0.456, -0.8, 0.467, -0.8, 1, 0.667, -0.8, 0.867, -1, 1.067, -1, 1, 1.078, -1, 1.089, 0, 1.1, 0, 1, 1.178, 0, 1.256, 0, 1.333, 0, 1, 1.411, 0, 1.489, 0, 1.567, 0, 1, 1.578, 0, 1.589, -1, 1.6, -1, 1, 1.867, -1, 2.133, -1, 2.4, -1, 1, 2.411, -1, 2.422, 0, 2.433, 0, 1, 2.5, 0, 2.567, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, -16, 1, 0.111, -16, 0.222, -2, 0.333, -2, 1, 0.444, -2, 0.556, -10.235, 0.667, -12, 1, 0.889, -15.531, 1.111, -16, 1.333, -16, 1, 1.444, -16, 1.556, -2, 1.667, -2, 1, 1.778, -2, 1.889, -10.235, 2, -12, 1, 2.211, -15.354, 2.422, -15.945, 2.633, -15.996]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, -14, 1, 0.111, -7.17, 0.222, 9, 0.333, 9, 1, 0.444, 9, 0.556, 7.088, 0.667, 2, 1, 0.889, -8.175, 1.111, -14, 1.333, -14, 1, 1.444, -14, 1.556, 9, 1.667, 9, 1, 1.778, 9, 1.889, 7.088, 2, 2, 1, 2.211, -7.666, 2.422, -13.407, 2.633, -13.956]}, {"Target": "Parameter", "Id": "ParamLegR2X", "Segments": [0, 0, 1, 0.111, 0, 0.222, -15, 0.333, -15, 1, 0.444, -15, 0.556, 0, 0.667, 0, 1, 0.778, 0, 0.889, -6, 1, -6, 1, 1.111, -6, 1.222, 0, 1.333, 0, 1, 1.444, 0, 1.556, -15, 1.667, -15, 1, 1.778, -15, 1.889, 0, 2, 0, 1, 2.111, 0, 2.222, -6, 2.333, -6, 1, 2.433, -6, 2.533, -1.14, 2.633, -0.168]}, {"Target": "Parameter", "Id": "ParamLegR2", "Segments": [0, 3, 1, 0.111, 3, 0.222, 12, 0.333, 12, 1, 0.444, 12, 0.556, -30, 0.667, -30, 1, 0.889, -30, 1.111, -13.983, 1.333, 3, 1, 1.444, 11.492, 1.556, 12, 1.667, 12, 1, 1.778, 12, 1.889, -30, 2, -30, 1, 2.211, -30, 2.422, -0.217, 2.633, 2.761]}, {"Target": "Parameter", "Id": "ParamLegR3X", "Segments": [0, 1, 1, 0.111, 1, 0.222, 8, 0.333, 8, 1, 0.444, 8, 0.556, 6.462, 0.667, 1, 1, 0.778, -4.462, 0.889, -9, 1, -9, 1, 1.111, -9, 1.222, -4.462, 1.333, 1, 1, 1.444, 6.462, 1.556, 8, 1.667, 8, 1, 1.778, 8, 1.889, 6.462, 2, 1, 1, 2.111, -4.462, 2.222, -9, 2.333, -9, 1, 2.433, -9, 2.533, -0.9, 2.633, 0.72]}, {"Target": "Parameter", "Id": "ParamLegR3Z", "Segments": [0, 30, 1, 0.111, 30, 0.222, -5, 0.333, -5, 1, 0.444, -5, 0.556, 10.163, 0.667, 16, 1, 0.889, 27.674, 1.111, 30, 1.333, 30, 1, 1.444, 30, 1.556, -5, 1.667, -5, 1, 1.778, -5, 1.889, 10.163, 2, 16, 1, 2.211, 27.09, 2.422, 29.744, 2.633, 29.982]}, {"Target": "Parameter", "Id": "ParamAllX", "Segments": [0, 0, 1, 0.122, 0.627, 0.244, 1, 0.367, 1, 1, 0.478, 1, 0.589, 0.667, 0.7, 0, 1, 0.811, -0.667, 0.922, -1, 1.033, -1, 1, 1.133, -1, 1.233, 0, 1.333, 0, 1, 1.456, 0, 1.578, 1, 1.7, 1, 1, 1.811, 1, 1.922, 0.667, 2.033, 0, 1, 2.144, -0.667, 2.256, -1, 2.367, -1, 1, 2.456, -1, 2.544, -0.21, 2.633, -0.034]}, {"Target": "Parameter", "Id": "ParamAllY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 2, 0.333, 2, 1, 0.444, 2, 0.556, 0, 0.667, 0, 1, 0.778, 0, 0.889, 2, 1, 2, 1, 1.111, 2, 1.222, 0, 1.333, 0, 1, 1.444, 0, 1.556, 2, 1.667, 2, 1, 1.778, 2, 1.889, 0, 2, 0, 1, 2.111, 0, 2.222, 2, 2.333, 2, 1, 2.433, 2, 2.533, 0.38, 2.633, 0.056]}, {"Target": "Parameter", "Id": "ParamAllRotate", "Segments": [0, -2, 1, 0.111, -1.897, 0.222, 1, 0.333, 1, 1, 0.444, 1, 0.556, 0.444, 0.667, 0, 1, 0.778, -0.444, 0.889, -0.556, 1, -1, 1, 1.111, -1.444, 1.222, -2, 1.333, -2, 1, 1.444, -2, 1.556, 1, 1.667, 1, 1, 1.778, 1, 1.889, 0.444, 2, 0, 1, 2.111, -0.444, 2.222, -0.556, 2.333, -1, 1, 2.433, -1.4, 2.533, -1.89, 2.633, -1.984]}]}