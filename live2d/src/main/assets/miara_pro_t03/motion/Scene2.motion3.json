{"Version": 3, "Meta": {"Duration": 5.167, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 68, "TotalSegmentCount": 303, "TotalPointCount": 855, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamMoveX", "Segments": [0, -28.2, 1, 0.3, -28.2, 0.6, -46.431, 0.9, -46.431, 1, 1.211, -46.431, 1.522, -1.283, 1.833, 57.2, 1, 2.022, 92.707, 2.211, 100, 2.4, 100, 0, 5.167, 100]}, {"Target": "Parameter", "Id": "ParamMoveY", "Segments": [0, 67.6, 1, 0.3, 67.6, 0.6, 55.803, 0.9, 26.903, 1, 1.022, 15.129, 1.144, 5.749, 1.267, 5.749, 1, 1.456, 5.749, 1.644, 13.68, 1.833, 43.834, 1, 2.022, 73.988, 2.211, 100, 2.4, 100, 0, 5.167, 100]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, 3, 1, 0.3, 3, 0.6, -3, 0.9, -3, 1, 1.167, -3, 1.433, 3, 1.7, 3, 0, 5.167, 3]}, {"Target": "Parameter", "Id": "ParamScaling", "Segments": [0, -10, 1, 0.467, -10, 0.933, -7.923, 1.4, 0, 1, 1.567, 2.83, 1.733, 10, 1.9, 10, 0, 5.167, 10]}, {"Target": "Parameter", "Id": "ParamEffect1", "Segments": [0, 0, 1, 0.067, 0, 0.133, 4, 0.2, 4, 1, 0.278, 4, 0.356, -1, 0.433, -1, 1, 0.5, -1, 0.567, 3, 0.633, 3, 1, 0.711, 3, 0.789, -2, 0.867, -2, 1, 0.933, -2, 1, 3, 1.067, 3, 1, 1.156, 3, 1.244, 0, 1.333, 0, 1, 1.4, 0, 1.467, 4, 1.533, 4, 1, 1.611, 4, 1.689, -1, 1.767, -1, 1, 1.833, -1, 1.9, 3, 1.967, 3, 1, 2.044, 3, 2.122, -2, 2.2, -2, 1, 2.267, -2, 2.333, 3, 2.4, 3, 1, 2.489, 3, 2.578, 0, 2.667, 0, 1, 2.733, 0, 2.8, 4, 2.867, 4, 1, 2.944, 4, 3.022, -1, 3.1, -1, 1, 3.167, -1, 3.233, 3, 3.3, 3, 1, 3.378, 3, 3.456, -2, 3.533, -2, 1, 3.6, -2, 3.667, 3, 3.733, 3, 1, 3.822, 3, 3.911, 0, 4, 0, 1, 4.067, 0, 4.133, 4, 4.2, 4, 1, 4.233, 4, 4.267, 0, 4.3, 0, 1, 4.367, 0, 4.433, 4, 4.5, 4, 1, 4.578, 4, 4.656, -1, 4.733, -1, 1, 4.8, -1, 4.867, 3, 4.933, 3, 1, 5.011, 3, 5.089, -2, 5.167, -2]}, {"Target": "Parameter", "Id": "ParamBodyAngleX2", "Segments": [0, 30, 0, 5.167, 30]}, {"Target": "Parameter", "Id": "ParamFlapping8", "Segments": [0, 0, 1, 0.111, 0, 0.222, 30, 0.333, 30, 1, 0.444, 30, 0.556, 20, 0.667, 0, 1, 0.778, -20, 0.889, -30, 1, -30, 1, 1.111, -30, 1.222, -20, 1.333, 0, 1, 1.444, 20, 1.556, 30, 1.667, 30, 1, 1.778, 30, 1.889, 20, 2, 0, 1, 2.111, -20, 2.222, -30, 2.333, -30, 1, 2.444, -30, 2.556, -20, 2.667, 0, 1, 2.778, 20, 2.889, 30, 3, 30, 1, 3.111, 30, 3.222, 20, 3.333, 0, 1, 3.444, -20, 3.556, -30, 3.667, -30, 1, 3.778, -30, 3.889, -20, 4, 0, 1, 4.111, 20, 4.222, 30, 4.333, 30, 1, 4.444, 30, 4.556, 20, 4.667, 0, 1, 4.778, -20, 4.889, -30, 5, -30, 0, 5.167, -30]}, {"Target": "Parameter", "Id": "ParamFlapping7", "Segments": [0, 30, 1, 0.111, 30, 0.222, 20, 0.333, 0, 1, 0.444, -20, 0.556, -30, 0.667, -30, 1, 0.778, -30, 0.889, -20, 1, 0, 1, 1.111, 20, 1.222, 30, 1.333, 30, 1, 1.444, 30, 1.556, 20, 1.667, 0, 1, 1.778, -20, 1.889, -30, 2, -30, 1, 2.111, -30, 2.222, -20, 2.333, 0, 1, 2.444, 20, 2.556, 30, 2.667, 30, 1, 2.778, 30, 2.889, 20, 3, 0, 1, 3.111, -20, 3.222, -30, 3.333, -30, 1, 3.444, -30, 3.556, -20, 3.667, 0, 1, 3.778, 20, 3.889, 30, 4, 30, 1, 4.111, 30, 4.222, 20, 4.333, 0, 1, 4.444, -20, 4.556, -30, 4.667, -30, 1, 4.778, -30, 4.889, 0, 5, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamFlapping4", "Segments": [0, 0, 1, 0.111, 0, 0.222, 30, 0.333, 30, 1, 0.444, 30, 0.556, 20, 0.667, 0, 1, 0.778, -20, 0.889, -30, 1, -30, 1, 1.111, -30, 1.222, -20, 1.333, 0, 1, 1.444, 20, 1.556, 30, 1.667, 30, 1, 1.778, 30, 1.889, 20, 2, 0, 1, 2.111, -20, 2.222, -30, 2.333, -30, 1, 2.444, -30, 2.556, -20, 2.667, 0, 1, 2.778, 20, 2.889, 30, 3, 30, 1, 3.111, 30, 3.222, 20, 3.333, 0, 1, 3.444, -20, 3.556, -30, 3.667, -30, 1, 3.778, -30, 3.889, -20, 4, 0, 1, 4.111, 20, 4.222, 30, 4.333, 30, 1, 4.444, 30, 4.556, 20, 4.667, 0, 1, 4.778, -20, 4.889, -30, 5, -30, 0, 5.167, -30]}, {"Target": "Parameter", "Id": "ParamFlapping3", "Segments": [0, 30, 1, 0.111, 30, 0.222, 20, 0.333, 0, 1, 0.444, -20, 0.556, -30, 0.667, -30, 1, 0.778, -30, 0.889, -20, 1, 0, 1, 1.111, 20, 1.222, 30, 1.333, 30, 1, 1.444, 30, 1.556, 20, 1.667, 0, 1, 1.778, -20, 1.889, -30, 2, -30, 1, 2.111, -30, 2.222, -20, 2.333, 0, 1, 2.444, 20, 2.556, 30, 2.667, 30, 1, 2.778, 30, 2.889, 20, 3, 0, 1, 3.111, -20, 3.222, -30, 3.333, -30, 1, 3.444, -30, 3.556, -20, 3.667, 0, 1, 3.778, 20, 3.889, 30, 4, 30, 1, 4.111, 30, 4.222, 20, 4.333, 0, 1, 4.444, -20, 4.556, -30, 4.667, -30, 1, 4.778, -30, 4.889, 0, 5, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLight1", "Segments": [0, 0, 0, 5.167, 10]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLight2", "Segments": [0, 0, 0, 5.167, 10]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLight3", "Segments": [0, 0, 0, 5.167, 10]}, {"Target": "Parameter", "Id": "ParamWaterSurface1", "Segments": [0, 0, 1, 0.711, 0, 1.422, 10, 2.133, 10, 1, 3.144, 10, 4.156, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamWaterSurface2", "Segments": [0, 0, 1, 0.711, 0, 1.422, 10, 2.133, 10, 1, 3.144, 10, 4.156, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLower1", "Segments": [0, 0, 1, 0.711, 0, 1.422, 10, 2.133, 10, 1, 3.144, 10, 4.156, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceLower2", "Segments": [0, 0, 1, 0.711, 0, 1.422, 10, 2.133, 10, 1, 3.144, 10, 4.156, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceBack1", "Segments": [0, 0, 1, 0.711, 0, 1.422, 10, 2.133, 10, 1, 3.144, 10, 4.156, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamWaterSurfaceBack2", "Segments": [0, 0, 1, 0.711, 0, 1.422, 10, 2.133, 10, 1, 3.144, 10, 4.156, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 11, 1, 0.244, 11, 0.489, -1.128, 0.733, -21, 1, 0.778, -24.613, 0.822, -25, 0.867, -25, 1, 1.144, -25, 1.422, 23.892, 1.7, 26, 1, 2, 28.276, 2.3, 27.92, 2.6, 27.92, 1, 2.833, 27.92, 3.067, 27.92, 3.3, 27.92, 1, 3.522, 27.92, 3.744, 26.48, 3.967, 26.48, 0, 5.167, 26.48]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 2, 1, 0.289, 2, 0.578, -30, 0.867, -30, 1, 1.144, -30, 1.422, 20, 1.7, 20, 1, 2.133, 20, 2.567, 9.86, 3, 9.86, 1, 3.111, 9.86, 3.222, 9.86, 3.333, 9.86, 1, 3.544, 9.86, 3.756, 10.58, 3.967, 10.58, 0, 5.167, 10.58]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -10, 1, 0.256, -10, 0.511, -30, 0.767, -30, 1, 1.233, -30, 1.7, 6, 2.167, 6, 1, 2.444, 6, 2.722, 0.908, 3, 0, 1, 3.333, -1.09, 3.667, -1, 4, -1, 0, 5.167, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.2, 1, 0.167, -0.2, 0.333, -0.2, 0.5, -0.2, 1, 0.644, -0.2, 0.789, -0.4, 0.933, -0.4, 1, 1.033, -0.4, 1.133, -0.4, 1.233, -0.4, 1, 1.378, -0.4, 1.522, 0, 1.667, 0, 1, 1.833, 0, 2, 0, 2.167, 0, 1, 2.556, 0, 2.944, 0, 3.333, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0.3, 1, 0.167, 0.3, 0.333, 0.3, 0.5, 0.3, 1, 0.644, 0.3, 0.789, 0, 0.933, 0, 1, 1.033, 0, 1.133, 0, 1.233, 0, 1, 1.378, 0, 1.522, 0.618, 1.667, 0.8, 1, 1.833, 1.009, 2, 1, 2.167, 1, 1, 2.556, 1, 2.944, 1, 3.333, 1, 0, 5.167, 1]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.078, 1, 0.156, 1, 0.233, 1, 1, 0.367, 1, 0.5, 0, 0.633, 0, 1, 0.722, 0, 0.811, 0, 0.9, 0, 1, 1.056, 0, 1.211, 1, 1.367, 1, 0, 5.167, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.078, 1, 0.156, 1, 0.233, 1, 1, 0.367, 1, 0.5, 0, 0.633, 0, 1, 0.722, 0, 0.811, 0, 0.9, 0, 1, 1.056, 0, 1.211, 1, 1.367, 1, 0, 5.167, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.211, 0, 0.422, -0.624, 0.633, -0.624, 1, 0.722, -0.624, 0.811, -0.665, 0.9, -0.546, 1, 1.167, -0.188, 1.433, 0.4, 1.7, 0.4, 0, 5.167, 0.4]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.3, 0, 0.6, 0, 0.9, 0, 1, 1.167, 0, 1.433, 0.1, 1.7, 0.1, 0, 5.167, 0.1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 8, 1, 0.167, 8, 0.333, 1.49, 0.5, -1, 1, 0.589, -2.328, 0.678, -2, 0.767, -2, 1, 1.078, -2, 1.389, 10, 1.7, 10, 1, 1.889, 10, 2.078, 10, 2.267, 10, 1, 2.422, 10, 2.578, 10, 2.733, 10, 1, 3.1, 10, 3.467, 9, 3.833, 9, 0, 5.167, 9]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, -6, 1, 0.233, -6, 0.467, -5.872, 0.7, -5, 1, 1.056, -3.671, 1.411, -1.11, 1.767, 0, 1, 1.944, 0.555, 2.122, 0.5, 2.3, 0.5, 1, 2.8, 0.5, 3.3, 0, 3.8, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamArmL1", "Segments": [0, 19, 1, 0.2, 19, 0.4, -2, 0.6, -2, 1, 0.667, -2, 0.733, -2, 0.8, -2, 1, 1.122, -2, 1.444, 26.535, 1.767, 29, 1, 1.967, 30.53, 2.167, 30, 2.367, 30, 0, 5.167, 30]}, {"Target": "Parameter", "Id": "ParamArmL2", "Segments": [0, 6, 1, 0.244, 6, 0.489, 30, 0.733, 30, 1, 0.933, 30, 1.133, 11.857, 1.333, 0, 1, 1.544, -12.516, 1.756, -24.478, 1.967, -28, 1, 2.111, -30.41, 2.256, -30, 2.4, -30, 0, 5.167, -30]}, {"Target": "Parameter", "Id": "ParamArmL3", "Segments": [0, 0, 1, 0.256, 0, 0.511, 30, 0.767, 30, 1, 1.178, 30, 1.589, -6, 2, -6, 1, 2.144, -6, 2.289, -6, 2.433, -6, 0, 5.167, -6]}, {"Target": "Parameter", "Id": "ParamFingerL1X", "Segments": [0, 0, 1, 0.256, 0, 0.511, 23, 0.767, 23, 1, 1.178, 23, 1.589, -30, 2, -30, 1, 2.144, -30, 2.289, -30, 2.433, -30, 0, 5.167, -30]}, {"Target": "Parameter", "Id": "ParamFingerL1Z", "Segments": [0, 0, 1, 0.256, 0, 0.511, -16, 0.767, -16, 1, 1.178, -16, 1.589, 0, 2, 0, 1, 2.144, 0, 2.289, 0, 2.433, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamFingerL2", "Segments": [0, 0, 1, 0.256, 0, 0.511, -29, 0.767, -29, 1, 1.178, -29, 1.589, -14.101, 2, 15, 1, 2.144, 25.225, 2.289, 30, 2.433, 30, 1, 2.844, 30, 3.256, 20, 3.667, 20, 0, 5.167, 20]}, {"Target": "Parameter", "Id": "ParamFingerL3", "Segments": [0, 0, 1, 0.256, 0, 0.511, -19, 0.767, -19, 1, 1.178, -19, 1.589, 0.25, 2, 21, 1, 2.144, 28.29, 2.289, 28, 2.433, 28, 1, 2.844, 28, 3.256, 9, 3.667, 9, 0, 5.167, 9]}, {"Target": "Parameter", "Id": "ParamFingerL4", "Segments": [0, 0, 1, 0.256, 0, 0.511, -25, 0.767, -25, 1, 1.178, -25, 1.589, 15, 2, 15, 1, 2.144, 15, 2.289, 15, 2.433, 15, 1, 2.844, 15, 3.256, 6, 3.667, 6, 0, 5.167, 6]}, {"Target": "Parameter", "Id": "ParamArmR1", "Segments": [0, 0, 1, 0.267, 0, 0.533, -8, 0.8, -8, 1, 0.856, -8, 0.911, -8, 0.967, -8, 1, 1.256, -8, 1.544, 11.075, 1.833, 17, 1, 2.133, 23.153, 2.433, 23, 2.733, 23, 1, 3.144, 23, 3.556, 21, 3.967, 21, 0, 5.167, 21]}, {"Target": "Parameter", "Id": "ParamArmR2", "Segments": [0, 7, 1, 0.278, 7, 0.556, 13.853, 0.833, 19, 1, 0.911, 20.441, 0.989, 20, 1.067, 20, 1, 1.344, 20, 1.622, -14.456, 1.9, -17, 1, 2.167, -19.442, 2.433, -19, 2.7, -19, 1, 3.133, -19, 3.567, -18, 4, -18, 0, 5.167, -18]}, {"Target": "Parameter", "Id": "ParamArmR3", "Segments": [0, 8, 1, 0.278, 8, 0.556, 23, 0.833, 23, 1, 1.2, 23, 1.567, -2.772, 1.933, -8, 1, 2.2, -11.802, 2.467, -11, 2.733, -11, 1, 3.156, -11, 3.578, -9, 4, -9, 0, 5.167, -9]}, {"Target": "Parameter", "Id": "ParamFingerR1", "Segments": [0, 0, 1, 0.756, 0, 1.511, 16, 2.267, 16, 1, 2.844, 16, 3.422, 1, 4, 1, 0, 5.167, 1]}, {"Target": "Parameter", "Id": "ParamFingerR2", "Segments": [0, 0, 1, 0.311, 0, 0.622, -19, 0.933, -19, 1, 1.389, -19, 1.844, 30, 2.3, 30, 1, 2.867, 30, 3.433, 0, 4, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamFingerR3", "Segments": [0, 0, 1, 0.311, 0, 0.622, -30, 0.933, -30, 1, 1.4, -30, 1.867, 19, 2.333, 19, 1, 2.889, 19, 3.444, 5, 4, 5, 0, 5.167, 5]}, {"Target": "Parameter", "Id": "ParamFingerR4", "Segments": [0, 0, 1, 0.311, 0, 0.622, -23, 0.933, -23, 1, 1.4, -23, 1.867, 30, 2.333, 30, 1, 2.889, 30, 3.444, 16, 4, 16, 0, 5.167, 16]}, {"Target": "Parameter", "Id": "ParamLegL1X", "Segments": [0, -23, 1, 0.278, -23, 0.556, -24, 0.833, -24, 1, 1.178, -24, 1.522, 0, 1.867, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamLegL1Z", "Segments": [0, 6, 1, 0.278, 6, 0.556, 14, 0.833, 14, 1, 1.178, 14, 1.522, 0, 1.867, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamLegL2X", "Segments": [0, 11, 1, 0.622, 11, 1.244, 0, 1.867, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamLegL2Z", "Segments": [0, 23, 1, 0.3, 23, 0.6, 0, 0.9, 0, 1, 1.222, 0, 1.544, 20, 1.867, 20, 0, 5.167, 20]}, {"Target": "Parameter", "Id": "ParamLegL3X", "Segments": [0, 21, 1, 0.311, 21, 0.622, 30, 0.933, 30, 1, 1.244, 30, 1.556, 18, 1.867, 18, 0, 5.167, 18]}, {"Target": "Parameter", "Id": "ParamLegL3Z", "Segments": [0, -13, 1, 0.311, -13, 0.622, -14, 0.933, -14, 1, 1.111, -14, 1.289, 3.598, 1.467, 12, 1, 1.6, 18.302, 1.733, 18, 1.867, 18, 1, 2.244, 18, 2.622, 18, 3, 18, 0, 5.167, 18]}, {"Target": "Parameter", "Id": "ParamLegLDO", "Segments": [0, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamLegR1X", "Segments": [0, -12, 1, 0.244, -12, 0.489, -26, 0.733, -26, 1, 0.778, -26, 0.822, -26, 0.867, -26, 1, 1.2, -26, 1.533, -18, 1.867, -18, 0, 5.167, -18]}, {"Target": "Parameter", "Id": "ParamLegR1Z", "Segments": [0, 0, 1, 0.267, 0, 0.533, 2, 0.8, 2, 1, 1.156, 2, 1.511, 0, 1.867, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamLegR2X", "Segments": [0, 0, 1, 0.244, 0, 0.489, -17, 0.733, -17, 1, 0.778, -17, 0.822, -17, 0.867, -17, 1, 1.2, -17, 1.533, 0, 1.867, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamLegR2", "Segments": [0, 0, 0, 5.167, 0]}, {"Target": "Parameter", "Id": "ParamAllX", "Segments": [0, 0, 1, 0.244, 0, 0.489, -4.02, 0.733, -4.02, 1, 0.778, -4.02, 0.822, -4.02, 0.867, -4.02, 1, 1.2, -4.02, 1.533, 4.5, 1.867, 4.5, 1, 2.244, 4.5, 2.622, 3.3, 3, 3.3, 0, 5.167, 3.3]}, {"Target": "Parameter", "Id": "ParamAllY", "Segments": [0, 0, 1, 0.267, 0, 0.533, -1.3, 0.8, -1.3, 1, 1.156, -1.3, 1.511, -0.3, 1.867, -0.3, 0, 5.167, -0.3]}, {"Target": "Parameter", "Id": "ParamAllRotate", "Segments": [0, 0, 1, 0.244, 0, 0.489, -2, 0.733, -2, 1, 0.778, -2, 0.822, -2, 0.867, -2, 1, 1.2, -2, 1.533, 6, 1.867, 6, 1, 2.244, 6, 2.622, 5, 3, 5, 0, 5.167, 5]}]}