package com.solvibe.live2d

import android.graphics.BitmapFactory
import android.opengl.GLES20
import android.opengl.GLUtils
import com.live2d.sdk.cubism.framework.CubismFramework
import com.solvibe.live2d.LAppDelegate.Companion.instance
import java.io.IOException
import java.io.InputStream

/**
 * 纹理管理类
 */
class LAppTextureManager {
    private val textures: MutableList<TextureInfo> = ArrayList()

    /**
     * 纹理信息数据类
     */
    class TextureInfo {
        @JvmField
        var id: Int = 0 // 纹理ID

        @JvmField
        var width: Int = 0 // 宽度

        @JvmField
        var height: Int = 0 // 高度
        var filePath: String? = null // 文件路径
    }

    /**
     * 从PNG文件创建纹理
     * @param filePath 文件路径
     * @return 纹理信息
     */
    fun createTextureFromPngFile(filePath: String): TextureInfo {
        // 检查是否已加载
        for (textureInfo in textures) {
            if (textureInfo.filePath == filePath) {
                return textureInfo
            }
        }
        // 从assets文件夹创建位图
        val assetManager = instance.context.assets
        var stream: InputStream? = null
        try {
            stream = assetManager.open(filePath)
        } catch (e: IOException) {
            e.printStackTrace()
        }
        val bitmap = BitmapFactory.decodeStream(stream)
        // 激活纹理0
        GLES20.glActiveTexture(GLES20.GL_TEXTURE0)
        // 生成OpenGL纹理
        val textureId = IntArray(1)
        GLES20.glGenTextures(1, textureId, 0)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId[0])
        // 将2D图像分配给纹理
        GLUtils.texImage2D(GLES20.GL_TEXTURE_2D, 0, bitmap, 0)
        // 生成mipmap
        GLES20.glGenerateMipmap(GLES20.GL_TEXTURE_2D)
        // 缩小时的插值设置
        GLES20.glTexParameteri(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_MIN_FILTER,
            GLES20.GL_LINEAR_MIPMAP_LINEAR
        )
        // 放大时的插值设置
        GLES20.glTexParameteri(GLES20.GL_TEXTURE_2D, GLES20.GL_TEXTURE_MAG_FILTER, GLES20.GL_LINEAR)
        val textureInfo = TextureInfo()
        textureInfo.filePath = filePath
        textureInfo.width = bitmap!!.width
        textureInfo.height = bitmap.height
        textureInfo.id = textureId[0]
        textures.add(textureInfo)
        // 释放bitmap
        bitmap.recycle()
        if (LAppDefine.DEBUG_LOG_ENABLE) {
            CubismFramework.coreLogFunction("Create texture: $filePath")
        }
        return textureInfo
    }

    /**
     * 从Bitmap创建纹理
     * @param bitmap 位图对象
     * @return 纹理信息
     */
    fun createTextureFromBitmap(bitmap: android.graphics.Bitmap): TextureInfo {
        GLES20.glActiveTexture(GLES20.GL_TEXTURE0)
        val textureId = IntArray(1)
        GLES20.glGenTextures(1, textureId, 0)
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId[0])
        GLUtils.texImage2D(GLES20.GL_TEXTURE_2D, 0, bitmap, 0)
        GLES20.glGenerateMipmap(GLES20.GL_TEXTURE_2D)
        GLES20.glTexParameteri(
            GLES20.GL_TEXTURE_2D,
            GLES20.GL_TEXTURE_MIN_FILTER,
            GLES20.GL_LINEAR_MIPMAP_LINEAR
        )
        GLES20.glTexParameteri(GLES20.GL_TEXTURE_2D, GLES20.GL_TEXTURE_MAG_FILTER, GLES20.GL_LINEAR)
        val textureInfo = TextureInfo()
        textureInfo.filePath = "bitmap_${System.currentTimeMillis()}"
        textureInfo.width = bitmap.width
        textureInfo.height = bitmap.height
        textureInfo.id = textureId[0]
        textures.add(textureInfo)
        return textureInfo
    }

    /**
     * 根据纹理ID获取纹理信息
     * @param textureId 纹理ID
     * @return 纹理信息
     */
    fun getTextureInfo(textureId: Int): TextureInfo? {
        return textures.find { it.id == textureId }
    }
}
