package com.solvibe.live2d

import com.live2d.sdk.cubism.framework.math.CubismMatrix44
import com.live2d.sdk.cubism.framework.motion.ACubismMotion
import com.live2d.sdk.cubism.framework.motion.IBeganMotionCallback
import com.live2d.sdk.cubism.framework.motion.IFinishedMotionCallback
import com.solvibe.live2d.LAppDefine.DEBUG_LOG_ENABLE
import com.solvibe.live2d.LAppDefine.HitAreaName
import com.solvibe.live2d.LAppDefine.MotionGroup
import com.solvibe.live2d.LAppDefine.USE_MODEL_RENDER_TARGET
import com.solvibe.live2d.LAppDefine.USE_RENDER_TARGET
import com.solvibe.live2d.LAppView.RenderingTarget
import java.io.IOException

/**
 * Live2D模型管理器
 * 负责模型的生成、销毁、触摸事件处理和模型切换
 */
class LAppLive2DManager private constructor() {
    // 复用的矩阵对象，避免每帧创建
    private val reusableMatrix = CubismMatrix44.create()
    private val models: MutableList<LAppModel> = ArrayList()

    /** 显示场景的索引值 */
    var currentModel: Int = 0
        private set

    /** 模型目录名 */
    private val modelDir: MutableList<String> = ArrayList()

    /** onUpdate方法中使用的缓存变量 */
    private val viewMatrix: CubismMatrix44 = CubismMatrix44.create()
    private val projection = CubismMatrix44.create()

    init {
        setUpModel()
        changeScene(0)
    }

    /**
     * 释放当前场景中的所有模型
     */
    fun releaseAllModel() {
        for (model in models) {
            model.deleteModel()
        }
        models.clear()
    }

    /**
     * 设置assets文件夹中的模型文件夹名
     */
    fun setUpModel() {
        // 遍历assets文件夹中的所有文件夹名，定义存在模型的文件夹
        // 如果文件夹存在但找不到同名的.model3.json文件，则不包含在列表中
        modelDir.clear()
        val assets = LAppDelegate.instance.context.resources.assets
        try {
            val root = assets.list("")
            for (subdir in root!!) {
                val files = assets.list(subdir)
                val target = "$subdir.model3.json"
                // 搜索是否存在与文件夹同名的.model3.json
                for (file in files!!) {
                    if (file == target) {
                        modelDir.add(subdir)
                        break
                    }
                }
            }
            modelDir.sort()
        } catch (ex: IOException) {
            throw IllegalStateException(ex)
        }
    }

    /**
     * 模型更新和绘制处理
     */
    fun onUpdate() {
        val width = LAppDelegate.instance.windowWidth
        val height = LAppDelegate.instance.windowHeight
        for (i in models.indices) {
            val model = models[i]
            if (model.getModel() == null) {
                LAppPal.printLog("Failed to model.getModel().")
                continue
            }
            projection.loadIdentity()
            // 应用模型缩放
            applyModelScaling(model, width, height)

            // 应用视图矩阵变换
            viewMatrix.multiplyByMatrix(projection)
            // 模型绘制前处理
            LAppDelegate.instance.view!!.preModelDraw(model)
            model.update()
            // 应用垂直偏移并绘制
            drawModelWithOffset(model, height)
            // 模型绘制后处理
            LAppDelegate.instance.view!!.postModelDraw(model)
        }
    }

    /**
     * 屏幕拖拽处理
     * @param x 屏幕x坐标
     * @param y 屏幕y坐标
     */
    fun onDrag(x: Float, y: Float) {
        for (i in models.indices) {
            val model = getModel(i)
            model?.setDragging(x, y)
        }
    }

    /**
     * 屏幕点击处理
     * @param x 屏幕x坐标
     * @param y 屏幕y坐标
     */
    fun onTap(x: Float, y: Float) {
        if (DEBUG_LOG_ENABLE) {
            LAppPal.printLog("tap point: x: $x, y: $y")
        }
        for (i in models.indices) {
            val model = models[i]
            // 点击头部时随机播放表情
            if (model.hitTest(HitAreaName.HEAD.id, x, y)) {
                if (DEBUG_LOG_ENABLE) {
                    LAppPal.printLog("hit area: " + HitAreaName.HEAD.id)
                }
                model.setRandomExpression()
            } else if (model.hitTest(HitAreaName.BODY.id, x, y)) {
                if (DEBUG_LOG_ENABLE) {
                    LAppPal.printLog("hit area: " + HitAreaName.BODY.id)
                }
                model.startRandomMotion(
                    MotionGroup.TAP_BODY.id,
                    LAppDefine.Priority.NORMAL.priority,
                    finishedMotion,
                    beganMotion
                )
            }
        }
    }

    /**
     * 切换到下一个场景
     * 示例应用中进行模型集的切换
     */
    fun nextScene() {
        val number = (currentModel + 1) % modelDir.size
        changeScene(number)
    }

    /**
     * 切换场景
     * @param index 要切换的场景索引
     */
    fun changeScene(index: Int) {
        currentModel = index
        if (DEBUG_LOG_ENABLE) {
            LAppPal.printLog("model index: $currentModel")
        }
        val modelDirName = modelDir[index]
        val modelPath = LAppDefine.ResourcePath.ROOT.path + modelDirName + "/"
        val modelJsonName = "$modelDirName.model3.json"
        releaseAllModel()
        models.add(LAppModel())
        models[0].loadAssets(modelPath, modelJsonName)
        /*
         * 模型半透明显示示例
         * 当定义了USE_RENDER_TARGET、USE_MODEL_RENDER_TARGET时
         * 将模型绘制到其他渲染目标，并将绘制结果作为纹理贴到其他精灵上
         */
        val useRenderingTarget = when {
            USE_RENDER_TARGET -> {
                // 绘制到LAppView持有的目标时选择此项
                RenderingTarget.VIEW_FRAME_BUFFER
            }

            USE_MODEL_RENDER_TARGET -> {
                // 绘制到各LAppModel持有的目标时选择此项
                RenderingTarget.MODEL_FRAME_BUFFER
            }

            else -> {
                // 渲染到默认主帧缓冲区（通常）
                RenderingTarget.NONE
            }
        }
        if (USE_RENDER_TARGET || USE_MODEL_RENDER_TARGET) {
            // 作为为模型单独添加α的示例，再创建一个模型并稍微偏移位置
            models.add(LAppModel())
            models[1].loadAssets(modelPath, modelJsonName)
            models[1].getModelMatrix().translateX(0.2f)
        }
        // 切换渲染目标
        LAppDelegate.instance.view!!.switchRenderingTarget(useRenderingTarget)
        // 选择其他渲染目标时的背景清除颜色
        val clearColor = floatArrayOf(0.0f, 0.0f, 0.0f)
        LAppDelegate.instance.view!!.setRenderingTargetClearColor(
            clearColor[0],
            clearColor[1],
            clearColor[2]
        )
    }

    /**
     * 返回当前场景中持有的模型
     * @param number 模型列表的索引值
     * @return 返回模型实例，索引值超出范围时返回null
     */
    fun getModel(number: Int): LAppModel? {
        if (number < models.size) {
            return models[number]
        }
        return null
    }

    /** 获取第一个模型 */
    fun getModel(): LAppModel? {
        return getModel(0)
    }

    /** 获取模型数量 */
    val modelNum: Int
        get() {
            return models.size
        }

    /** 动作开始时的回调函数 */
    private class BeganMotion : IBeganMotionCallback {
        override fun execute(motion: ACubismMotion?) {
            LAppPal.printLog("Motion Began")
        }
    }

    /** 动作结束时的回调函数 */
    private class FinishedMotion : IFinishedMotionCallback {
        override fun execute(motion: ACubismMotion?) {
            LAppPal.printLog("Motion Finished")
        }
    }

    /**
     * 应用模型缩放
     */
    private fun applyModelScaling(model: LAppModel, width: Int, height: Int) {
        val customWidth = LAppDelegate.instance.getCustomModelWidth()
        val customHeight = LAppDelegate.instance.getCustomModelHeight()
        val isAutoWidth = LAppDelegate.instance.isAutoWidth()

        when {
            customHeight > 0 && isAutoWidth -> {
                // 只设置高度，保持宽高比
                applyHeightOnlyScaling(model, customHeight, width, height)
            }

            customWidth > 0 && customHeight > 0 -> {
                // 设置固定宽高
                applyFixedSizeScaling(model, customWidth, customHeight, width, height)
            }

            else -> {
                // 使用默认缩放
                applyDefaultScaling(model, width, height)
            }
        }
    }

    /** 应用仅高度缩放（保持宽高比） */
    private fun applyHeightOnlyScaling(
        model: LAppModel,
        customHeight: Int,
        width: Int,
        height: Int
    ) {
        val scale = customHeight.toFloat() / height.toFloat()
        if (model.getModel().canvasWidth > 1.0f && width < height) {
            model.getModelMatrix().setWidth(2.0f * scale)
            projection.scale(scale, width.toFloat() / height.toFloat() * scale)
        } else {
            projection.scale(height.toFloat() / width.toFloat() * scale, scale)
        }
    }

    /** 应用固定大小缩放 */
    private fun applyFixedSizeScaling(
        model: LAppModel,
        customWidth: Int,
        customHeight: Int,
        width: Int,
        height: Int
    ) {
        val scaleX = customWidth.toFloat() / width.toFloat()
        val scaleY = customHeight.toFloat() / height.toFloat()
        model.getModelMatrix().setWidth(2.0f * scaleX)
        projection.scale(scaleX, scaleY)
    }

    /** 应用默认缩放 */
    private fun applyDefaultScaling(model: LAppModel, width: Int, height: Int) {
        if (model.getModel().canvasWidth > 1.0f && width < height) {
            model.getModelMatrix().setWidth(2.0f)
            projection.scale(1.0f, width.toFloat() / height.toFloat())
        } else {
            projection.scale(height.toFloat() / width.toFloat(), 1.0f)
        }
    }

    /** 应用垂直偏移并绘制模型 */
    private fun drawModelWithOffset(model: LAppModel, height: Int) {
        // 复用矩阵对象，避免每帧创建
        reusableMatrix.loadIdentity()
        reusableMatrix.multiplyByMatrix(projection)
        val verticalOffsetPixels = LAppDelegate.instance.getModelVerticalOffsetPixels()
        if (verticalOffsetPixels != 0.0f) {
            // 将像素偏移转换为OpenGL坐标系偏移
            val normalizedOffset = (verticalOffsetPixels * 2.0f) / height.toFloat()
            reusableMatrix.translateRelative(0.0f, normalizedOffset)
        }
        model.draw(reusableMatrix)
    }

    companion object {
        /** 单例实例 */
        private var s_instance: LAppLive2DManager? = null
        private val beganMotion = BeganMotion()
        private val finishedMotion = FinishedMotion()

        /** 获取单例实例 */
        @JvmStatic
        val instance: LAppLive2DManager
            get() {
                if (s_instance == null) {
                    s_instance = LAppLive2DManager()
                }
                return s_instance!!
            }

        /** 释放单例实例 */
        fun releaseInstance() {
            s_instance = null
        }
    }
}
