package com.solvibe.live2d

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.BitmapFactory
import android.opengl.GLES20
import com.live2d.sdk.cubism.core.ICubismLogger
import com.live2d.sdk.cubism.framework.CubismFramework
import com.solvibe.utils.base.BaseApplication

/**
 * Live2D应用程序委托类
 * 管理Live2D的生命周期和全局状态
 */
class LAppDelegate private constructor() {
    private val cubismOption = CubismFramework.Option()

    /** 纹理管理器 */
    var textureManager: LAppTextureManager? = null
        private set

    /** 视图 */
    var view: LAppView? = null
        private set

    /** 窗口宽度 */
    var windowWidth: Int = 0
        private set

    /** 窗口高度 */
    var windowHeight: Int = 0
        private set

    /** 模型场景索引 */
    private var currentModel = 0

    /** 是否点击中 */
    private var isCaptured = false

    /** 鼠标X坐标 */
    private var mouseX = 0f

    /** 鼠标Y坐标 */
    private var mouseY = 0f

    /** 背景图片纹理ID */
    private var backgroundTextureId: Int = 0

    /**
     * 背景图片Sprite
     */
    private var backgroundSprite: LAppSprite? = null

    /**
     * 待设置的背景资源ID
     */
    private var pendingBackgroundResourceId: Int = 0
    private var pendingBackgroundContext: Context? = null

    /**
     * 保存的背景资源ID，用于OpenGL上下文重建时恢复
     */
    private var savedBackgroundResourceId: Int = 0
    private var savedBackgroundContext: Context? = null

    /**
     * 自定义模型大小（像素）
     */
    private var customModelWidth: Int = 0
    private var customModelHeight: Int = 0
    private var isAutoWidth: Boolean = false // 是否自动计算宽度

    /**
     * 模型垂直偏移（像素）
     */
    private var modelVerticalOffsetPixels: Float = 0f // 默认无偏移

    init {
        LAppPal.printLog("init")
        // Set up Cubism SDK framework.
        cubismOption.logFunction = ICubismLogger {
            LAppPal.printLog(it)
        }
        cubismOption.loggingLevel = LAppDefine.cubismLoggingLevel

        CubismFramework.cleanUp()
        CubismFramework.startUp(cubismOption)
    }

    companion object {
        @JvmStatic
        val instance: LAppDelegate
            get() {
                if (s_instance == null) {
                    s_instance = LAppDelegate()
                }
                return s_instance!!
            }

        fun releaseInstance() {
            if (s_instance != null) {
                LAppPal.printLog("release LAppDelegate Instance")
                s_instance = null
            }
        }

        @SuppressLint("StaticFieldLeak")
        private var s_instance: LAppDelegate? = null
    }

    val context = BaseApplication.getInstance()

    fun init() {
        if (textureManager != null) {
            return
        }
        textureManager = LAppTextureManager()
        view = LAppView()

        LAppPal.updateTime()
    }

    fun onPause() {
        currentModel = LAppLive2DManager.instance.currentModel
    }

    fun onStop() {
        // 清理当前的背景纹理，但保留保存的背景信息用于恢复
        backgroundTextureId = 0
        backgroundSprite = null

        if (view != null) {
            view!!.close()
        }
        textureManager = null

        LAppLive2DManager.releaseInstance()
        CubismFramework.dispose()
    }

    fun onDestroy() {
        releaseInstance()
    }

    fun onSurfaceCreated() {
        // 纹理采样设置
        GLES20.glClearColor(0.0f, 0.0f, 0.0f, 0.0f)
        GLES20.glTexParameteri(GLES20.GL_TEXTURE_2D, GLES20.GL_TEXTURE_MAG_FILTER, GLES20.GL_LINEAR)
        GLES20.glTexParameteri(GLES20.GL_TEXTURE_2D, GLES20.GL_TEXTURE_MIN_FILTER, GLES20.GL_LINEAR)
        // 透明度设置
        GLES20.glEnable(GLES20.GL_BLEND)
        GLES20.glBlendFunc(GLES20.GL_ONE, GLES20.GL_ONE_MINUS_SRC_ALPHA)
        // 初始化Cubism SDK框架
        CubismFramework.initialize()
        // 重置背景纹理ID，因为OpenGL上下文已重新创建
        backgroundTextureId = 0
        backgroundSprite = null
        // 如果有保存的背景，重新设置
        if (savedBackgroundResourceId != 0 && savedBackgroundContext != null) {
            pendingBackgroundResourceId = savedBackgroundResourceId
            pendingBackgroundContext = savedBackgroundContext
            LAppPal.printLog("Restoring background after surface recreated")
        }
    }

    fun onSurfaceChanged(width: Int, height: Int) {
        // 描画範囲指定
        GLES20.glViewport(0, 0, width, height)
        windowWidth = width
        windowHeight = height

        // AppViewの初期化
        view!!.initialize()
        view!!.initializeSprite()

        // load models
        if (LAppLive2DManager.instance.currentModel != currentModel) {
            LAppLive2DManager.instance.changeScene(currentModel)
        }
    }

    fun run() {
        // 時間更新
        LAppPal.updateTime()

        // 处理待设置的背景
        processPendingBackground()

        // 画面初期化
        GLES20.glClearColor(0f, 0f, 0f, 0f)
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT or GLES20.GL_DEPTH_BUFFER_BIT)
        GLES20.glClearDepthf(1.0f)

        // 先渲染背景
        renderBackground()

        // 禁用深度测试，确保Live2D模型渲染在背景之上
        GLES20.glDisable(GLES20.GL_DEPTH_TEST)

        view?.render()
    }

    fun onTouchBegan(x: Float, y: Float) {
        mouseX = x
        mouseY = y

        if (view != null) {
            isCaptured = true
            view!!.onTouchesBegan(mouseX, mouseY)
        }
    }

    fun onTouchEnd(x: Float, y: Float) {
        mouseX = x
        mouseY = y

        if (view != null) {
            isCaptured = false
            view!!.onTouchesEnded()
        }
    }

    fun onTouchMoved(x: Float, y: Float) {
        mouseX = x
        mouseY = y

        if (isCaptured && view != null) {
            view!!.onTouchesMoved(mouseX, mouseY)
        }
    }

    /**
     * 设置背景图片
     */
    fun setBackgroundImage(context: Context, resourceId: Int) {
        // 保存背景信息，用于OpenGL上下文重建时恢复
        savedBackgroundContext = context
        savedBackgroundResourceId = resourceId

        // 保存待设置的背景信息，在OpenGL线程中处理
        pendingBackgroundContext = context
        pendingBackgroundResourceId = resourceId
        LAppPal.printLog("setBackgroundImage called with resourceId: $resourceId")
    }

    private fun processPendingBackground() {
        if (pendingBackgroundResourceId != 0 && pendingBackgroundContext != null) {
            try {
                LAppPal.printLog("Processing pending background...")
                val bitmap = BitmapFactory.decodeResource(
                    pendingBackgroundContext!!.resources,
                    pendingBackgroundResourceId
                )
                if (bitmap != null) {
                    LAppPal.printLog("Bitmap loaded: ${bitmap.width}x${bitmap.height}")

                    // 清除旧纹理
                    if (backgroundTextureId != 0) {
                        GLES20.glDeleteTextures(1, intArrayOf(backgroundTextureId), 0)
                        backgroundTextureId = 0
                    }

                    // 创建新纹理
                    backgroundTextureId = textureManager?.createTextureFromBitmap(bitmap)?.id ?: 0
                    LAppPal.printLog("Background texture ID: $backgroundTextureId")

                    if (backgroundTextureId != 0) {
                        updateBackgroundSprite()
                        LAppPal.printLog("Background sprite updated")
                    }

                    bitmap.recycle()
                } else {
                    LAppPal.printLog("Failed to decode bitmap")
                }
            } catch (e: Exception) {
                LAppPal.printLog("Error processing background: ${e.message}")
            } finally {
                pendingBackgroundResourceId = 0
                pendingBackgroundContext = null
            }
        }
    }

    private fun renderBackground() {
        if (backgroundSprite != null && backgroundTextureId != 0 && view?.spriteShader != null) {
            // 使用sprite shader程序
            GLES20.glUseProgram(view!!.spriteShader!!.shaderId)

            // 设置正确的OpenGL状态
            GLES20.glDisable(GLES20.GL_DEPTH_TEST)
            GLES20.glEnable(GLES20.GL_BLEND)
            GLES20.glBlendFunc(GLES20.GL_ONE, GLES20.GL_ONE_MINUS_SRC_ALPHA)

            backgroundSprite!!.render()

            // 恢复深度测试
            GLES20.glEnable(GLES20.GL_DEPTH_TEST)
        }
    }

    private fun updateBackgroundSprite() {
        if (backgroundTextureId == 0 || view?.spriteShader == null || windowWidth == 0 || windowHeight == 0) return

        val programId = view!!.spriteShader!!.shaderId
        val textureInfo = textureManager?.getTextureInfo(backgroundTextureId) ?: return

        val textureWidth = textureInfo.width.toFloat()
        val textureHeight = textureInfo.height.toFloat()
        val viewWidth = windowWidth.toFloat()
        val viewHeight = windowHeight.toFloat()

        // CenterCrop缩放
        val scaleX = viewWidth / textureWidth
        val scaleY = viewHeight / textureHeight
        val scale = maxOf(scaleX, scaleY)

        val scaledWidth = textureWidth * scale
        val scaledHeight = textureHeight * scale
        val centerX = viewWidth * 0.5f
        val centerY = viewHeight * 0.5f

        // 重新创建sprite以确保使用正确的纹理ID
        backgroundSprite =
            LAppSprite(centerX, centerY, scaledWidth, scaledHeight, backgroundTextureId, programId)
        backgroundSprite!!.setWindowSize(windowWidth, windowHeight)
        backgroundSprite!!.setColor(1.0f, 1.0f, 1.0f, 1.0f)
    }

    /**
     * 清除背景图片
     */
    fun clearBackgroundImage() {
        // 清除当前背景
        if (backgroundTextureId != 0) {
            GLES20.glDeleteTextures(1, intArrayOf(backgroundTextureId), 0)
            backgroundTextureId = 0
        }
        backgroundSprite = null

        // 清除保存的背景信息
        savedBackgroundResourceId = 0
        savedBackgroundContext = null
        pendingBackgroundResourceId = 0
        pendingBackgroundContext = null

        LAppPal.printLog("Background cleared")
    }

    /**
     * 设置Live2D模型固定大小
     * @param w 宽度（像素）
     * @param h 高度（像素）
     */
    fun setModelSize(w: Int, h: Int) {
        customModelWidth = w
        customModelHeight = h
        isAutoWidth = false
    }

    /**
     * 设置Live2D模型高度（宽度自适应保持比例）
     * @param h 高度（像素）
     */
    fun setModelHeight(h: Int) {
        customModelWidth = 0
        customModelHeight = h
        isAutoWidth = true
    }

    // 获取器方法
    fun getCustomModelWidth(): Int = customModelWidth
    fun getCustomModelHeight(): Int = customModelHeight
    fun isAutoWidth(): Boolean = isAutoWidth
    fun getModelVerticalOffsetPixels(): Float = modelVerticalOffsetPixels

    /**
     * 设置模型垂直偏移
     * @param offsetPixels 垂直偏移像素数，正数向上，负数向下
     */
    fun setModelVerticalOffsetPixels(offsetPixels: Float) {
        modelVerticalOffsetPixels = offsetPixels
    }
}
