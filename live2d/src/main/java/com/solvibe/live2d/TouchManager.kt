package com.solvibe.live2d

import kotlin.math.abs
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.sqrt

/**
 * 触摸管理器
 */
class TouchManager {
    /** 触摸开始时的x坐标 */
    var startX: Float = 0f
        private set

    /** 触摸开始时的y坐标 */
    var startY: Float = 0f
        private set

    /** 单点触摸时的x坐标 */
    var lastX: Float = 0f
        private set

    /** 单点触摸时的y坐标 */
    var lastY: Float = 0f
        private set

    /** 双点触摸时第一个点的x坐标 */
    var lastX1: Float = 0f
        private set

    /** 双点触摸时第一个点的y坐标 */
    var lastY1: Float = 0f
        private set

    /** 双点触摸时第二个点的x坐标 */
    var lastX2: Float = 0f
        private set

    /** 双点触摸时第二个点的y坐标 */
    var lastY2: Float = 0f
        private set

    /** 两指触摸时的距离 */
    var lastTouchDistance: Float = 0f
        private set

    /** x轴移动距离 */
    var deltaX: Float = 0f
        private set

    /** y轴移动距离 */
    var deltaY: Float = 0f
        private set

    /** 缩放比例 */
    var scale: Float = 0f
        private set

    /** 是否为单点触摸 */
    var isTouchSingle: Boolean = false
        private set

    /** 是否可以滑动 */
    var isFlipAvailable: Boolean = false
        private set

    /**
     * 触摸开始事件
     * @param deviceX 触摸屏幕的x坐标
     * @param deviceY 触摸屏幕的y坐标
     */
    fun touchesBegan(deviceX: Float, deviceY: Float) {
        lastX = deviceX
        lastY = deviceY
        startX = deviceX
        startY = deviceY
        lastTouchDistance = -1.0f
        isFlipAvailable = true
        isTouchSingle = true
    }

    /**
     * 拖拽事件（单点）
     * @param deviceX 触摸屏幕的x坐标
     * @param deviceY 触摸屏幕的y坐标
     */
    fun touchesMoved(deviceX: Float, deviceY: Float) {
        lastX = deviceX
        lastY = deviceY
        lastTouchDistance = -1.0f
        isTouchSingle = true
    }

    /**
     * 拖拽事件（双点）
     * @param deviceX1 第一个触摸点的x坐标
     * @param deviceY1 第一个触摸点的y坐标
     * @param deviceX2 第二个触摸点的x坐标
     * @param deviceY2 第二个触摸点的y坐标
     */
    fun touchesMoved(deviceX1: Float, deviceY1: Float, deviceX2: Float, deviceY2: Float) {
        val distance = calculateDistance(deviceX1, deviceY1, deviceX2, deviceY2)
        val centerX = (deviceX1 + deviceX2) * 0.5f
        val centerY = (deviceY1 + deviceY2) * 0.5f
        if (lastTouchDistance > 0.0f) {
            scale = (distance / lastTouchDistance).toDouble().pow(0.75).toFloat()
            deltaX = calculateMovingAmount(deviceX1 - lastX1, deviceX2 - lastX2)
            deltaY = calculateMovingAmount(deviceY1 - lastY1, deviceY2 - lastY2)
        } else {
            scale = 1.0f
            deltaX = 0.0f
            deltaY = 0.0f
        }
        lastX = centerX
        lastY = centerY
        lastX1 = deviceX1
        lastY1 = deviceY1
        lastX2 = deviceX2
        lastY2 = deviceY2
        lastTouchDistance = distance
        isTouchSingle = false
    }

    /**
     * 计算滑动距离
     * @return 滑动距离
     */
    fun calculateGetFlickDistance(): Float {
        return calculateDistance(startX, startY, lastX, lastY)
    }

    /**
     * 计算两点间距离
     * @param x1 第一个点的x坐标
     * @param y1 第一个点的y坐标
     * @param x2 第二个点的x坐标
     * @param y2 第二个点的y坐标
     * @return 两点间距离
     */
    private fun calculateDistance(x1: Float, y1: Float, x2: Float, y2: Float): Float {
        return sqrt(((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2)).toDouble()).toFloat()
    }

    /**
     * 计算移动量
     * 不同方向时返回0，同方向时返回绝对值较小的值
     * @param v1 第一个移动量
     * @param v2 第二个移动量
     * @return 较小的移动量
     */
    private fun calculateMovingAmount(v1: Float, v2: Float): Float {
        if ((v1 > 0.0f) != (v2 > 0.0f)) {
            return 0.0f
        }
        val sign = if (v1 > 0.0f) 1.0f else -1.0f
        val absoluteValue1 = abs(v1)
        val absoluteValue2 = abs(v2)
        return sign * (min(absoluteValue1, absoluteValue2))
    }
}
