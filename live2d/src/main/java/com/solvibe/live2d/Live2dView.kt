package com.solvibe.live2d

import android.annotation.SuppressLint
import android.content.Context
import android.opengl.GLSurfaceView
import android.util.AttributeSet
import android.view.MotionEvent
import com.solvibe.utils.ext.logv
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10

/**
 * Live2D视图组件，继承自GLSurfaceView
 * 提供Live2D模型的显示、交互和控制功能
 */
class Live2dView(
    context: Context,
    attrs: AttributeSet? = null
) : GLSurfaceView(context, attrs), GLSurfaceView.Renderer {
    private val appDelegate = LAppDelegate.instance

    init {
        logv("Live2dView init")
        setEGLContextClientVersion(2) // 使用OpenGL ES 2.0
        setEGLConfigChooser(8, 8, 8, 8, 16, 0)
        holder.setFormat(android.graphics.PixelFormat.TRANSLUCENT)
        setRenderer(this)
        renderMode = RENDERMODE_CONTINUOUSLY
        @SuppressLint("ClickableViewAccessibility")
        setOnTouchListener { _, event ->
            val pointX = event.x
            val pointY = event.y
            when (event.action) {
                MotionEvent.ACTION_DOWN -> appDelegate.onTouchBegan(pointX, pointY)
                MotionEvent.ACTION_UP -> appDelegate.onTouchEnd(pointX, pointY)
                MotionEvent.ACTION_MOVE -> appDelegate.onTouchMoved(pointX, pointY)
            }
            true
        }
    }

    /** 初始化时调用（绘制上下文丢失和重新创建时） */
    override fun onSurfaceCreated(unused: GL10?, config: EGLConfig?) {
        logv("Live2dView onSurfaceCreated")
        appDelegate.init()
        appDelegate.onSurfaceCreated()
    }

    /** 主要在横竖屏切换时调用 */
    override fun onSurfaceChanged(unused: GL10?, width: Int, height: Int) {
        logv("Live2dView onSurfaceChanged")
        appDelegate.onSurfaceChanged(width, height)
    }

    /** 重复调用进行绘制 */
    override fun onDrawFrame(unused: GL10?) {
        appDelegate.run()
    }

    override fun onResume() {
        super.onResume()
        logv("Live2dView onResume")
    }

    override fun onPause() {
        super.onPause()
        logv("Live2dView onPause")
        appDelegate.onPause()
        appDelegate.onStop()
    }

    fun onDestroy() {
        appDelegate.onDestroy()
    }

    // ========== 背景设置 ==========

    /**
     * 设置背景图片
     * @param resourceId drawable资源ID
     */
    fun setBackgroundImage(resourceId: Int) {
        appDelegate.setBackgroundImage(context, resourceId)
    }

    /**
     * 清除背景图片
     */
    fun clearBackgroundImage() {
        appDelegate.clearBackgroundImage()
    }

    // ========== 模型大小设置 ==========

    /**
     * 设置模型固定大小
     * @param w 宽度（像素）
     * @param h 高度（像素）
     */
    fun setModelSize(w: Int, h: Int) {
        appDelegate.setModelSize(w, h)
    }

    /**
     * 设置模型高度（宽度自适应保持比例）
     * @param h 高度（像素）
     */
    fun setModelHeight(h: Int) {
        appDelegate.setModelHeight(h)
    }

    // ========== 模型位置设置 ==========

    /**
     * 设置模型垂直偏移
     * @param offsetPixels 垂直偏移像素数，正数向上，负数向下
     */
    fun setModelVerticalOffset(offsetPixels: Float) {
        appDelegate.setModelVerticalOffsetPixels(offsetPixels)
    }
}