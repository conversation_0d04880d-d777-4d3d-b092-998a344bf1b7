package com.solvibe.live2d

import android.opengl.GLES20
import com.live2d.sdk.cubism.framework.math.CubismMatrix44
import com.live2d.sdk.cubism.framework.math.CubismViewMatrix
import com.live2d.sdk.cubism.framework.rendering.android.CubismOffscreenSurfaceAndroid
import com.solvibe.live2d.LAppDefine.DEBUG_TOUCH_LOG_ENABLE
import com.solvibe.live2d.LAppDefine.MaxLogicalView
import com.solvibe.live2d.LAppPal.printLog
import java.lang.AutoCloseable
import kotlin.math.abs

/**
 * Live2D视图处理类
 * 管理视图矩阵、渲染目标和触摸事件
 */
class LAppView : AutoCloseable {
    /** LAppModel的渲染目标 */
    enum class RenderingTarget {
        NONE, // 默认帧缓冲区渲染
        MODEL_FRAME_BUFFER, // 各模型自有帧缓冲区渲染
        VIEW_FRAME_BUFFER // 视图帧缓冲区渲染
    }

    private val deviceToScreen: CubismMatrix44 = CubismMatrix44.create() // 设备坐标到屏幕坐标转换矩阵
    private val viewMatrix = CubismViewMatrix() // 画面显示的缩放和移动变换矩阵

    /** 渲染目标选择 */
    var renderingTarget: RenderingTarget? = RenderingTarget.NONE
        private set

    /** 渲染目标清除颜色 */
    private val clearColor = FloatArray(4)
    private val renderingBuffer = CubismOffscreenSurfaceAndroid()
    private var renderingSprite: LAppSprite? = null

    /** 模型切换标志 */
    var isChangedModel = false
    private val touchManager = TouchManager()

    /** 着色器创建委托类 */
    var spriteShader: LAppSpriteShader? = null
        private set

    init {
        clearColor[0] = 1.0f
        clearColor[1] = 1.0f
        clearColor[2] = 1.0f
        clearColor[3] = 0.0f
    }

    override fun close() {
        spriteShader!!.close()
    }

    /** 初始化视图 */
    fun initialize() {
        val width = LAppDelegate.instance.windowWidth
        val height = LAppDelegate.instance.windowHeight
        val ratio = width.toFloat() / height.toFloat()
        val left = -ratio
        val right = ratio
        val bottom = LAppDefine.LogicalView.LEFT.value
        val top = LAppDefine.LogicalView.RIGHT.value
        // 设备对应的屏幕范围：X左端、X右端、Y下端、Y上端
        viewMatrix.setScreenRect(left, right, bottom, top)
        viewMatrix.scale(LAppDefine.Scale.DEFAULT.value, LAppDefine.Scale.DEFAULT.value)
        // 初始化为单位矩阵
        deviceToScreen.loadIdentity()
        if (width > height) {
            val screenW = abs(right - left)
            deviceToScreen.scaleRelative(screenW / width, -screenW / width)
        } else {
            val screenH = abs(top - bottom)
            deviceToScreen.scaleRelative(screenH / height, -screenH / height)
        }
        deviceToScreen.translateRelative(-width * 0.5f, -height * 0.5f)
        // 显示范围设置
        viewMatrix.setMaxScale(LAppDefine.Scale.MAX.value) // 极限放大率
        viewMatrix.minScale = LAppDefine.Scale.MIN.value // 极限缩小率
        // 可显示的最大范围
        viewMatrix.setMaxScreenRect(
            MaxLogicalView.LEFT.value,
            MaxLogicalView.RIGHT.value,
            MaxLogicalView.BOTTOM.value,
            MaxLogicalView.TOP.value
        )
        spriteShader = LAppSpriteShader()
    }

    /** 初始化精灵 */
    fun initializeSprite() {
        val windowWidth = LAppDelegate.instance.windowWidth
        val windowHeight = LAppDelegate.instance.windowHeight
        val programId = spriteShader!!.shaderId
        // 覆盖整个屏幕的大小
        val x = windowWidth * 0.5f
        val y = windowHeight * 0.5f
        if (renderingSprite == null) {
            renderingSprite =
                LAppSprite(x, y, windowWidth.toFloat(), windowHeight.toFloat(), 0, programId)
        } else {
            renderingSprite!!.resize(x, y, windowWidth.toFloat(), windowHeight.toFloat())
        }
    }

    /** 渲染 */
    fun render() {
        val maxWidth = LAppDelegate.instance.windowWidth
        val maxHeight = LAppDelegate.instance.windowHeight
        if (isChangedModel) {
            isChangedModel = false
            LAppLive2DManager.instance.nextScene()
        }
        // 模型绘制
        val live2dManager = LAppLive2DManager.instance
        live2dManager.onUpdate()
        // 各模型持有的绘制目标作为纹理的情况
        if (renderingTarget == RenderingTarget.MODEL_FRAME_BUFFER && renderingSprite != null) {
            val uvVertex = floatArrayOf(
                1.0f, 1.0f,
                0.0f, 1.0f,
                0.0f, 0.0f,
                1.0f, 0.0f
            )
            for (i in 0..<live2dManager.modelNum) {
                val model = live2dManager.getModel(i)
                val alpha = if (i < 1) 1.0f else model!!.getOpacity() // 只有一方可以获取不透明度
                renderingSprite!!.setColor(1.0f * alpha, 1.0f * alpha, 1.0f * alpha, alpha)
                if (model != null) {
                    renderingSprite!!.setWindowSize(maxWidth, maxHeight)
                    renderingSprite!!.renderImmediate(
                        model.renderingBuffer.colorBuffer[0],
                        uvVertex
                    )
                }
            }
        }
    }

    /**
     * 模型绘制前调用
     * @param refModel 模型数据
     */
    fun preModelDraw(refModel: LAppModel) {
        val useTarget: CubismOffscreenSurfaceAndroid
        // 透明度设置
        GLES20.glBlendFunc(GLES20.GL_ONE, GLES20.GL_ONE_MINUS_SRC_ALPHA)
        // 向其他渲染目标绘制的情况
        if (renderingTarget != RenderingTarget.NONE) {
            // 使用的目标
            useTarget = if (renderingTarget == RenderingTarget.VIEW_FRAME_BUFFER)
                renderingBuffer
            else
                refModel.renderingBuffer
            // 绘制目标内部未创建时在此创建
            if (!useTarget.isValid) {
                val width = LAppDelegate.instance.windowWidth
                val height = LAppDelegate.instance.windowHeight
                // 模型绘制画布
                useTarget.createOffscreenSurface(width, height, null)
            }
            // 开始渲染
            useTarget.beginDraw(null)
            useTarget.clear(clearColor[0], clearColor[1], clearColor[2], clearColor[3]) // 背景清除颜色
        }
    }

    /**
     * 模型绘制后调用
     * @param refModel 模型数据
     */
    fun postModelDraw(refModel: LAppModel) {
        var useTarget: CubismOffscreenSurfaceAndroid?
        // 向其他渲染目标绘制的情况
        if (renderingTarget != RenderingTarget.NONE) {
            // 使用的目标
            useTarget = if (renderingTarget == RenderingTarget.VIEW_FRAME_BUFFER)
                renderingBuffer
            else
                refModel.renderingBuffer
            // 渲染结束
            useTarget.endDraw()
            // 使用LAppView持有的帧缓冲区时，向精灵绘制在此进行
            if (renderingTarget == RenderingTarget.VIEW_FRAME_BUFFER && renderingSprite != null) {
                val uvVertex = floatArrayOf(
                    1.0f, 1.0f,
                    0.0f, 1.0f,
                    0.0f, 0.0f,
                    1.0f, 0.0f
                )
                renderingSprite!!.setColor(
                    1.0f * getSpriteAlpha(0),
                    1.0f * getSpriteAlpha(0),
                    1.0f * getSpriteAlpha(0),
                    getSpriteAlpha(0)
                )
                val maxWidth = LAppDelegate.instance.windowWidth
                val maxHeight = LAppDelegate.instance.windowHeight
                renderingSprite!!.setWindowSize(maxWidth, maxHeight)
                renderingSprite!!.renderImmediate(useTarget.colorBuffer[0], uvVertex)
            }
        }
    }

    /**
     * 切换渲染目标
     * @param targetType 渲染目标
     */
    fun switchRenderingTarget(targetType: RenderingTarget?) {
        renderingTarget = targetType
    }

    /**
     * 触摸开始时调用
     * @param pointX 屏幕X坐标
     * @param pointY 屏幕Y坐标
     */
    fun onTouchesBegan(pointX: Float, pointY: Float) {
        touchManager.touchesBegan(pointX, pointY)
    }

    /**
     * 触摸移动时调用
     * @param pointX 屏幕X坐标
     * @param pointY 屏幕Y坐标
     */
    fun onTouchesMoved(pointX: Float, pointY: Float) {
        val viewX = transformViewX(touchManager.lastX)
        val viewY = transformViewY(touchManager.lastY)
        touchManager.touchesMoved(pointX, pointY)
        LAppLive2DManager.instance.onDrag(viewX, viewY)
    }

    /** 触摸结束时调用 */
    fun onTouchesEnded() {
        // 触摸结束
        val live2DManager = LAppLive2DManager.instance
        live2DManager.onDrag(0.0f, 0.0f)
        // 单击
        // 获取逻辑坐标变换后的坐标
        val x = deviceToScreen.transformX(touchManager.lastX)
        // 获取逻辑坐标变换后的坐标
        val y = deviceToScreen.transformY(touchManager.lastY)
        if (DEBUG_TOUCH_LOG_ENABLE) {
            printLog("Touches ended x: $x, y:$y")
        }
        live2DManager.onTap(x, y)
    }

    /**
     * X坐标转换为View坐标
     * @param deviceX 设备X坐标
     * @return ViewX坐标
     */
    fun transformViewX(deviceX: Float): Float {
        // 获取逻辑坐标变换后的坐标
        val screenX = deviceToScreen.transformX(deviceX)
        // 放大、缩小、移动后的值
        return viewMatrix.invertTransformX(screenX)
    }

    /**
     * Y坐标转换为View坐标
     * @param deviceY 设备Y坐标
     * @return ViewY坐标
     */
    fun transformViewY(deviceY: Float): Float {
        // 获取逻辑坐标变换后的坐标
        val screenY = deviceToScreen.transformY(deviceY)
        // 放大、缩小、移动后的值
        return viewMatrix.invertTransformX(screenY)
    }

    /**
     * X坐标转换为Screen坐标
     * @param deviceX 设备X坐标
     * @return ScreenX坐标
     */
    fun transformScreenX(deviceX: Float): Float {
        return deviceToScreen.transformX(deviceX)
    }

    /**
     * Y坐标转换为Screen坐标
     * @param deviceY 设备Y坐标
     * @return ScreenY坐标
     */
    fun transformScreenY(deviceY: Float): Float {
        return deviceToScreen.transformX(deviceY)
    }

    /**
     * 渲染目标切换为非默认时的背景清除颜色设置
     * @param r 红色(0.0~1.0)
     * @param g 绿色(0.0~1.0)
     * @param b 蓝色(0.0~1.0)
     */
    fun setRenderingTargetClearColor(r: Float, g: Float, b: Float) {
        clearColor[0] = r
        clearColor[1] = g
        clearColor[2] = b
    }

    /**
     * 其他渲染目标绘制模型示例中决定绘制时的α值
     * @param assign 分配值
     * @return α值
     */
    fun getSpriteAlpha(assign: Int): Float {
        // 根据assign数值适当设置差异
        var alpha = 0.4f + assign.toFloat() * 0.5f
        // 作为示例对α设置适当差异
        if (alpha > 1.0f) {
            alpha = 1.0f
        }
        if (alpha < 0.1f) {
            alpha = 0.1f
        }
        return alpha
    }
}
