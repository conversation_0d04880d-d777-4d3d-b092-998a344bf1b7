package com.solvibe.live2d

import com.live2d.sdk.cubism.framework.CubismFrameworkConfig
import com.solvibe.utils.base.BaseApplication

/**
 * Live2D应用程序常量定义
 */
object LAppDefine {
    /** MOC3文件整合性验证开关 */
    const val MOC_CONSISTENCY_VALIDATION_ENABLE: Boolean = true

    /** motion3.json文件整合性验证开关 */
    const val MOTION_CONSISTENCY_VALIDATION_ENABLE: Boolean = true

    /** 调试日志开关 */
    val DEBUG_LOG_ENABLE: Boolean = BaseApplication.showLog

    /** 触摸处理调试日志开关 */
    const val DEBUG_TOUCH_LOG_ENABLE: Boolean = true

    /** Framework日志输出级别 */
    @JvmField
    val cubismLoggingLevel: CubismFrameworkConfig.LogLevel = CubismFrameworkConfig.LogLevel.VERBOSE

    /** 预乘Alpha开关 */
    const val PREMULTIPLIED_ALPHA_ENABLE: Boolean = true

    /** 是否绘制到LAppView持有的目标 */
    const val USE_RENDER_TARGET: Boolean = false

    /** 是否绘制到每个LAppModel持有的目标 */
    const val USE_MODEL_RENDER_TARGET: Boolean = false

    /** 动作音效开关 */
    const val ENABLE_MOTION_SOUND: Boolean = false

    /** 缩放比例 */
    enum class Scale(val value: Float) {
        /** 默认缩放比例 */
        DEFAULT(1.0f),

        /** 最大缩放比例 */
        MAX(2.0f),

        /** 最小缩放比例 */
        MIN(0.8f)
    }

    /** 逻辑视图坐标系 */
    enum class LogicalView(val value: Float) {
        /** 左端 */
        LEFT(-1.0f),

        /** 右端 */
        RIGHT(1.0f),

        /** 底端 */
        BOTTOM(-1.0f),

        /** 顶端 */
        TOP(1.0f)
    }

    /** 最大逻辑视图坐标系 */
    enum class MaxLogicalView(val value: Float) {
        /** 最大左端 */
        LEFT(-2.0f),

        /** 最大右端 */
        RIGHT(2.0f),

        /** 最大底端 */
        BOTTOM(-2.0f),

        /** 最大顶端 */
        TOP(2.0f)
    }

    /** 资源路径 */
    enum class ResourcePath(val path: String) {
        /** 材质目录相对路径 */
        ROOT(""),

        /** 着色器目录相对路径 */
        SHADER_ROOT("Shaders"),

        /** 顶点着色器文件 */
        VERT_SHADER("VertSprite.vert"),

        /** 片段着色器文件 */
        FRAG_SHADER("FragSprite.frag");
    }

    /** 动作组 */
    enum class MotionGroup(val id: String) {
        /** 空闲时播放的动作ID */
        IDLE("Idle"),

        /** 点击身体时播放的动作ID */
        TAP_BODY("TapBody");
    }

    /** 碰撞检测标签 */
    enum class HitAreaName(val id: String) {
        HEAD("Head"),
        BODY("Body");
    }

    /** 动作优先级 */
    enum class Priority(val priority: Int) {
        NONE(0),
        IDLE(1),
        NORMAL(2),
        FORCE(3)
    }
}
