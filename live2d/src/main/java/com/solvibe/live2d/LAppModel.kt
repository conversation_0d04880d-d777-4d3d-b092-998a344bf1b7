package com.solvibe.live2d

import com.live2d.sdk.cubism.framework.CubismDefaultParameterId.ParameterId
import com.live2d.sdk.cubism.framework.CubismFramework
import com.live2d.sdk.cubism.framework.CubismModelSettingJson
import com.live2d.sdk.cubism.framework.ICubismModelSetting
import com.live2d.sdk.cubism.framework.effect.CubismBreath
import com.live2d.sdk.cubism.framework.effect.CubismBreath.BreathParameterData
import com.live2d.sdk.cubism.framework.effect.CubismEyeBlink
import com.live2d.sdk.cubism.framework.id.CubismId
import com.live2d.sdk.cubism.framework.math.CubismMatrix44
import com.live2d.sdk.cubism.framework.model.CubismMoc
import com.live2d.sdk.cubism.framework.model.CubismUserModel
import com.live2d.sdk.cubism.framework.motion.ACubismMotion
import com.live2d.sdk.cubism.framework.motion.CubismMotion
import com.live2d.sdk.cubism.framework.motion.IBeganMotionCallback
import com.live2d.sdk.cubism.framework.motion.IFinishedMotionCallback
import com.live2d.sdk.cubism.framework.rendering.android.CubismOffscreenSurfaceAndroid
import com.live2d.sdk.cubism.framework.rendering.android.CubismRendererAndroid
import com.live2d.sdk.cubism.framework.utils.CubismDebug
import com.solvibe.live2d.LAppDelegate.Companion.instance
import com.solvibe.utils.base.BaseApplication
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random

/**
 * Live2D模型类，继承自CubismUserModel
 */
class LAppModel : CubismUserModel() {
    /** 是否正在说话 */
    var isSay = false

    /** 模型主目录 */
    private var modelHomeDirectory: String? = null

    /** 模型设置信息 */
    private var modelSetting: ICubismModelSetting? = null


    /** 唇同步相关 */
    private var lastLipSyncTime = 0L
    private var _lastLipSyncValue = 0f

    /**
     * 加载模型资源
     * @param dir 模型目录
     * @param fileName 模型文件名
     */
    fun loadAssets(dir: String?, fileName: String) {
        if (LAppDefine.DEBUG_LOG_ENABLE) {
            LAppPal.printLog("load model setting: $fileName")
        }
        modelHomeDirectory = dir
        val filePath = modelHomeDirectory + fileName
        // 加载JSON配置
        val buffer: ByteArray = createBuffer(filePath)
        val setting: ICubismModelSetting = CubismModelSettingJson(buffer)
        // 设置模型
        setupModel(setting)
        if (model == null) {
            LAppPal.printLog("Failed to loadAssets().")
            return
        }
        // 设置渲染器
        val renderer = CubismRendererAndroid.create()
        setupRenderer(renderer)
        setupTextures()
    }

    /**
     * 删除模型
     */
    fun deleteModel() {
        delete()
    }

    /**
     * 模型更新处理，从模型参数确定绘制状态
     */
    fun update() {
        val deltaTimeSeconds = LAppPal.deltaTime
        userTimeSeconds += deltaTimeSeconds
        dragManager.update(deltaTimeSeconds)
        dragX = dragManager.x
        dragY = dragManager.y
        // 动作是否更新参数
        var isMotionUpdated = false
        // 加载上次保存的状态
        model.loadParameters()
        // 如果没有动作播放，从待机动作中随机播放
        if (motionManager.isFinished) {
            startRandomMotion(LAppDefine.MotionGroup.IDLE.id, LAppDefine.Priority.IDLE.priority)
        } else {
            // 更新动作
            isMotionUpdated = motionManager.updateMotion(model, deltaTimeSeconds)
        }
        // 保存模型状态
        model.saveParameters()
        // 不透明度
        opacity = model.modelOpacity
        // 眨眼处理（仅在主动作未更新时）
        if (!isMotionUpdated) {
            eyeBlink?.updateParameters(model, deltaTimeSeconds)
        }
        // 表情处理
        expressionManager?.updateMotion(model, deltaTimeSeconds)
        // 拖拽跟随功能
        // 拖拽调整脸部朝向
        model.addParameterValue(idParamAngleX, dragX * 30) // 添加-30到30的值
        model.addParameterValue(idParamAngleY, dragY * 30)
        model.addParameterValue(idParamAngleZ, dragX * dragY * (-30))
        // 拖拽调整身体朝向
        model.addParameterValue(idParamBodyAngleX, dragX * 10) // 添加-10到10的值
        // 拖拽调整眼部朝向
        model.addParameterValue(idParamEyeBallX, dragX) // 添加-1到1的值
        model.addParameterValue(idParamEyeBallY, dragY)
        // 呼吸功能
        breath?.updateParameters(model, deltaTimeSeconds)
        // 物理设置
        physics?.evaluate(model, deltaTimeSeconds)
        // 唇同步设置
        if (lipSync) {
            // 实时唇同步时，从系统获取音量并输入0~1范围的值
            val value = getRadomLipSyncValue()
            for (i in lipSyncIds.indices) {
                val lipSyncId = lipSyncIds[i]
                model.addParameterValue(lipSyncId, value)
            }
        }
        // 姿势设置
        pose?.updateParameters(model, deltaTimeSeconds)
        model.update()
    }

    /** 获取随机唇同步值 */
    fun getRadomLipSyncValue(): Float {
        if (!isSay) return 0f
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastLipSyncTime < 100) {
            return _lastLipSyncValue
        }
        lastLipSyncTime = currentTime
        _lastLipSyncValue = Random.nextFloat()
        return _lastLipSyncValue
    }

    /**
     * 开始播放指定的动作
     * @param group 动作组名
     * @param number 组内编号
     * @param priority 优先级
     * @param onFinishedMotionHandler 动作播放结束时的回调函数
     * @param onBeganMotionHandler 动作开始时的回调函数
     * @return 返回开始的动作识别号，无法开始时返回-1
     */
    @JvmOverloads
    fun startMotion(
        group: String?,
        number: Int,
        priority: Int,
        onFinishedMotionHandler: IFinishedMotionCallback? = null,
        onBeganMotionHandler: IBeganMotionCallback? = null
    ): Int {
        if (priority == LAppDefine.Priority.FORCE.priority) {
            motionManager.reservationPriority = priority
        } else if (!motionManager.reserveMotion(priority)) {
            if (debugMode) {
                LAppPal.printLog("Cannot start motion.")
            }
            return -1
        }
        val name = group + "_" + number
        var motion = motions[name] as CubismMotion?
        if (motion == null) {
            val fileName = modelSetting!!.getMotionFileName(group, number)
            if (fileName != "") {
                val path = modelHomeDirectory + fileName
                val buffer = createBuffer(path)
                motion = loadMotion(
                    buffer,
                    onFinishedMotionHandler,
                    onBeganMotionHandler,
                    motionConsistency
                )
                if (motion != null) {
                    val fadeInTime = modelSetting!!.getMotionFadeInTimeValue(group, number)
                    if (fadeInTime != -1.0f) {
                        motion.fadeInTime = fadeInTime
                    }
                    val fadeOutTime = modelSetting!!.getMotionFadeOutTimeValue(group, number)
                    if (fadeOutTime != -1.0f) {
                        motion.fadeOutTime = fadeOutTime
                    }
                    motion.setEffectIds(eyeBlinkIds, lipSyncIds)
                } else {
                    CubismDebug.cubismLogError("Can't start motion %s", path)
                    // 重置无法加载的动作的ReservePriority
                    motionManager.reservationPriority = LAppDefine.Priority.NONE.priority
                    return -1
                }
            }
        } else {
            motion.setBeganMotionHandler(onBeganMotionHandler)
            motion.setFinishedMotionHandler(onFinishedMotionHandler)
        }
        if (LAppDefine.ENABLE_MOTION_SOUND) {
            // 加载音频文件
            val voice = modelSetting!!.getMotionSoundFileName(group, number)
            if (voice != "") {
                val path = modelHomeDirectory + voice
                // 在别的线程播放音频
                val voicePlayer = LAppWavFileHandler(path)
                voicePlayer.start()
            }
        }
        if (debugMode) {
            LAppPal.printLog("start motion: " + group + "_" + number)
        }
        return motionManager.startMotionPriority(motion, priority)
    }

    /**
     * 开始播放随机选择的动作
     * @param group 动作组名
     * @param priority 优先级
     * @param onFinishedMotionHandler 动作播放结束时的回调函数
     * @param onBeganMotionHandler 动作开始时的回调函数
     * @return 返回开始的动作识别号，无法开始时返回-1
     */
    @JvmOverloads
    fun startRandomMotion(
        group: String?,
        priority: Int,
        onFinishedMotionHandler: IFinishedMotionCallback? = null,
        onBeganMotionHandler: IBeganMotionCallback? = null
    ): Int {
        if (modelSetting!!.getMotionCount(group) == 0) {
            return -1
        }
        LAppPal.printLog("开始执行随机待机动作")
        val number = Random.nextInt(Int.Companion.MAX_VALUE) % modelSetting!!.getMotionCount(group)
        return startMotion(group, number, priority, onFinishedMotionHandler, onBeganMotionHandler)
    }

    /** 绘制模型 */
    fun draw(matrix: CubismMatrix44) {
        if (model == null) {
            return
        }
        // 为避免缓存变量定义，使用multiply()而不是multiplyByMatrix()
        CubismMatrix44.multiply(
            modelMatrix.array,
            matrix.array,
            matrix.array
        )
        this.getRenderer<CubismRendererAndroid?>()?.mvpMatrix = matrix
        this.getRenderer<CubismRendererAndroid?>()?.drawModel()
    }

    /**
     * 碰撞检测测试
     * 从指定ID的顶点列表计算矩形，判断坐标是否在矩形范围内
     * @param hitAreaName 要测试碰撞检测的目标ID
     * @param x 判定的x坐标
     * @param y 判定的y坐标
     * @return 碰撞时返回true
     */
    fun hitTest(hitAreaName: String?, x: Float, y: Float): Boolean {
        // 透明时无碰撞检测
        if (opacity < 1) {
            return false
        }
        val count = modelSetting!!.hitAreasCount
        for (i in 0..<count) {
            if (modelSetting!!.getHitAreaName(i) == hitAreaName) {
                val drawID = modelSetting!!.getHitAreaId(i)
                return isHit(drawID, x, y)
            }
        }
        // 不存在时返回false
        return false
    }

    /**
     * 设置指定的表情动作
     * @param expressionID 表情动作ID
     */
    fun setExpression(expressionID: String?) {
        val motion = expressions[expressionID]
        if (debugMode) {
            LAppPal.printLog("expression: $expressionID")
        }
        if (motion != null) {
            expressionManager.startMotionPriority(motion, LAppDefine.Priority.FORCE.priority)
        } else {
            if (debugMode) {
                LAppPal.printLog("expression " + expressionID + "is null")
            }
        }
    }

    /**
     * 设置指定的表情动作并等待指定时间后停止
     */
    fun setExpression(expressionID: String?, time: Long) {
        BaseApplication.scope.launch {
            setExpression(expressionID)
            delay(time)
            stopAllExpressions()
        }
    }

    private var expJob: Job? = null

    fun setRandomExpression(time: Long) {
        expJob?.cancel()
        expJob = BaseApplication.scope.launch {
            setRandomExpression()
            delay(time)
            stopAllExpressions()
        }
    }

    /** 设置随机选择的表情动作 */
    fun setRandomExpression() {
        if (expressions.isEmpty()) {
            return
        }
        val number = Random.nextInt(Int.Companion.MAX_VALUE) % expressions.size
        var i = 0
        for (key in expressions.keys) {
            if (i == number) {
                setExpression(key)
                return
            }
            i++
        }
    }

    fun stopAllExpressions() {
        expressionManager.stopAllMotions()
    }

    fun stopAllMotions() {
        motionManager.stopAllMotions()
    }

    /**
     * .moc3ファイルの整合性をチェックする。
     *
     * @param mocFileName MOC3ファイル名
     * @return MOC3に整合性があるかどうか。整合性があればtrue。
     */
    fun hasMocConsistencyFromFile(mocFileName: String): Boolean {
        assert(!mocFileName.isEmpty())

        var path = mocFileName
        path = modelHomeDirectory + path

        val buffer: ByteArray = createBuffer(path)
        val consistency = CubismMoc.hasMocConsistency(buffer)

        if (!consistency) {
            CubismDebug.cubismLogInfo("Inconsistent MOC3.")
        } else {
            CubismDebug.cubismLogInfo("Consistent MOC3.")
        }

        return consistency
    }

    // model3.jsonからモデルを生成する
    private fun setupModel(setting: ICubismModelSetting?) {
        modelSetting = setting

        isUpdated = true
        isInitialized = false

        // Load Cubism Model
        run {
            val fileName = modelSetting!!.modelFileName
            if (fileName != "") {
                val path = modelHomeDirectory + fileName

                if (LAppDefine.DEBUG_LOG_ENABLE) {
                    LAppPal.printLog("create model: " + modelSetting!!.modelFileName)
                }

                val buffer: ByteArray = createBuffer(path)
                loadModel(buffer, mocConsistency)
            }
        }

        // load expression files(.exp3.json)
        run {
            if (modelSetting!!.expressionCount > 0) {
                val count = modelSetting!!.expressionCount

                for (i in 0..<count) {
                    val name = modelSetting!!.getExpressionName(i)
                    var path = modelSetting!!.getExpressionFileName(i)
                    path = modelHomeDirectory + path

                    val buffer: ByteArray = createBuffer(path)
                    val motion = loadExpression(buffer)

                    if (motion != null) {
                        expressions.put(name, motion)
                    }
                }
            }
        }

        // Physics
        run {
            val path = modelSetting!!.physicsFileName
            if (path != "") {
                val modelPath = modelHomeDirectory + path
                val buffer: ByteArray = createBuffer(modelPath)

                loadPhysics(buffer)
            }
        }

        // Pose
        run {
            val path = modelSetting!!.poseFileName
            if (path != "") {
                val modelPath = modelHomeDirectory + path
                val buffer: ByteArray = createBuffer(modelPath)
                loadPose(buffer)
            }
        }

        // Load eye blink data
        if (modelSetting!!.eyeBlinkParameterCount > 0) {
            eyeBlink = CubismEyeBlink.create(modelSetting)
        }

        // Load Breath Data
        breath = CubismBreath.create()
        val breathParameters: MutableList<BreathParameterData?> = ArrayList()

        breathParameters.add(BreathParameterData(idParamAngleX, 0.0f, 15.0f, 6.5345f, 0.5f))
        breathParameters.add(BreathParameterData(idParamAngleY, 0.0f, 8.0f, 3.5345f, 0.5f))
        breathParameters.add(BreathParameterData(idParamAngleZ, 0.0f, 10.0f, 5.5345f, 0.5f))
        breathParameters.add(BreathParameterData(idParamBodyAngleX, 0.0f, 4.0f, 15.5345f, 0.5f))
        breathParameters.add(
            BreathParameterData(
                CubismFramework.getIdManager().getId(ParameterId.BREATH.id),
                0.5f,
                0.5f,
                3.2345f,
                0.5f
            )
        )

        breath?.parameters = breathParameters

        // Load UserData
        run {
            val path = modelSetting!!.userDataFile
            if (path != "") {
                val modelPath = modelHomeDirectory + path
                val buffer: ByteArray = createBuffer(modelPath)
                loadUserData(buffer)
            }
        }


        // EyeBlinkIds
        val eyeBlinkIdCount = modelSetting!!.eyeBlinkParameterCount
        for (i in 0..<eyeBlinkIdCount) {
            eyeBlinkIds.add(modelSetting!!.getEyeBlinkParameterId(i))
        }

        // LipSyncIds
        val lipSyncIdCount = modelSetting!!.lipSyncParameterCount
        for (i in 0..<lipSyncIdCount) {
            lipSyncIds.add(modelSetting!!.getLipSyncParameterId(i))
        }

        if (modelSetting == null || modelMatrix == null) {
            LAppPal.printLog("Failed to setupModel().")
            return
        }

        // Set layout
        val layout: MutableMap<String?, Float?> = HashMap()

        // レイアウト情報が存在すればその情報からモデル行列をセットアップする
        if (modelSetting!!.getLayoutMap(layout)) {
            modelMatrix.setupFromLayout(layout)
        }

        model.saveParameters()

        // Load motions
        for (i in 0..<modelSetting!!.motionGroupCount) {
            val group = modelSetting!!.getMotionGroupName(i)
            preLoadMotionGroup(group)
        }

        motionManager.stopAllMotions()

        isUpdated = false
        isInitialized = true
    }

    /**
     * モーションデータをグループ名から一括でロードする。
     * モーションデータの名前はModelSettingから取得する。
     *
     * @param group モーションデータのグループ名
     */
    private fun preLoadMotionGroup(group: String?) {
        val count = modelSetting!!.getMotionCount(group)

        for (i in 0..<count) {
            // ex) idle_0
            val name = group + "_" + i

            val path = modelSetting!!.getMotionFileName(group, i)
            if (path != "") {
                val modelPath = modelHomeDirectory + path

                if (debugMode) {
                    LAppPal.printLog("load motion: " + path + "==>[" + group + "_" + i + "]")
                }
                val buffer = createBuffer(modelPath)

                // If a motion cannot be loaded, a process is skipped.
                val tmp = loadMotion(buffer, motionConsistency)
                if (tmp == null) {
                    continue
                }

                val fadeInTime = modelSetting!!.getMotionFadeInTimeValue(group, i)

                if (fadeInTime != -1.0f) {
                    tmp.fadeInTime = fadeInTime
                }

                val fadeOutTime = modelSetting!!.getMotionFadeOutTimeValue(group, i)

                if (fadeOutTime != -1.0f) {
                    tmp.fadeOutTime = fadeOutTime
                }

                tmp.setEffectIds(eyeBlinkIds, lipSyncIds)
                motions.put(name, tmp)
            }
        }
    }

    /**
     * OpenGLのテクスチャユニットにテクスチャをロードする
     */
    private fun setupTextures() {
        for (modelTextureNumber in 0..<modelSetting!!.textureCount) {
            // テクスチャ名が空文字だった場合はロード・バインド処理をスキップ
            if (modelSetting!!.getTextureFileName(modelTextureNumber) == "") {
                continue
            }

            // OpenGL ESのテクスチャユニットにテクスチャをロードする
            var texturePath = modelSetting!!.getTextureFileName(modelTextureNumber)
            texturePath = modelHomeDirectory + texturePath

            val texture =
                instance
                    .textureManager!!
                    .createTextureFromPngFile(texturePath)
            val glTextureNumber = texture.id

            this.getRenderer<CubismRendererAndroid?>()
                ?.bindTexture(modelTextureNumber, glTextureNumber)

            if (LAppDefine.PREMULTIPLIED_ALPHA_ENABLE) {
                this.getRenderer<CubismRendererAndroid?>()?.isPremultipliedAlpha(true)
            } else {
                this.getRenderer<CubismRendererAndroid?>()?.isPremultipliedAlpha(false)
            }
        }
    }

    fun getMotionCount(group: String): Int {
        return modelSetting?.getMotionCount(group) ?: 0
    }

    val tapBodyMotionCount: Int
        get() = getMotionCount(LAppDefine.MotionGroup.TAP_BODY.id)


    /** 时间积算值（秒） */
    private var userTimeSeconds = 0f

    /** 眨眼功能参数ID */
    private val eyeBlinkIds: MutableList<CubismId?> = ArrayList()

    /** 唇同步功能参数ID */
    private val lipSyncIds: MutableList<CubismId?> = ArrayList()

    /** 已加载的动作映射 */
    private val motions: MutableMap<String?, ACubismMotion?> = HashMap()

    /** 已加载的表情映射 */
    private val expressions: MutableMap<String?, ACubismMotion?> = HashMap()

    /** 帧缓冲区以外的绘制目标 */
    @JvmField
    val renderingBuffer: CubismOffscreenSurfaceAndroid = CubismOffscreenSurfaceAndroid()

    /** 参数ID: ParamAngleX */
    private val idParamAngleX: CubismId?

    /** 参数ID: ParamAngleY */
    private val idParamAngleY: CubismId?

    /** 参数ID: ParamAngleZ */
    private val idParamAngleZ: CubismId?

    /** 参数ID: ParamBodyAngleX */
    private val idParamBodyAngleX: CubismId?

    /** 参数ID: ParamEyeBallX */
    private val idParamEyeBallX: CubismId?

    /** 参数ID: ParamEyeBallY */
    private val idParamEyeBallY: CubismId?

    init {
        if (LAppDefine.MOC_CONSISTENCY_VALIDATION_ENABLE) {
            mocConsistency = true
        }
        if (LAppDefine.MOTION_CONSISTENCY_VALIDATION_ENABLE) {
            motionConsistency = true
        }
        if (LAppDefine.DEBUG_LOG_ENABLE) {
            debugMode = true
        }
        val idManager = CubismFramework.getIdManager()
        idParamAngleX = idManager.getId(ParameterId.ANGLE_X.id)
        idParamAngleY = idManager.getId(ParameterId.ANGLE_Y.id)
        idParamAngleZ = idManager.getId(ParameterId.ANGLE_Z.id)
        idParamBodyAngleX = idManager.getId(ParameterId.BODY_ANGLE_X.id)
        idParamEyeBallX = idManager.getId(ParameterId.EYE_BALL_X.id)
        idParamEyeBallY = idManager.getId(ParameterId.EYE_BALL_Y.id)
    }

    companion object {
        /** 创建缓冲区 */
        private fun createBuffer(path: String): ByteArray {
            if (LAppDefine.DEBUG_LOG_ENABLE) {
                LAppPal.printLog("create buffer: $path")
            }
            return LAppPal.loadFileAsBytes(path)
        }
    }
}
