android {
    flavorDimensions "channel"

    productFlavors {
        google {
            dimension "channel"
            if (buildFeatures.buildConfig) {
                buildConfigField "String", "channel", "\"google play\""
            }
        }
        samsung {
            dimension "channel"
            if (buildFeatures.buildConfig) {
                buildConfigField "String", "channel", "\"samsung\""
            }
        }
        xiaomi {
            dimension "channel"
            if (buildFeatures.buildConfig) {
                buildConfigField "String", "channel", "\"xiaomi\""
            }
        }
        huawei {
            dimension "channel"
            if (buildFeatures.buildConfig) {
                buildConfigField "String", "channel", "\"huawei\""
            }
        }
        web {
            dimension "channel"
            if (buildFeatures.buildConfig) {
                buildConfigField "String", "channel", "\"web\""
            }
        }
    }
}
