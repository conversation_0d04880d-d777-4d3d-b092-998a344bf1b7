buildscript {
    repositories {
        google()
        mavenCentral()
        maven {
            url 'https://maven.aliyun.com/repository/central'
        }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven {
            allowInsecureProtocol true
            url "http://git.imyfone.info/MFMobilePublicGroup/AndroidModule/raw/master"
        }
        maven {
            url "https://oss.sonatype.org/content/repositories/snapshots"
        }
    }
    dependencies {
        classpath libs.google.services
        classpath libs.agp
        classpath libs.kotlin.gradle.plugin
        classpath libs.kotlin.compose.plugin
        classpath libs.kotlin.serialization.plugin
        classpath libs.mfccgroup.plugin
        classpath libs.firebase.crashlytics.gradle
        classpath libs.com.google.devtools.ksp.gradle.plugin
        classpath libs.protobuf.gradle.plugin
    }
}
allprojects {
    repositories {
        maven {
            allowInsecureProtocol true
            url "http://maven.imyfone.info/repository/public/"
        }
        maven {
            allowInsecureProtocol true
            url "http://git.imyfone.info/MFMobilePublicGroup/AndroidModule/-/raw/master"
        }
        maven { url "https://jitpack.io" }
        mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven {
            url 'https://maven.aliyun.com/repository/central'
        }
        maven { url 'https://repo1.maven.org/maven2/' }
        google()
        maven {
            url 'https://api.mapbox.com/downloads/v2/releases/maven'
            authentication {
                basic(BasicAuthentication)
            }
            credentials {
                username = "mapbox"
                // Use the secret token you stored in gradle.properties as the password
                password = 'sk.eyJ1IjoibGVlcWl1dXUiLCJhIjoiY2xkd2ZuMXBnMDZ3NzNvcWpibXpnZ2lsYyJ9.tBAQyGi4UAJYD_e-uhgRSg'
            }
        }
        maven {
            url "https://oss.sonatype.org/content/repositories/snapshots"
        }
    }
}