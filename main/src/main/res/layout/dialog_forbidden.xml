<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/dp_308"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ly_sub"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_38"
        android:background="@drawable/shape_16_white"
        android:paddingBottom="@dimen/dp_24"
        app:layout_constraintTop_toTopOf="@id/iv_limit">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_62"
            android:gravity="center"
            android:lineHeight="@dimen/sp_22"
            android:maxWidth="@dimen/dp_270"
            android:text="@string/ip_not_available"
            android:textColor="@color/black"
            android:textSize="@dimen/sp_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/tv_ok"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48"
            android:layout_marginHorizontal="@dimen/dp_28"
            android:layout_marginTop="@dimen/dp_24"
            android:background="@drawable/shape_100_confirm"
            android:gravity="center"
            android:minWidth="@dimen/dp_330"
            android:text="@string/ok"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_limit"
        android:layout_width="@dimen/dp_110"
        android:layout_height="@dimen/dp_80"
        android:src="@drawable/ic_ip_limit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
