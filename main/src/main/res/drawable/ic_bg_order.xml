<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="260dp"
    android:height="260dp"
    android:viewportWidth="260"
    android:viewportHeight="260">
  <group>
    <clip-path
        android:pathData="M0,0h260v260h-260z"/>
    <path
        android:pathData="M0,0h260v260h-260z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M260,218C255.38,181.84 199.04,153.27 130.22,153.27C61.39,153.27 5.05,181.83 0.43,218H260Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="130.22"
            android:startY="218"
            android:endX="130.22"
            android:endY="153.27"
            android:type="linear">
          <item android:offset="0" android:color="#00FFFFFF"/>
          <item android:offset="0.77" android:color="#FFDCEEFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M211.84,176.4C213.22,176.4 214.33,176.07 214.33,175.65C214.33,175.23 213.22,174.89 211.84,174.89C210.47,174.89 209.36,175.23 209.36,175.65C209.36,176.07 210.47,176.4 211.84,176.4Z"
        android:strokeAlpha="0.6"
        android:fillColor="#CCDFF6"
        android:fillAlpha="0.6"/>
    <path
        android:pathData="M210.08,175.77C210.96,176.11 215.21,169.92 215.84,168.28C216.48,166.64 215.8,164.83 214.37,164.28C212.94,163.74 211.3,164.62 210.67,166.26C210.04,167.9 209.19,175.44 210.08,175.77Z"
        android:strokeAlpha="0.5765"
        android:fillAlpha="0.5765">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="213.03"
            android:startY="165.26"
            android:endX="211.9"
            android:endY="175.74"
            android:type="linear">
          <item android:offset="0" android:color="#FFD6EBFF"/>
          <item android:offset="1" android:color="#FF84C3FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M210.08,175.82C211.21,175.52 211.05,166.81 210.5,164.87C209.95,162.94 208.06,161.63 206.29,162.09C204.52,162.56 203.55,164.54 204.1,166.51C204.65,168.49 208.94,176.11 210.08,175.82Z"
        android:strokeAlpha="0.8969"
        android:fillAlpha="0.8969">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="207.35"
            android:startY="163.03"
            android:endX="208.36"
            android:endY="175.72"
            android:type="linear">
          <item android:offset="0" android:color="#CCCCE7FF"/>
          <item android:offset="1" android:color="#FF7BBBF6"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M68.78,99.1C70.44,99.1 72.09,98.77 73.63,98.13C75.17,97.5 76.57,96.56 77.75,95.38C78.93,94.21 79.86,92.81 80.5,91.27C81.14,89.73 81.46,88.08 81.46,86.41C81.46,84.75 81.14,83.1 80.5,81.56C79.86,80.02 78.93,78.62 77.75,77.44C76.57,76.27 75.17,75.33 73.63,74.69C72.09,74.06 70.44,73.73 68.78,73.73C68.54,73.73 68.32,73.78 68.08,73.8C67.21,70.92 65.44,68.4 63.02,66.61C60.61,64.82 57.69,63.86 54.68,63.86C51.68,63.86 48.75,64.82 46.34,66.61C43.93,68.4 42.15,70.92 41.28,73.8C41.04,73.78 40.82,73.73 40.58,73.73C38.92,73.73 37.27,74.06 35.73,74.69C34.19,75.33 32.79,76.27 31.61,77.44C30.44,78.62 29.5,80.02 28.86,81.56C28.23,83.1 27.9,84.75 27.9,86.41C27.9,88.08 28.23,89.73 28.86,91.27C29.5,92.81 30.44,94.21 31.61,95.38C32.79,96.56 34.19,97.5 35.73,98.13C37.27,98.77 38.92,99.1 40.58,99.1H68.78Z"
        android:strokeAlpha="0.6"
        android:fillAlpha="0.6">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="54.73"
            android:startY="99.1"
            android:endX="54.73"
            android:endY="63.86"
            android:type="linear">
          <item android:offset="0" android:color="#19FFFFFF"/>
          <item android:offset="0.14" android:color="#44F8FBFF"/>
          <item android:offset="0.37" android:color="#84EDF5FF"/>
          <item android:offset="0.58" android:color="#BAE4F0FF"/>
          <item android:offset="0.76" android:color="#E0DEECFF"/>
          <item android:offset="0.91" android:color="#F7DAEAFF"/>
          <item android:offset="1" android:color="#FFDCEEFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M229.35,127.37C230.35,127.39 231.34,127.21 232.27,126.84C233.19,126.47 234.04,125.92 234.75,125.22C235.46,124.52 236.02,123.69 236.41,122.77C236.79,121.85 236.99,120.86 236.99,119.87C236.99,118.87 236.79,117.88 236.41,116.96C236.02,116.04 235.46,115.21 234.75,114.51C234.04,113.81 233.19,113.26 232.27,112.89C231.34,112.52 230.35,112.34 229.35,112.36C229.21,112.36 229.08,112.39 228.94,112.4C228.43,110.7 227.38,109.21 225.95,108.15C224.52,107.09 222.79,106.52 221.01,106.52C219.24,106.52 217.5,107.09 216.08,108.15C214.65,109.21 213.6,110.7 213.08,112.4C212.94,112.4 212.81,112.36 212.67,112.36C210.7,112.39 208.82,113.2 207.44,114.6C206.06,116.01 205.29,117.9 205.29,119.87C205.29,121.83 206.06,123.73 207.44,125.13C208.82,126.54 210.7,127.34 212.67,127.37H229.35Z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="221.01"
            android:startY="127.37"
            android:endX="221.01"
            android:endY="106.52"
            android:type="linear">
          <item android:offset="0" android:color="#00FFFFFF"/>
          <item android:offset="0.77" android:color="#FFDCEEFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M189.14,185.33C195.51,183.94 199.3,182.17 199.33,180.18C199.43,175.11 168.24,170.43 138.35,169.89C108.45,169.34 84.14,173 84.05,178.07C83.96,183.04 111.7,188.07 141.84,187.42C143.54,183.61 141.76,187.63 141.76,187.74C141.71,190.37 160,193.29 176.99,193.6C193.99,193.91 204.81,192.41 204.86,189.78C204.89,187.99 198.54,186.31 189.14,185.33Z"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="140.77"
            android:startY="193.37"
            android:endX="141.2"
            android:endY="169.94"
            android:type="linear">
          <item android:offset="0" android:color="#0A2F79E4"/>
          <item android:offset="0.77" android:color="#332673E3"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M195.18,51.57C193.81,49.42 191.5,48.09 188.98,48C187.52,45.73 185.01,44.39 182.34,44.43C180.19,44.41 178.13,45.27 176.62,46.81C175.93,46.58 175.2,46.47 174.47,46.46C171.11,46.46 168.33,48.83 168.04,51.81C165.47,52.43 163.63,54.71 163.52,57.38C163.52,60.6 166.43,63.22 169.95,63.22H194.66C198.23,63.22 201.09,60.6 201.09,57.38C201.15,54.32 198.51,51.81 195.18,51.57Z"
        android:strokeAlpha="0.8"
        android:fillAlpha="0.8">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="182.34"
            android:startY="63.22"
            android:endX="182.34"
            android:endY="44.43"
            android:type="linear">
          <item android:offset="0" android:color="#19FFFFFF"/>
          <item android:offset="0.14" android:color="#44F8FBFF"/>
          <item android:offset="0.37" android:color="#84EDF5FF"/>
          <item android:offset="0.58" android:color="#BAE4F0FF"/>
          <item android:offset="0.76" android:color="#E0DEECFF"/>
          <item android:offset="0.91" android:color="#F7DAEAFF"/>
          <item android:offset="1" android:color="#FFDCEEFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M241.12,43.61C238.7,38.64 233.67,35.47 228.14,35.46C225.06,35.46 222.06,36.46 219.59,38.3C216.66,32.58 210.28,29.5 203.97,30.74C197.65,31.99 192.93,37.27 192.4,43.68C185.61,45.82 181.39,52.59 182.47,59.62C183.55,66.65 189.6,71.85 196.73,71.86H236.91C244.07,71.87 250.16,66.63 251.21,59.55C252.26,52.47 247.95,45.7 241.09,43.63L241.12,43.61Z"
        android:strokeAlpha="0.8"
        android:fillAlpha="0.8">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="216.84"
            android:startY="71.83"
            android:endX="216.84"
            android:endY="30.44"
            android:type="linear">
          <item android:offset="0" android:color="#19FFFFFF"/>
          <item android:offset="0.09" android:color="#21FEFEFF"/>
          <item android:offset="0.23" android:color="#35FBFCFF"/>
          <item android:offset="0.4" android:color="#54F5F9FF"/>
          <item android:offset="0.59" android:color="#82EEF5FF"/>
          <item android:offset="0.79" android:color="#BCE4EFFF"/>
          <item android:offset="1" android:color="#FFDCEEFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M255.65,76.3C258.21,76.3 260.3,74.21 260.3,71.65C260.3,69.08 258.21,67 255.65,67C253.08,67 251,69.08 251,71.65C251,74.21 253.08,76.3 255.65,76.3Z"
        android:strokeAlpha="0.7"
        android:fillAlpha="0.7">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="255.65"
            android:startY="76.3"
            android:endX="255.65"
            android:endY="67"
            android:type="linear">
          <item android:offset="0" android:color="#FFF3F9FF"/>
          <item android:offset="0.77" android:color="#FFDCEEFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M64,52C67.87,52 71,48.87 71,45C71,41.13 67.87,38 64,38C60.13,38 57,41.13 57,45C57,48.87 60.13,52 64,52Z"
        android:strokeAlpha="0.7"
        android:fillAlpha="0.7">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="64"
            android:startY="52"
            android:endX="64"
            android:endY="38"
            android:type="linear">
          <item android:offset="0" android:color="#FFF3F9FF"/>
          <item android:offset="0.77" android:color="#FFDCEEFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M169.43,178.03H104.14C99.14,178.03 95.09,173.99 95.09,169.01V80.04C95.09,75.06 99.14,71.02 104.14,71.02H169.43C174.43,71.02 178.48,75.06 178.48,80.04V169.01C178.48,173.99 174.43,178.03 169.43,178.03Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="170.7"
            android:startY="134.07"
            android:endX="88.69"
            android:endY="79.11"
            android:type="linear">
          <item android:offset="0" android:color="#FF70B3FF"/>
          <item android:offset="1" android:color="#FFC2E0FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M165.45,180.54H103.11C98.12,180.54 94.09,176.49 94.09,171.48V83.59C94.09,78.59 98.12,74.54 103.11,74.54H165.45C170.43,74.54 174.46,78.59 174.46,83.59V171.49C174.46,176.49 170.43,180.54 165.45,180.54Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="233.96"
            android:startY="340.42"
            android:endX="59.9"
            android:endY="121.74"
            android:type="linear">
          <item android:offset="0" android:color="#FF579DF0"/>
          <item android:offset="1" android:color="#FFBFDEFA"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M164.65,169.49H104.9C104.2,169.49 103.63,168.92 103.63,168.21V82.84C103.63,82.14 104.2,81.57 104.9,81.57H164.65C165.35,81.57 165.92,82.14 165.92,82.84V168.21C165.92,168.92 165.35,169.49 164.65,169.49Z"
        android:fillColor="#FAFAFA"/>
    <path
        android:pathData="M131.63,168.98C126.06,168.6 95.46,166.28 76.51,150.94C75.57,150.18 76.01,148.62 77.21,148.51C106.62,145.84 103.04,93.2 103.25,83.11C103.26,82.25 103.81,81.57 104.66,81.57H164.05C164.81,81.57 165.42,82.19 165.42,82.95L162.31,139.48C162.2,139.79 161.98,140.39 161.76,140.65L138.89,166.22C138.61,166.55 132.06,169.01 131.63,168.98Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M140.08,149.37C143.49,149.58 153.66,149.66 162.4,139.85C160.33,150.17 147.03,168.85 129.75,168.98C139.68,163.86 139.3,154.38 138.99,150.13C138.95,149.63 139.59,149.34 140.08,149.37Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="218.83"
            android:startY="137"
            android:endX="174.91"
            android:endY="155.75"
            android:type="linear">
          <item android:offset="0" android:color="#FF55A5F1"/>
          <item android:offset="1" android:color="#FFC6E3FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M156.13,94.63H117.44C117.03,94.63 116.69,94.97 116.69,95.39V96.89C116.69,97.31 117.03,97.65 117.44,97.65H156.13C156.54,97.65 156.88,97.31 156.88,96.89V95.39C156.88,94.97 156.54,94.63 156.13,94.63Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="204.78"
            android:startY="96.16"
            android:endX="109.86"
            android:endY="90.17"
            android:type="linear">
          <item android:offset="0" android:color="#FF52A9FC"/>
          <item android:offset="1" android:color="#FFB1D9FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M155.13,104.68H116.44C116.03,104.68 115.69,105.02 115.69,105.43V106.94C115.69,107.36 116.03,107.69 116.44,107.69H155.13C155.54,107.69 155.88,107.36 155.88,106.94V105.43C155.88,105.02 155.54,104.68 155.13,104.68Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="203.78"
            android:startY="106.21"
            android:endX="108.86"
            android:endY="100.22"
            android:type="linear">
          <item android:offset="0" android:color="#FF52A9FC"/>
          <item android:offset="1" android:color="#FFB1D9FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M134.02,114.73H115.43C115.02,114.73 114.68,115.07 114.68,115.48V116.99C114.68,117.4 115.02,117.74 115.43,117.74H134.02C134.44,117.74 134.77,117.4 134.77,116.99V115.48C134.77,115.07 134.44,114.73 134.02,114.73Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="158.73"
            android:startY="116.33"
            android:endX="111.81"
            android:endY="110.4"
            android:type="linear">
          <item android:offset="0" android:color="#FF54A8F8"/>
          <item android:offset="1" android:color="#FFB1D9FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M140.06,114.73H138.55C138.13,114.73 137.8,115.07 137.8,115.48V116.99C137.8,117.4 138.13,117.74 138.55,117.74H140.06C140.47,117.74 140.81,117.4 140.81,116.99V115.48C140.81,115.07 140.47,114.73 140.06,114.73Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="144.4"
            android:startY="120.4"
            android:endX="137.26"
            android:endY="114.38"
            android:type="linear">
          <item android:offset="0" android:color="#FF53A7F7"/>
          <item android:offset="1" android:color="#FFB1D9FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M114,124.1L152.65,125.45C153.07,125.46 153.4,125.81 153.38,126.23L153.33,127.74C153.32,128.15 152.97,128.48 152.55,128.46L113.89,127.11C113.47,127.1 113.15,126.75 113.16,126.33L113.22,124.83C113.23,124.41 113.58,124.09 114,124.1Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="201.23"
            android:startY="128.68"
            android:endX="106.57"
            android:endY="119.38"
            android:type="linear">
          <item android:offset="0" android:color="#FF52A9FC"/>
          <item android:offset="1" android:color="#FFB1D9FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M111.06,133.48L149.65,136.18C150.07,136.2 150.38,136.56 150.35,136.98L150.25,138.48C150.22,138.9 149.86,139.21 149.44,139.18L110.85,136.48C110.44,136.45 110.13,136.09 110.15,135.68L110.26,134.18C110.29,133.76 110.65,133.45 111.06,133.48Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="198.09"
            android:startY="141.1"
            android:endX="103.81"
            android:endY="128.5"
            android:type="linear">
          <item android:offset="0" android:color="#FF52A9FC"/>
          <item android:offset="1" android:color="#FFB1D9FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M107.67,143.38L126.12,145.65C126.54,145.7 126.83,146.07 126.78,146.49L126.6,147.98C126.54,148.4 126.17,148.69 125.75,148.64L107.31,146.37C106.89,146.32 106.6,145.95 106.65,145.53L106.83,144.04C106.89,143.62 107.26,143.33 107.67,143.38Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="150.45"
            android:startY="150.25"
            android:endX="104.6"
            android:endY="138.65"
            android:type="linear">
          <item android:offset="0" android:color="#FF54A8F8"/>
          <item android:offset="1" android:color="#FFB1D9FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M130.61,146.2L132.11,146.38C132.52,146.43 132.81,146.81 132.76,147.22L132.58,148.72C132.53,149.13 132.15,149.42 131.74,149.37L130.24,149.19C129.83,149.14 129.54,148.76 129.59,148.35L129.77,146.85C129.82,146.44 130.2,146.15 130.61,146.2Z"
        android:fillColor="#85C2FC"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M148.84,74.54H122.72V83.58H148.84V74.54Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="153.09"
            android:startY="80.08"
            android:endX="122.32"
            android:endY="67.72"
            android:type="linear">
          <item android:offset="0" android:color="#FF47A5FF"/>
          <item android:offset="1" android:color="#FFB1D9FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M124.23,71.02H150.35L148.34,74.54H122.22L124.23,71.02Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="154.93"
            android:startY="72.83"
            android:endX="117.94"
            android:endY="67.47"
            android:type="linear">
          <item android:offset="0" android:color="#FF56A4FF"/>
          <item android:offset="1" android:color="#FFB1D9FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M136.28,62.98C138.78,62.98 140.8,65.01 140.8,67.5C140.8,70 138.78,72.03 136.28,72.03C133.78,72.03 131.76,70 131.76,67.5C131.76,65.01 133.78,62.98 136.28,62.98ZM136.28,64.99C134.89,64.99 133.77,66.12 133.77,67.5C133.77,68.89 134.89,70.02 136.28,70.02C137.67,70.02 138.79,68.89 138.79,67.5C138.79,66.12 137.67,64.99 136.28,64.99Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="142.34"
            android:startY="68.31"
            android:endX="130.14"
            android:endY="61.94"
            android:type="linear">
          <item android:offset="0" android:color="#FF54ACFF"/>
          <item android:offset="1" android:color="#FFB1D9FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M137.16,137.32C127.37,147.11 127.37,162.98 137.16,172.77C145.91,181.52 159.51,182.45 169.29,175.57L182.77,189.05C184.46,190.75 187.21,190.75 188.9,189.05C190.6,187.36 190.6,184.62 188.9,182.93L175.42,169.44C182.3,159.66 181.37,146.06 172.62,137.32C162.83,127.53 146.95,127.53 137.16,137.32ZM166.56,143.66C172.95,150.05 172.94,160.41 166.54,166.81C160.15,173.2 149.79,173.21 143.4,166.82C137.01,160.43 137.02,150.07 143.41,143.68C149.81,137.28 160.17,137.27 166.56,143.66Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="119.58"
            android:startY="165.53"
            android:endX="201"
            android:endY="123.02"
            android:type="linear">
          <item android:offset="0" android:color="#FF50AAFF"/>
          <item android:offset="1" android:color="#FFC5E3FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M138.99,135.49C129.2,145.28 129.2,161.15 138.99,170.94C147.74,179.69 161.34,180.62 171.12,173.74L184.6,187.23C186.29,188.92 189.04,188.92 190.73,187.23C192.43,185.53 192.43,182.79 190.73,181.1L177.25,167.61C184.13,157.83 183.2,144.23 174.45,135.49C164.66,125.7 148.78,125.7 138.99,135.49ZM168.39,141.83C174.78,148.22 174.77,158.58 168.37,164.98C161.98,171.38 151.62,171.38 145.23,164.99C138.84,158.61 138.85,148.24 145.24,141.85C151.64,135.45 162,135.45 168.39,141.83Z"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="161.83"
            android:startY="128.14"
            android:endX="161.83"
            android:endY="188.5"
            android:type="linear">
          <item android:offset="0" android:color="#FFF0F8FF"/>
          <item android:offset="1" android:color="#CCFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M61,186.47C63.11,186.47 64.82,185.93 64.82,185.27C64.82,184.62 63.11,184.08 61,184.08C58.89,184.08 57.18,184.62 57.18,185.27C57.18,185.93 58.89,186.47 61,186.47Z"
        android:strokeAlpha="0.6"
        android:fillColor="#CCDFF6"
        android:fillAlpha="0.6"/>
    <path
        android:pathData="M56.94,184.81C57.46,176.12 59.64,171.1 63.48,169.73C64.99,169.52 66.67,169.22 65.5,171.18C64.32,173.14 61.18,172.38 58.04,184.8C57.75,185.33 57.39,185.34 56.94,184.81Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="61.42"
            android:startY="171.37"
            android:endX="63.38"
            android:endY="185.01"
            android:type="linear">
          <item android:offset="0" android:color="#FFD1E9FF"/>
          <item android:offset="1" android:color="#FF7CB8FE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M56.87,184.7C55.85,176.59 55.01,171.48 54.36,169.39C53.39,166.26 51.31,160.22 55.63,162.5C59.96,164.79 60.47,176.5 58.47,183.63C57.85,185.43 57.31,185.79 56.87,184.7Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="56.3"
            android:startY="164.68"
            android:endX="57.89"
            android:endY="192.67"
            android:type="linear">
          <item android:offset="0" android:color="#FFCDE7FF"/>
          <item android:offset="1" android:color="#FF57A7F0"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
