<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <corners android:radius="@dimen/dp_100" />
            <gradient
                android:type="linear"
                android:startColor="@color/color_9568f7"
                android:endColor="@color/color_8c67f2"
                android:angle="45"/>
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape>
            <corners android:radius="@dimen/dp_100" />
            <solid android:color="@color/color_4b4b51"/>
        </shape>
    </item>
    <item>
        <shape android:state_enabled="true">
            <corners android:radius="@dimen/dp_100" />
            <gradient
                android:type="linear"
                android:startColor="@color/color_8c67f2"
                android:endColor="@color/color_9568f7"
                android:angle="45"/>
        </shape>
    </item>
</selector>