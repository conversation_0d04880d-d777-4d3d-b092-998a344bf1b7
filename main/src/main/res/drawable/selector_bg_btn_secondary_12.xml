<?xml version="1.0" encoding="utf-8"?><!--<ripple-->
<!--    android:color="@color/colorButtonSecondaryPressed"-->
<!--    android:radius="360dp">-->
<!--    <item>-->
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <corners android:radius="@dimen/dp_12" />
            <solid android:color="@color/colorButtonSecondaryPressed" />
            <stroke android:color="@color/colorPrimary" android:width="1dp"/>
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape>
            <stroke android:color="@color/colorPrimary" android:width="1dp"/>
            <corners android:radius="@dimen/dp_12" />
            <solid android:color="@color/colorButtonSecondaryDisabled" />
        </shape>
    </item>
    <item android:state_enabled="true">
        <shape>
            <stroke android:color="@color/colorPrimary" android:width="1dp"/>
            <corners android:radius="@dimen/dp_12" />
            <solid android:color="@color/colorButtonSecondaryNormal" />
        </shape>
    </item>
</selector><!--    </item>-->
    <!--</ripple>-->