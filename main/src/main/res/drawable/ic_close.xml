<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="16dp"
    android:height="16dp"
    android:viewportWidth="16"
    android:viewportHeight="16">
  <path
      android:pathData="M2,2L14,14M14,2L2,14"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeLineCap="round">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="2"
          android:startY="8"
          android:endX="14.001"
          android:endY="8"
          android:type="linear">
        <item android:offset="0.028" android:color="#FF315BB5"/>
        <item android:offset="0.506" android:color="#FF3B66BE"/>
        <item android:offset="1" android:color="#FF2953AC"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
