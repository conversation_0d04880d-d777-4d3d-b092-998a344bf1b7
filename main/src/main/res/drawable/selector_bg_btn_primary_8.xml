<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <corners android:radius="@dimen/dp_8" />
            <solid android:color="@color/colorButtonPrimaryPressed"/>
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape>
            <corners android:radius="@dimen/dp_8" />
            <solid android:color="@color/colorButtonPrimaryDisabled"/>
        </shape>
    </item>
    <item>
        <shape android:state_enabled="true">
            <corners android:radius="@dimen/dp_8" />
            <solid android:color="@color/colorButtonPrimaryPressed"/>
        </shape>
    </item>
</selector>