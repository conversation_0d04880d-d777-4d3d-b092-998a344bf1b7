<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <corners android:radius="@dimen/dp_12" />
            <solid android:color="@color/white"/>
            <stroke android:color="@color/colorButtonPrimaryPressed" android:width="@dimen/dp_1"/>
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape>
            <corners android:radius="@dimen/dp_12" />
            <solid android:color="@color/press_bg"/>
            <stroke android:color="@color/colorButtonPrimaryPressed" android:width="@dimen/dp_1"/>
        </shape>
    </item>
    <item>
        <shape android:state_enabled="true">
            <corners android:radius="@dimen/dp_12" />
            <solid android:color="@color/white"/>
            <stroke android:color="@color/colorButtonPrimaryPressed" android:width="@dimen/dp_1"/>
        </shape>
    </item>
</selector>


