import os
import tkinter
import traceback
from tkinter import filedialog
from typing import List

import pandas as pd
from lxml import etree
from pandas import DataFrame


def android_auto_i18n():
    root = tkinter.Tk()
    root.withdraw()

    i18n_excel_path = filedialog.askopenfilename(
        title="请选择你的 i18n excel 文件",
        filetypes=(("i18n excel", "*.xlsx"),)
    )
    print(f"你选择的文件是：{i18n_excel_path}")

    try:
        df = pd.read_excel(i18n_excel_path, index_col='string_id')
        android_res_dir = os.getcwd()
        print(f"当前安卓资源目录为：{android_res_dir}")

        string_ids = df.index.tolist()
        print(f"字符串id：{string_ids}")

        langs = df.columns.tolist()
        print(f"读取到语言：{langs}")

        for lang in langs:
            lang_ident = langs_map[lang]
            if lang_ident is not None:
                values_dir = 'values' if lang_ident == '' else f"values-{lang_ident}"
                strings_xml_path = os.path.join(android_res_dir, values_dir, "strings.xml")
                write_strings_xml(lang, strings_xml_path, string_ids, df)
    except FileNotFoundError as e:
        print(e)
        return
    except Exception as e:
        traceback.print_exc()
        print(f"读取 XLSX 文件时发生错误：{e}")
        return


langs_map = {
    "en": "",
    "简体中文": "zh-rCN",
    "繁体中文": "zh-rTW",
    "韩语": "ko",
    "日语": "ja",
}


def write_strings_xml(lang: str, xml_path: str, string_ids: List[str], df: DataFrame):
    try:
        if os.path.exists(xml_path):
            xml_tree = etree.parse(xml_path)
            root_tag = xml_tree.getroot()
        else:
            raise FileNotFoundError
    except FileNotFoundError:
        # 如果文件不存在，创建一个新的 <resources> 根标签
        root_tag = etree.Element("resources")
        xml_tree = etree.ElementTree(root_tag)

        os.makedirs(os.path.dirname(xml_path), exist_ok=True)
        print(f"文件 {xml_path} 不存在，已创建新文件。")
    except Exception:
        traceback.print_exc()
        return

    for string_id in string_ids:
        string_tag = root_tag.find(f"./string[@name='{string_id}']")

        excel_value = df.loc[string_id, lang]
        if pd.isna(excel_value):
            raise InterruptedError(f"读取单元格异常：string_id={string_id}, lang={lang}")
        value = f'"{str(excel_value)}"'

        if string_tag is None:
            new_tag = etree.SubElement(root_tag, "string", name=string_id)
            new_tag.text = value
        else:
            string_tag.text = value

    etree.indent(xml_tree, space='    ', level=0)
    xml_tree.write(xml_path, encoding='utf-8', xml_declaration=True)


if __name__ == '__main__':
    android_auto_i18n()
