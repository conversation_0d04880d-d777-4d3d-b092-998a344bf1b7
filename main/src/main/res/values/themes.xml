<resources>

    <style name="Theme.Light.NoTitle.LoadDialog" parent="android:style/Theme.Dialog">
        <!-- 去黑边 -->
        <item name="android:windowFrame">@null</item>
        <!-- 设置是否可滑动 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 设置是否透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 背景 -->
        <item name="android:background">@null</item>
        <!-- 窗口背景 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 是否变暗 -->
        <item name="android:backgroundDimEnabled">false</item>
        <!-- 点击空白部分activity不消失 -->
        <item name="android:windowCloseOnTouchOutside">false</item>
    </style>
    <!-- 從底部移入，移出動畫 -->
    <style name="Animation.Bottom.Rising" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/dialog_in_bottom</item>
        <item name="android:windowExitAnimation">@anim/dialog_out_bottom</item>
    </style>
    <!-- 原地淡入淡出動畫 -->
    <style name="Animation.Center.Lucency" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/dialog_center_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_center_out</item>
    </style>

    <style name="dialogStyle" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="AppTheme.NoActionBar" parent="Theme.AppCompat.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>

    <style name="SplashTheme" parent="AppTheme.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_bg</item>
    </style>

    <style name="BlackWindowTheme" parent="AppTheme.NoActionBar">
        <item name="android:windowBackground">@color/black</item>
    </style>

    <style name="longPrimaryButton">
        <item name="android:background">@drawable/selector_bg_btn_primary_12</item>
        <item name="android:layout_width">@dimen/dp_302</item>
        <item name="android:layout_height">@dimen/dp_52</item>
        <item name="android:textAllCaps">false</item>

        <item name="android:textSize">@dimen/sp_16</item>
        <item name="android:textColor">@color/selector_on_primary</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="primaryEditText" parent="Widget.AppCompat.EditText">
        <item name="android:background">@drawable/bg_edit_text</item>
        <item name="android:layout_width">@dimen/dp_302</item>
        <item name="android:layout_height">@dimen/dp_48</item>
        <item name="android:paddingEnd">@dimen/dp_15</item>
        <item name="android:paddingStart">@dimen/dp_15</item>

        <item name="android:textSize">@dimen/sp_15</item>
        <item name="android:textColor">@color/colorTextPrimary</item>
        <item name="android:textColorHint">@color/colorTextPrimaryVariant</item>
        <item name="android:gravity">start|center_vertical</item>
        <item name="android:singleLine">true</item>
        <item name="android:lines">1</item>
    </style>

    <style name="Theme.NoTitle.Dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowAnimationStyle">@style/anim_center_in_out</item>
        <!-- 设置是否透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 背景 -->
        <!--        <item name="android:background">@null</item>-->
        <!-- 窗口背景 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 是否变暗 -->
        <!--   <item name="android:backgroundDimEnabled">false</item>-->
    </style>

    <style name="anim_center_in_out">
        <item name="android:windowEnterAnimation">@anim/dialog_center_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_center_out</item>
    </style>

    <style name="shortPrimaryButton">
        <item name="android:background">@drawable/selector_bg_btn_primary_12</item>
        <item name="android:layout_width">@dimen/dp_192</item>
        <item name="android:layout_height">@dimen/dp_48</item>
        <item name="android:textAllCaps">false</item>

        <item name="android:textSize">@dimen/sp_16</item>
        <item name="android:textColor">@color/selector_on_primary</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="tipMoveButton">
        <item name="android:background">@drawable/selector_bg_btn_primary_12</item>
        <item name="android:layout_width">@dimen/dp_200</item>
        <item name="android:layout_height">@dimen/dp_48</item>
        <item name="android:textAllCaps">false</item>

        <item name="android:textSize">@dimen/sp_16</item>
        <item name="android:textColor">@color/selector_on_primary</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="tipCancelButton">
        <item name="android:background">@drawable/selector_bg_btn_white_12</item>
        <item name="android:layout_width">@dimen/dp_200</item>
        <item name="android:layout_height">@dimen/dp_48</item>
        <item name="android:textAllCaps">false</item>

        <item name="android:textSize">@dimen/sp_16</item>
        <item name="android:textColor">@color/colorButtonPrimaryPressed</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="longSecondaryButton">
        <item name="android:background">@drawable/selector_bg_btn_secondary_12</item>
        <item name="android:layout_width">@dimen/dp_302</item>
        <item name="android:layout_height">@dimen/dp_48</item>
        <item name="android:textAllCaps">false</item>

        <item name="android:textSize">@dimen/sp_16</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:gravity">center</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <style name="dialog_bottom" parent="AppTheme.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@style/Animation.Bottom.Rising</item>
    </style>

    <style name="userInfoTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_marginStart">@dimen/dp_28</item>
        <item name="android:layout_marginEnd">@dimen/dp_16</item>
        <item name="android:layout_marginTop">@dimen/dp_16</item>
    </style>

    <style name="userInfoValue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:textColor">@color/color_999999</item>
        <item name="android:layout_marginEnd">@dimen/dp_36</item>
    </style>

    <style name="userInfoLine">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/dp_1</item>
        <item name="android:layout_marginTop">@dimen/dp_12</item>
        <item name="android:background">@color/color_1FFFFFFF</item>
    </style>

    <style name="imageShape12">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">@dimen/dp_12</item>
    </style>

    <style name="imageShapeRound">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
    <!--feedback editText-->
</resources>