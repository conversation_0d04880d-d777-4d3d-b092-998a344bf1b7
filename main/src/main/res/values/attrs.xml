<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="InterBlackTextView">
        <attr name="typeface">
            <enum name="black" value="0" />
            <enum name="inter" value="1" />
            <enum name="bold" value="2" />
        </attr>
    </declare-styleable>

    <declare-styleable name="MaxHeightRecyclerView">
        <attr name="maxHeight" format="dimension" />
    </declare-styleable>
    <declare-styleable name="MaxHeightNestedScrollView">
        <attr name="myMaxHeight" format="dimension" />
    </declare-styleable>

    <declare-styleable name="VerifyCodeButton">
        <!--默认背景-->
        <attr name="android:background" />
        <!--点击后背景-->
        <attr name="clickedBackground" format="reference" />
        <!--倒计时间-->
        <attr name="countdownTime" format="integer" />
        <!--倒计时间后提示文字-->
        <attr name="countdownText" format="string" />
    </declare-styleable>
</resources>