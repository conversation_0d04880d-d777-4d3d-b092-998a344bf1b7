package com.solvibe

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.core.net.toUri
import com.imyfone.membership.MembershipClient
import com.imyfone.membership.api.bean.GuestInfoBean
import com.imyfone.membership.api.bean.MemberBean
import com.imyfone.membership.api.bean.UserBean
import com.imyfone.membership.repository.AccountCommonCode
import com.solvibe.main.App
import com.solvibe.main.BuildConfig
import com.solvibe.main.activity.WebActivity
import com.solvibe.main.bean.GuestVipState
import com.solvibe.main.bean.MemberVipState
import com.solvibe.main.bean.TouristsData
import com.solvibe.main.bean.UserManager
import com.solvibe.main.ext.stateInApp
import com.solvibe.main.repo.PermissionRepo
import com.solvibe.utils.ext.loge
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.runBlocking
import okhttp3.HttpUrl.Companion.toHttpUrl

/**
 *creater:linjinhao on 2025/5/13 10:04
 */
@SuppressLint("StaticFieldLeak")
object Membership {
    private var completableDeferred: CompletableDeferred<String>? = null
    lateinit var membershipClient: MembershipClient
        private set

    val account get() = membershipClient.account
    val guestRepo get() = membershipClient.guest
    val permissionRepo = PermissionRepo()
    private val _touristsTokenDataStateFlow = MutableStateFlow<TouristsData?>(null)
    val touristsTokenDataStateFlow get() = _touristsTokenDataStateFlow.asStateFlow()

    /**
     * 登录失效的事件
     */
    private val _loginInvalidEvent = MutableSharedFlow<LoginInvalidEvent>()
    val loginInvalidEvent = _loginInvalidEvent.asSharedFlow()

    /**
     * 用户账号状态事件
     */
    private val _AccountStateEvent = MutableSharedFlow<AccountStateEvent>()
    val accountStateEvent = _AccountStateEvent.asSharedFlow()
    private lateinit var _userStateFlow: StateFlow<UserBean?>
    val userStateFlow get() = _userStateFlow

    private lateinit var _memberStateFlow: StateFlow<MemberBean?>
    val memberStateFlow get() = _memberStateFlow

    private lateinit var _guestStateFlow: StateFlow<GuestInfoBean?>
    val guestStateFlow get() = _guestStateFlow

    private lateinit var _guestVipStateFlow: StateFlow<GuestVipState?>
    val guestVipStateFlow get() = _guestVipStateFlow
    private lateinit var _memberVipStateFlow: StateFlow<MemberVipState?>
    val memberVipStateFlow get() = _memberVipStateFlow

    private lateinit var _vipStateFlow: StateFlow<Boolean>
    val vipStateFlow get() = _vipStateFlow

    private lateinit var _loginStateFlow: StateFlow<Boolean>
    val loginStateFlow get() = _loginStateFlow


    fun initClient() {
        membershipClient = MembershipClient.Builder(App.getInstance())
            .pid(com.solvibe.main.config.Constant.INFORMATION_SOURCES)
            .accountBaseURL(BuildConfig.BaseUrl)
            .transferGoogleSkuID { it -> it }
            .supportGuest(true)
            .iCartBrowser { s, completableDeferred ->
                makePurchasePending(App.getInstance(), s, completableDeferred)
            }.build().apply {
                setICartURL(BuildConfig.BaseUrlICart)
            }

        _userStateFlow = account.userFlow.stateInApp(SharingStarted.Eagerly)
        _memberStateFlow = account.memberFlow.stateInApp(SharingStarted.Eagerly)
        _guestStateFlow = guestRepo.guestInfoFlow.stateInApp(SharingStarted.Eagerly)

        _guestVipStateFlow = UserManager.guestVipState.stateInApp(SharingStarted.Eagerly)
        _memberVipStateFlow = UserManager.memberVipState.stateInApp(SharingStarted.Eagerly)


        _vipStateFlow = combine(_memberStateFlow, _guestStateFlow) { member, guest ->
            if (member != null) {
                member.vipTypeOfProduct == 2
            } else {
                guest?.vip_type_of_product == 2
            }
        }.stateInApp(
            initialValue = runBlocking { false },
            SharingStarted.Eagerly
        )

        _loginStateFlow = _userStateFlow.map {
            it != null
        }.stateInApp(
            initialValue = runBlocking { false },
            SharingStarted.Eagerly
        )

        membershipClient.account.onLoginInvalid = {
            when (it) {
                AccountCommonCode.LOGIN_INVALID -> {
                    App.launch {
                        _loginInvalidEvent.emit(LoginInvalidEvent.LoginInvalid)
                    }
                }

                AccountCommonCode.LOGIN_OUT_BY_OTHER -> {
                    App.launch {
                        _loginInvalidEvent.emit(LoginInvalidEvent.LoginOutByOther)
                    }
                }

                AccountCommonCode.LOGIN_OUT_BY_CHANGE_PASSWORD -> {
                    App.launch {
                        _loginInvalidEvent.emit(LoginInvalidEvent.LoginOutByChangePassword)
                    }
                }
            }
        }
    }

    /**
     * 挂起购买，等待网页结果返回
     */
    private fun makePurchasePending(
        context: Context,
        url: String,
        completableDeferred: CompletableDeferred<String>
    ) {
        this.completableDeferred?.cancel()
        this.completableDeferred = completableDeferred
        // 字符串拼接
        val purchaseURL = try {
            url.toHttpUrl().newBuilder()
                .addEncodedQueryParameter(
                    "pid",
                    com.solvibe.main.config.Constant.INFORMATION_SOURCES
                )
                .addEncodedQueryParameter("custom", com.solvibe.main.config.Constant.fromSite)
                .build()
                .toString()
        } catch (e: Exception) {
            url
        }
        WebActivity.purchase(context, purchaseURL)
    }

    internal fun completeICartPurchase(email: String) {
        this.completableDeferred?.complete(email)
        this.completableDeferred = null
    }

    fun toGoogleSubscription(context: Context) {
        try {
            val url = "https://play.google.com/store/account/subscriptions"
            val intent = Intent(Intent.ACTION_VIEW).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                data = url.toUri()
                setPackage("com.android.vending")
            }
            context.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun setTouristsTokenData(touristsData: TouristsData?) {
        _touristsTokenDataStateFlow.value = touristsData
    }

    fun getUserBean() = userStateFlow.value

    fun getUserId() = getUserBean()?.memberID

    fun getEmail() = getUserBean()?.email

    fun isLogin() = getUserBean()?.memberID != null

    fun isVip() = if (isLogin()) {
        memberStateFlow.value?.vipTypeOfProduct == 2
    } else {
        guestStateFlow.value?.vip_type_of_product == 2
    }

    fun logout() {
        loge("用户token过期，退出登录")
        App.launch {
            _loginInvalidEvent.emit(LoginInvalidEvent.LoginInvalid)
        }
    }

    val token
        get() = getUserBean()?.token.orEmpty()
            .ifEmpty { touristsTokenDataStateFlow.value?.token.orEmpty() }

    /**
     * 用户登录事件
     */
    fun sendAccountStateEvent(event: AccountStateEvent) {
        App.launch {
            _AccountStateEvent.emit(event)
        }
    }
}

/**
 * 登录失效，
 */
sealed interface LoginInvalidEvent {
    data object LoginInvalid : LoginInvalidEvent
    data object LoginOutByOther : LoginInvalidEvent
    data object LoginOutByChangePassword : LoginInvalidEvent
}

sealed interface ReportUserInfoEvent {
    data object LoginSuccess : ReportUserInfoEvent
}

/**
 * 账号状态，登录、登出、删除账号
 */
sealed interface AccountStateEvent {
    data object LoggedIn : AccountStateEvent
    data object Logout : AccountStateEvent
    data object DeleteAccountState : AccountStateEvent
}