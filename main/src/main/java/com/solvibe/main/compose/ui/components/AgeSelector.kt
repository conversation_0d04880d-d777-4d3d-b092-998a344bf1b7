package com.solvibe.main.compose.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White8

@Composable
fun AgeSelector(index: Int, onSelected: (index: Int) -> Unit) {
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        AgeSelectBox(index == 0, stringResource(R.string.new_user_info_select_page_age_below_14)) {
            onSelected(0)
        }
        AgeSelectBox(index == 1, stringResource(R.string.new_user_info_select_page_age_14_17)) {
            onSelected(1)
        }
        AgeSelectBox(index == 2, stringResource(R.string.new_user_info_select_page_age_18_25)) {
            onSelected(2)
        }
        AgeSelectBox(index == 3, stringResource(R.string.new_user_info_select_page_age_bove_25)) {
            onSelected(3)
        }
    }
}

@Composable
fun AgeSelectBox(
    selected: Boolean,
    text: String,
    onSelected: () -> Unit,
) {
    Box(
        Modifier
            .size(78.dp, 40.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(if (selected) Color(0xFFFF5476) else White8)
            .clickable(onClick = onSelected),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.colorScheme.onPrimary,
            ),
        )
    }
}

@Preview
@Composable
fun AgeSelectorPrev() {
    AgeSelector(0) {}
}