package com.solvibe.main.compose.ui.page.live2d

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DrawerState
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalDrawerSheet
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.Membership
import com.solvibe.main.App
import com.solvibe.main.R
import com.solvibe.main.bean.SVSession
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.page.chat.DeletePopup
import com.solvibe.main.compose.utils.withClickable
import com.solvibe.utils.ext.logv
import kotlinx.coroutines.launch

@Composable
fun MainDrawer(
    drawerState: DrawerState,
    onAvatarClick: () -> Unit,
    onLoginClick: () -> Unit,
    onSignUpClick: () -> Unit,
    onMoreClick: () -> Unit,
    viewModel: Live2DViewModel = viewModel()
) {
    val appViewModel = App.viewModel
    val sessions by appViewModel.sessionState.sessionsFlow.collectAsStateWithLifecycle()
    val scope = rememberCoroutineScope()

    ModalDrawerSheet(
        drawerState,
        Modifier
            .width(286.dp)
            .fillMaxHeight(),
        drawerShape = RoundedCornerShape(0.dp),
        drawerContainerColor = Color(0xFF19171F)
    ) {
        Column {
            if (sessions.isNotEmpty()) {
                ChatRecordList(
                    sessions.groupBy { it.dateStr }
                        .flatMap { (date, chatRecords) ->
                            buildList {
                                add(ChatRecordItem.Group(date))
                                addAll(chatRecords.map { ChatRecordItem.Record(it) })
                            }
                        },
                    onItemClick = {
                        appViewModel.sessionState.updateSessionId(it.id)
                        scope.launch {
                            drawerState.close()
                        }
                    },
                    onLongClick = {
                        viewModel.deletingSession = it.id
                    },
                    deletingSession = viewModel.deletingSession,
                    onCancelDelete = {
                        viewModel.deletingSession = null
                    },
                    onDelete = {
                        viewModel.showDeleteSessionDialog(viewModel.deletingSession)
                        viewModel.deletingSession = null
                    },
                    onLoadMore = {
                        appViewModel.sessionState.loadMore()
                    }
                )
            } else {
                EmptyChat {
                    scope.launch {
                        drawerState.close()
                    }
                }
            }
            UserCell(
                onAvatarClick = onAvatarClick,
                onLoginClick = onLoginClick,
                onSignUpClick = onSignUpClick,
                onMoreClick = onMoreClick
            )
        }
    }
}

@Composable
private fun ColumnScope.ChatRecordList(
    records: List<ChatRecordItem>,
    onItemClick: (SVSession) -> Unit,
    onLongClick: (SVSession) -> Unit,
    deletingSession: Long?,
    onCancelDelete: () -> Unit,
    onDelete: () -> Unit,
    onLoadMore: () -> Unit,
) {
    LazyColumn(
        Modifier
            .weight(1f)
            .fillMaxWidth()
    ) {
        items(records, key = { it.uniqueID() }) { item ->
            when (item) {
                is ChatRecordItem.Group -> ChatRecordGroup(item.date)
                is ChatRecordItem.Record -> ChatRecordItem(
                    item.session,
                    onItemClick,
                    onLongClick,
                    deletingSession == item.session.id,
                    onCancelDelete,
                    onDelete
                )
            }
        }
        item("LOADING_MORE") {
            LaunchedEffect(Unit) {
                onLoadMore()
            }
        }
    }
}

@Composable
fun ColumnScope.EmptyChat(
    onNewChatClick: () -> Unit
) {
    Column(
        Modifier
            .weight(1f)
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            stringResource(R.string.no_chat_record_tips_title),
            Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth(),
            style = MaterialTheme.typography.headlineLarge.copy(
                color = Color.White,
                fontSize = 20.sp,
                lineHeight = 30.sp,
                textAlign = TextAlign.Center
            )
        )
        DTVerticalSpacer(6.dp)
        Text(
            stringResource(R.string.no_chat_record_tips),
            Modifier
                .padding(horizontal = 40.dp)
                .fillMaxWidth(),
            style = MaterialTheme.typography.bodySmall.copy(
                color = Color(0xFFB6B6B6),
                textAlign = TextAlign.Center
            )
        )
        DTVerticalSpacer(20.dp)
        DTButton(
            stringResource(R.string.start_a_new_conversation),
            brush = Brush.horizontalGradient(
                listOf(
                    Color(0xFFBE4EFF),
                    Color(0xFFF343A1),
                    Color(0xFFFF5476),
                )
            ),
            Modifier
                .size(187.dp, 36.dp),
            shape = RoundedCornerShape(50),
            textStyle = MaterialTheme.typography.labelMedium.copy(
                color = Color.White,
            ),
            onClick = onNewChatClick
        )
    }
}

@Composable
private fun UserCell(
    viewModel: Live2DViewModel = viewModel(),
    onAvatarClick: () -> Unit,
    onLoginClick: () -> Unit,
    onSignUpClick: () -> Unit,
    onMoreClick: () -> Unit
) {
    val user by Membership.userStateFlow.collectAsStateWithLifecycle()
    val memberVipState by viewModel.memberVipStateFlow.collectAsState()
    val guestVipState by viewModel.guestVipStateFlow.collectAsState()
    val isVip by Membership.vipStateFlow.collectAsState()
    Row(
        Modifier
            .padding(start = 16.dp, end = 12.dp, bottom = 8.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painterResource(R.drawable.ic_avatar),
            null,
            Modifier
                .size(32.dp)
                .clip(CircleShape)
                .clickable(enabled = user != null, onClick = onAvatarClick)
        )
        if (user != null) {
            Column(
                Modifier
                    .padding(horizontal = 10.dp)
                    .weight(1f)
            ) {
                Text(
                    user?.email.orEmpty(),
                    style = MaterialTheme.typography.headlineSmall.copy(
                        color = Color.White,
                        lineHeight = 21.sp
                    )
                )
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        painterResource(if (isVip) R.drawable.ic_user_vip else R.drawable.ic_user_vip_def),
                        null,
                        Modifier.size(10.dp),
                        tint = Color.Unspecified
                    )
                    DTHorizontalSpacer(5.dp)
                    if (isVip) {
                        Text(
                            "${memberVipState?.licenseName}(${memberVipState?.vipFailureTimeText ?: ""})",
                            style = MaterialTheme.typography.labelMedium.copy(
                                color = Color(0xFFB6B6B6),
                                lineHeight = 18.sp
                            )
                        )
                    } else {
                        Text(
                            "Free",
                            style = MaterialTheme.typography.labelMedium.copy(
                                color = Color(0xFFB6B6B6),
                                lineHeight = 18.sp
                            )
                        )
                    }
                }
            }
        } else {
            Column(
                Modifier
                    .padding(horizontal = 10.dp)
                    .weight(1f)
            ) {
                Text(
                    buildAnnotatedString {
                        withClickable(stringResource(id = R.string.log_in), "login") {
                            logv("click login")
                            onLoginClick()
                        }
                        append(" / ")
                        withClickable(stringResource(id = R.string.sign_up), "signup") {
                            logv("click signup")
                            onSignUpClick()
                        }
                    },
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = Color.White,
                    )
                )
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        painterResource(if (isVip) R.drawable.ic_user_vip else R.drawable.ic_user_vip_def),
                        null,
                        Modifier.size(10.dp),
                        tint = Color.Unspecified
                    )
                    if (isVip) {
                        Text(
                            text = "${guestVipState?.licenseName ?: ""}(${guestVipState?.vipFailureTimeText ?: ""})",
                            style = MaterialTheme.typography.bodySmall.copy(color = Color(0xFFB6B6B6))
                        )
                    } else {
                        Text(
                            text = "Free",
                            style = MaterialTheme.typography.bodySmall.copy(color = Color(0xFFB6B6B6))
                        )
                    }
                }
            }

        }
        Icon(
            painterResource(R.drawable.ic_more_settings),
            null,
            Modifier
                .size(24.dp)
                .clip(CircleShape)
                .clickable(onClick = onMoreClick),
            tint = Color.Unspecified
        )
    }
}

@Composable
private fun ChatRecordGroup(date: String) {
    Text(
        date,
        Modifier
            .padding(start = 15.dp, top = 28.dp, end = 15.dp, bottom = 8.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.labelMedium.copy(
            color = Color(0xFFB6B6B6),
        )
    )
}

@Composable
private fun ChatRecordItem(
    session: SVSession,
    onItemClick: (SVSession) -> Unit,
    onLongClick: (SVSession) -> Unit,
    showDeletePop: Boolean,
    onCancelDelete: () -> Unit,
    onDelete: () -> Unit,
) {
    Column {
        Text(
            session.lastMessage?.content.orEmpty(),
            Modifier
                .padding(horizontal = 8.dp)
                .fillMaxWidth()
                .clip(RoundedCornerShape(6.dp))
                .combinedClickable(
                    onClick = {
                        onItemClick(session)
                    },
                    onLongClick = {
                        onLongClick(session)
                    }
                )
                .padding(horizontal = 8.dp, vertical = 12.dp),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = MaterialTheme.typography.bodySmall.copy(
                color = Color.White,
            )
        )
        if (showDeletePop) {
            DeletePopup(
                Modifier.padding(start = 8.dp, top = 1.dp),
                onDismiss = onCancelDelete,
                onClick = onDelete
            )
        }
    }
}

sealed interface ChatRecordItem {
    data class Group(val date: String) : ChatRecordItem
    data class Record(val session: SVSession) : ChatRecordItem

    fun uniqueID(): String {
        return when (this) {
            is Group -> date
            is Record -> session.id.toString()
        }
    }
}