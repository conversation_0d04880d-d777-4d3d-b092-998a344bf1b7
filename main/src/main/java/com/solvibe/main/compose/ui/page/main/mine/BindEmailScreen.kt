package com.solvibe.main.compose.ui.page.main.mine

import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.compose.AsyncImage
import com.solvibe.main.R
import com.solvibe.main.bean.BindAccountBean
import com.solvibe.main.compose.theme.SocialBlue
import com.solvibe.main.compose.theme.White
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTLoading
import com.solvibe.main.compose.ui.modifier.click
import com.solvibe.main.ext.showToast
import kotlinx.coroutines.flow.SharedFlow
import java.net.URLEncoder

/**
 *creater:linjinhao on 2025/5/22 16:20
 */
@Composable
fun BindEmailRoute(
    info: BindAccountBean,
    viewModel: BindEmailViewModel = viewModel(),
    onBack: () -> Unit,
    onCheckPassword: (data: BindAccountBean) -> Unit,
    onNeedCreateAccount: (data: BindAccountBean) -> Unit,
    onBindAnotherAccount: (data: BindAccountBean) -> Unit
) {
    var email by remember { mutableStateOf(TextFieldValue("")) }
    var emailTip by remember { mutableStateOf("") }
    BindEmailScreen(
        email = email,
        info = info,
        emailTip = emailTip,
        onBack = onBack,
        onEmailChanged = { newEmail ->
            if (newEmail.text.contains(' ', true)) {
                email = TextFieldValue(newEmail.text.replace(" ", ""))
            }
            email = if (newEmail.text.length > 50) {
                val substring = newEmail.text.substring(0, 50)
                TextFieldValue(substring, selection = TextRange(substring.length))
            } else {
                newEmail
            }
        })

    HandleBindEmailEvent(
        viewModel.bindEmailEvent,
        onChangeEmailTip = { emailTip = it },
        onNeedCreateAccount = {
            onNeedCreateAccount(
                info.copy(account = email.text, avatar = URLEncoder.encode(info.avatar))
            )
        },
        onCheckPassword = {
            onCheckPassword(
                info.copy(account = email.text, avatar = URLEncoder.encode(info.avatar))
            )
        }, onBindAnotherAccount = {
            onBindAnotherAccount(
                info.copy(avatar = URLEncoder.encode(info.avatar))
            )
        })

    val loading by viewModel.bindEmailState.collectAsState()
    DTLoading(loading = loading) { }
}

@Composable
fun BindEmailScreen(
    email: TextFieldValue,
    info: BindAccountBean,
    emailTip: String,
    viewModel: BindEmailViewModel = viewModel(),
    onBack: () -> Unit,
    onEmailChanged: (TextFieldValue) -> Unit,
) {
    val softwareKeyboardController = LocalSoftwareKeyboardController.current
    val localFocusManager = LocalFocusManager.current

    Box(
        modifier = Modifier
            .click {
                localFocusManager.clearFocus()
                softwareKeyboardController?.hide()
            }
            .fillMaxSize()) {
        val scrollState = rememberScrollState()
        Image(
            painterResource(R.drawable.bg_common_bg),
            contentDescription = "background",
            modifier = Modifier.fillMaxSize()
        )
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .fillMaxSize()
                .verticalScroll(scrollState)
        ) {
            Image(
                painterResource(R.drawable.ic_top_bar_back),
                contentDescription = "iconBack",
                modifier = Modifier
                    .padding(start = 20.dp)
                    .click { onBack() }
                    .size(30.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                stringResource(R.string.bind_account_id),
                style = TextStyle(fontSize = 16.sp, color = White, fontWeight = FontWeight.W600),
                modifier = Modifier
                    .padding(start = 20.dp, end = 20.dp)
                    .fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(30.dp))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Spacer(modifier = Modifier.width(16.dp))
                AsyncImage(
                    model = info.avatar,
                    contentDescription = "avatar",
                    modifier = Modifier
                        .size(36.dp)
                        .clip(RoundedCornerShape(6.dp))
                )
                Spacer(modifier = Modifier.width(16.dp))
                Image(
                    painterResource(R.drawable.ic_google),
                    contentDescription = "avatar",
                    modifier = Modifier.size(36.dp)
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = info.displayName,
                    style = TextStyle(
                        fontSize = 18.sp,
                        color = White,
                        fontWeight = FontWeight.W700
                    ),
                )
            }
            Spacer(modifier = Modifier.height(12.dp))
            Text(
                stringResource(R.string.please_enter_an_account_email_you_want_to_bind),
                style = TextStyle(fontSize = 18.sp, color = White, fontWeight = FontWeight.W700),
                modifier = Modifier
                    .padding(start = 20.dp, end = 20.dp)
                    .fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(30.dp))
            BindEmailTextField(email = email, onEmailChanged = onEmailChanged)
            Spacer(modifier = Modifier.height(3.dp))
            val backgroundColor by animateColorAsState(
                targetValue = if (emailTip.isNotEmpty()) colorResource(R.color.color_F44548) else Color.Transparent
            )
            Text(text = emailTip, style = TextStyle(fontSize = 12.sp, color = backgroundColor))

            Spacer(modifier = Modifier.height(116.dp))

            DTButton(
                R.string.next,
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp)
                    .fillMaxSize()
                    .height(46.dp),
                enable = email.text.isNotEmpty(),
                containerColor = SocialBlue,
                contentColor = White,
                disabledContainerColor = colorResource(R.color.color_4b4b51),
                disabledContentColor = colorResource(R.color.color_8A8C91)
            ) {
                localFocusManager.clearFocus()
                viewModel.checkEmailRegister(email = email.text)
            }
            Spacer(modifier = Modifier.height(20.dp))
        }
    }
    val loading by viewModel.bindEmailState.collectAsState()
    DTLoading(loading = loading) { }
}

@Composable
fun BindEmailTextField(
    modifier: Modifier = Modifier,
    email: TextFieldValue,
    onEmailChanged: (TextFieldValue) -> Unit,
) {
    Box(
        modifier
            .padding(start = 25.dp, end = 25.dp)
            .fillMaxWidth()
            .background(colorResource(R.color.colorViewBackground), CircleShape)
    ) {
        BasicTextField(
            value = email,
            onValueChange = {
                onEmailChanged(it)
            },
            textStyle = TextStyle(fontSize = 15.sp, color = Color.Black),
            singleLine = true,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
            modifier = Modifier
                .height(48.dp)
                .fillMaxWidth()
                .padding(start = 16.dp),
            decorationBox = { innerTextField ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(horizontal = 10.dp)
                ) {
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        if (email.text.isEmpty()) {
                            Text(
                                stringResource(R.string.email),
                                fontSize = 12.sp,
                                color = colorResource(R.color.colorTextPrimaryVariant)
                            )
                        }
                        innerTextField()
                    }
                }
            }
        )
    }

}


@Composable
private fun HandleBindEmailEvent(
    event: SharedFlow<BindEmailEvent>,
    onChangeEmailTip: (text: String) -> Unit,
    onCheckPassword: () -> Unit,
    onNeedCreateAccount: () -> Unit,
    onBindAnotherAccount: () -> Unit,
) {
    val context = LocalContext.current
    LaunchedEffect(event) {
        event.collect {
            when (it) {
                BindEmailEvent.CommonError -> {
                    showToast(context.getString(R.string.network_error))
                }

                BindEmailEvent.EmailFormatError -> {
                    onChangeEmailTip(context.getString(R.string.login_email_valid))
                }

                is BindEmailEvent.NeedCreateAccount -> {
                    onNeedCreateAccount()
                }

                is BindEmailEvent.EmailHasRegisterUnBind -> {
                    onCheckPassword()
                }

                is BindEmailEvent.EmailHasRegisterAndBind -> {
                    onBindAnotherAccount()
                }
            }
        }
    }
}