package com.solvibe.main.compose.ui.page.chat

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import coil3.compose.AsyncImage
import com.skydoves.landscapist.coil3.CoilImage
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.transformation.blur.BlurTransformationPlugin
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White12
import com.solvibe.main.compose.theme.White40
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.components.MsgLoading
import com.solvibe.main.db.result.AIRoleSession
import com.solvibe.main.db.table.MsgRecord
import com.solvibe.main.db.table.MsgType
import com.solvibe.main.ext.imageRequest
import com.solvibe.utils.utils.newLocalDateTimeFMT

@Composable
fun ChatMessage(
    aiRole: AIRoleSession?,
    msgRecord: MsgRecord,
    ttsPlaying: Boolean,
    onAvatarClick: () -> Unit,
    onContentClick: () -> Unit,
    onContentLongClick: () -> Unit,
    onTTSClick: () -> Unit,
    showLongPressedMsgPopup: Boolean,
    onDismissLongPressedMsgPopup: () -> Unit,
    onClickPopupOption: (MsgPopupOptionType) -> Unit,
    showDeletePop: Boolean,
    onDismissDeletePop: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (msgRecord.fromSelf) {
        MyChatMessage(
            msgRecord = msgRecord,
            onLongClick = onContentLongClick,
            showLongPressedMsgPopup = showLongPressedMsgPopup,
            onDismissLongPressedMsgPopup = onDismissLongPressedMsgPopup,
            onClickPopupOption = onClickPopupOption,
            modifier = modifier,
        )
    } else {
        OtherChatMessage(
            aiRole = aiRole,
            msgRecord = msgRecord,
            ttsPlaying = ttsPlaying,
            onAvatarClick = onAvatarClick,
            onContentClick = onContentClick,
            onContentLongClick = onContentLongClick,
            onTTSClick = onTTSClick,
            showLongPressedMsgPopup = showLongPressedMsgPopup,
            onDismissLongPressedMsgPopup = onDismissLongPressedMsgPopup,
            onClickPopupOption = onClickPopupOption,
            showDeletePop = showDeletePop,
            onDismissDeletePop = onDismissDeletePop,
            onDelete = onDelete,
            modifier = modifier,
        )
    }
}

@Composable
fun MyChatMessage(
    msgRecord: MsgRecord,
    onLongClick: () -> Unit,
    showLongPressedMsgPopup: Boolean,
    onDismissLongPressedMsgPopup: () -> Unit,
    onClickPopupOption: (MsgPopupOptionType) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier
            .padding(start = 56.dp, end = 16.dp)
            .fillMaxWidth(),
        horizontalAlignment = Alignment.End
    ) {
        Text(
            msgRecord.content,
            Modifier
                .myMessageBg()
                .combinedClickable(
                    onClick = {},
                    onLongClick = onLongClick
                )
                .padding(horizontal = 12.dp, vertical = 10.dp),
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                lineHeight = 21.sp,
            )
        )
        if (showLongPressedMsgPopup) {
            MsgSelectedPopup(
                modifier = Modifier.align(Alignment.End),
                onDismiss = onDismissLongPressedMsgPopup,
                onClick = onClickPopupOption
            )
        }
        DTVerticalSpacer(4.dp)
        Text(
            msgRecord.createdDatetime,
            style = MaterialTheme.typography.labelMedium.copy(
                color = White40,
            )
        )
    }
}

@Composable
fun OtherChatMessage(
    aiRole: AIRoleSession?,
    msgRecord: MsgRecord,
    ttsPlaying: Boolean,
    onAvatarClick: () -> Unit,
    onContentClick: () -> Unit,
    onContentLongClick: () -> Unit,
    onTTSClick: () -> Unit,
    showLongPressedMsgPopup: Boolean,
    onDismissLongPressedMsgPopup: () -> Unit,
    onClickPopupOption: (MsgPopupOptionType) -> Unit,
    showDeletePop: Boolean,
    onDismissDeletePop: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier
            .padding(start = 16.dp, end = 24.dp)
            .fillMaxWidth()
    ) {
        AsyncImage(
            aiRole?.avatar?.imageRequest(LocalContext.current),
            null,
            Modifier
                .padding(top = if (msgRecord.msgType == MsgType.Text) 10.dp else 0.dp)
                .size(32.dp)
                .clip(CircleShape)
                .clickable(onClick = onAvatarClick),
            contentScale = ContentScale.Crop
        )
        DTHorizontalSpacer(8.dp)
        Column(Modifier.weight(1f)) {
            Column {
                when (msgRecord.msgType) {
                    MsgType.Text -> {
                        TextMessage(
                            msgRecord = msgRecord,
                            ttsPlaying = ttsPlaying,
                            onClick = onContentClick,
                            onLongClick = onContentLongClick,
                            onTTSClick = onTTSClick,
                        )
                        if (showLongPressedMsgPopup) {
                            MsgSelectedPopup(
                                modifier = Modifier.align(Alignment.End),
                                onDismiss = onDismissLongPressedMsgPopup,
                                onClick = onClickPopupOption
                            )
                        }
                    }

                    MsgType.Image, MsgType.Video -> {
                        ImageMessage(
                            msgRecord = msgRecord,
                            showDeletePop = showDeletePop,
                            onClick = onContentClick,
                            onLongClick = onContentLongClick,
                            onDismissDeletePop = onDismissDeletePop,
                            onDelete = onDelete
                        )
                    }

                    MsgType.Voice -> {}
                    null -> {}
                }
                DTVerticalSpacer(4.dp)
                Text(
                    msgRecord.createdDatetime,
                    Modifier.align(Alignment.End),
                    style = MaterialTheme.typography.labelMedium.copy(
                        color = White40,
                    )
                )
            }
        }
    }
}

@Composable
fun TextMessage(
    msgRecord: MsgRecord,
    ttsPlaying: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    onTTSClick: () -> Unit,
) {
    ConstraintLayout {
        val (ttsRef, msgRef) = createRefs()

        Text(
            msgRecord.content,
            Modifier
                .otherMessageBg()
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick
                )
                .padding(12.dp)
                .constrainAs(msgRef) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top, 10.dp)
                    end.linkTo(parent.end)
                },
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                lineHeight = 21.sp,
            )
        )

        Box(
            Modifier
                .size(46.dp, 20.dp)
                .clip(RoundedCornerShape(10.dp, 10.dp, 10.dp, 0.dp))
                .clickable(onClick = onTTSClick)
                .background(Brush.horizontalGradient(listOf(Color(0xFFF62E8E), Color(0xFFAC1AF0))))
                .constrainAs(ttsRef) {
                    start.linkTo(msgRef.start, 10.dp)
                    top.linkTo(msgRef.top)
                    bottom.linkTo(msgRef.top)
                },
            contentAlignment = Alignment.Center
        ) {
            if (ttsPlaying) {
                MsgLoading(Modifier.fillMaxSize())
            } else {
                Icon(
                    painterResource(R.drawable.ic_chat_msg_tts),
                    null,
                    Modifier.size(31.dp, 11.dp),
                    tint = MaterialTheme.colorScheme.onPrimary
                )
            }
        }
    }
}

@Composable
fun ImageMessage(
    msgRecord: MsgRecord,
    showDeletePop: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    onDismissDeletePop: () -> Unit,
    onDelete: () -> Unit,
) {
    val context = LocalContext.current
    ConstraintLayout {
        val (imageRef, lockRef, unlockRef, playRef, deletePopRef) = createRefs()
        CoilImage(
            imageRequest = {
                msgRecord.content.imageRequest(context)
            },
            Modifier
                .otherMessageBg()
                .padding(10.dp)
                .size(180.dp, 240.dp)
                .clip(RoundedCornerShape(10.dp))
                .combinedClickable(onClick = onClick, onLongClick = onLongClick)
                .constrainAs(imageRef) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)
                },
            component = if (msgRecord.isLocked) rememberImageComponent {
                +BlurTransformationPlugin(radius = 80)
            } else rememberImageComponent {},
        )

        if (msgRecord.isLocked) {
            Icon(
                painter = painterResource(R.drawable.ic_locked),
                null,
                Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(Color(0x1AFFFFFF))
                    .padding(14.dp)
                    .constrainAs(lockRef) {
                        centerVerticallyTo(imageRef)
                        centerHorizontallyTo(imageRef)
                    },
                tint = MaterialTheme.colorScheme.onPrimary
            )
            Box(
                Modifier
                    .width(160.dp)
                    .height(30.dp)
                    .background(Color(0xFFEA557F), RoundedCornerShape(50))
                    .constrainAs(unlockRef) {
                        centerHorizontallyTo(imageRef)
                        bottom.linkTo(imageRef.bottom, 20.dp)
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    stringResource(R.string.click_to_unlock),
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 12.sp,
                    )
                )
            }
        } else if (msgRecord.msgType == MsgType.Video) {
            Image(
                painterResource(R.drawable.ic_video_msg_play),
                null,
                Modifier
                    .size(40.dp)
                    .constrainAs(playRef) {
                        centerVerticallyTo(imageRef)
                        centerHorizontallyTo(imageRef)
                    }
            )
        }

        if (showDeletePop) {
            DeletePopup(
                Modifier.constrainAs(deletePopRef) {
                    start.linkTo(imageRef.start, 153.dp)
                    top.linkTo(imageRef.bottom, (-32).dp)
                },
                onDismiss = onDismissDeletePop,
                onClick = onDelete,
            )
        }
    }
}

@Composable
fun WaitingMessage(aiRole: AIRoleSession?) {
    ConstraintLayout {
        val (avatarRef, loadingRef, datetimeRef) = createRefs()
        AsyncImage(
            aiRole?.avatar?.imageRequest(LocalContext.current),
            null,
            Modifier
                .size(32.dp)
                .clip(CircleShape)
                .constrainAs(avatarRef) {
                    start.linkTo(parent.start, 16.dp)
                    top.linkTo(parent.top)
                },
            contentScale = ContentScale.Crop
        )

        MsgLoading(
            Modifier
                .size(85.dp, 41.dp)
                .otherMessageBg()
                .constrainAs(loadingRef) {
                    start.linkTo(avatarRef.end, 8.dp)
                    top.linkTo(parent.top)
                }
        )

        val time = remember { newLocalDateTimeFMT() }

        Text(
            time,
            Modifier
                .constrainAs(datetimeRef) {
                    end.linkTo(loadingRef.end)
                    top.linkTo(loadingRef.bottom, 4.dp)
                    bottom.linkTo(parent.bottom, 16.dp)
                },
            style = MaterialTheme.typography.labelMedium.copy(
                color = White40,
            )
        )
    }
}

@Composable
fun Modifier.otherMessageBg(): Modifier {
    return this then Modifier
        .clip(RoundedCornerShape(0.dp, 12.dp, 12.dp, 12.dp))
        .background(White12)
}

@Composable
fun Modifier.myMessageBg(): Modifier {
    return this then Modifier
        .clip(RoundedCornerShape(12.dp, 12.dp, 0.dp, 12.dp))
        .background(Color(0xFFCA5A82))
}