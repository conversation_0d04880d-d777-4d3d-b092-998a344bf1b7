package com.solvibe.main.compose.ui.page.main.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.compose.AsyncImage
import com.solvibe.main.R
import com.solvibe.main.bean.BindAccountBean
import com.solvibe.main.compose.theme.SocialBlue
import com.solvibe.main.compose.theme.White
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTLoading
import com.solvibe.main.compose.ui.modifier.click
import com.solvibe.main.compose.ui.page.main.mine.login.LoginPwd
import com.solvibe.main.config.PASSWORD_REGEX
import com.solvibe.main.ext.showToast
import kotlinx.coroutines.flow.SharedFlow

/**
 *creater:linjinhao on 2025/5/26 16:51
 */
@Composable
fun CheckExistAccountPwdRoute(
    info: BindAccountBean,
    viewModel: SvCheckExistAccountPwdViewModel = viewModel(),
    bindAccountViewModel: SvCreateAccountViewModel = viewModel(),
    onBindSuccess: () -> Unit,
    onBindEmail: (info: BindAccountBean) -> Unit,
    onBack: () -> Unit
) {
    val context = LocalContext.current
    var pwdTip by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    val state1 by bindAccountViewModel.state.collectAsState()
    val state2 by viewModel.state.collectAsState()
    val loading by remember(state1, state2) { derivedStateOf { state1 || state2 } }
    CheckExistAccountPwdScreen(
        info = info,
        password = password,
        loading = loading,
        pwdTip = pwdTip,
        onchangePwdTip = { pwdTip = it },
        onPasswordChanged = { newPwd ->
            password = newPwd
            when {
                // 未填写密码，失去焦点时，输入框下方提示“Please enter a password.”
                password.isEmpty() -> {
                    pwdTip = (context.getString(R.string.enter_psw))
                }
                // 密码小于6个字符，失去焦点时，输入框下方提示“Password length must be 6-16 characters.”
                password.length < 6 || password.length > 16 -> {
                    pwdTip = (context.getString(R.string.login_pwd_valid))
                }
                // 仅允许输入字母、数字、和特殊字符（见注册页的符号限制），当输入了不被允许的其他字符时，失去焦点，输入框下方提示“Password only contains alphabets, numbers and special characters.”
                !PASSWORD_REGEX.matches(password) -> {
                    pwdTip = (context.getString(R.string.login_psw_valid))
                }
                // 密码符合情况，则清空提示
                else -> {
                    pwdTip = ""
                }
            }

            password = if (newPwd.length > 16) {
                newPwd.substring(0, 16)
            } else {
                newPwd
            }
        },
        onBack = onBack
    )
    HandleCheckPwdEvent(
        viewModel.checkPasswordEvent,
        onchangePwdTip = { pwdTip = it },
        onCheckPwdSuccess = {
            bindAccountViewModel.createAccount(
                email = info.account,
                password = password,
                state = info.state
            )
        })
    HandleBindAccountEvent(
        info = info,
        bindAccountViewModel.createAccountEvent,
        onBindSuccess = onBindSuccess,
        onchangePwdTip = { pwdTip = it },
        onBindEmail = onBindEmail
    )
}

@Composable
fun CheckExistAccountPwdScreen(
    info: BindAccountBean,
    password: String,
    pwdTip: String,
    loading: Boolean,
    viewModel: SvCheckExistAccountPwdViewModel = viewModel(),
    onchangePwdTip: (String) -> Unit,
    onPasswordChanged: (String) -> Unit,
    onBack: () -> Unit
) {
    val softwareKeyboardController = LocalSoftwareKeyboardController.current
    val localFocusManager = LocalFocusManager.current

    Box(
        modifier = Modifier
            .click {
                localFocusManager.clearFocus()
                softwareKeyboardController?.hide()
            }
            .fillMaxSize()) {
        Image(
            painterResource(R.drawable.bg_common_bg),
            contentDescription = "background",
            modifier = Modifier.fillMaxSize()
        )
        val scrollState = rememberScrollState()
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .fillMaxSize()
                .verticalScroll(scrollState)
        ) {
            Image(
                painterResource(R.drawable.ic_top_bar_back),
                contentDescription = "iconBack",
                modifier = Modifier
                    .padding(start = 20.dp)
                    .click { onBack() }
                    .size(30.dp)
            )
            Text(
                stringResource(R.string.bind_account_id),
                style = TextStyle(fontSize = 16.sp, color = White, fontWeight = FontWeight.W600),
                modifier = Modifier
                    .padding(start = 20.dp, end = 20.dp)
                    .fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(30.dp))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Spacer(modifier = Modifier.width(16.dp))
                AsyncImage(
                    model = info.avatar,
                    contentDescription = "avatar",
                    modifier = Modifier
                        .size(36.dp)
                        .clip(RoundedCornerShape(6.dp))
                )
                Spacer(modifier = Modifier.width(16.dp))
                Image(
                    painterResource(R.drawable.ic_google),
                    contentDescription = "avatar",
                    modifier = Modifier.size(36.dp)
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = info.displayName,
                    style = TextStyle(
                        fontSize = 18.sp,
                        color = White,
                        fontWeight = FontWeight.W700
                    ),
                )
            }
            Spacer(modifier = Modifier.height(12.dp))
            Text(
                stringResource(R.string.to_ensure_the_security_of_your_account_please_enter_the_password_of_the_account_email_to_confirm_the_binding),
                style = TextStyle(fontSize = 14.sp, color = White, fontWeight = FontWeight.W700),
                modifier = Modifier
                    .padding(horizontal = 20.dp)
                    .fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = info.account,
                style = TextStyle(fontSize = 18.sp, color = White, fontWeight = FontWeight.W700),
                modifier = Modifier
                    .padding(start = 20.dp, end = 20.dp)
                    .fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(7.dp))
            LoginPwd(
                modifier = Modifier.padding(horizontal = 20.dp),
                pwd = password,
                pwdTip = pwdTip,
                onchangePwdTip = { onchangePwdTip(it) },
                onPasswordChanged = {
                    onPasswordChanged(it)
                })

            Spacer(modifier = Modifier.height(116.dp))
            DTButton(
                R.string.next,
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp)
                    .fillMaxSize()
                    .height(46.dp),
                enable = checkAccountPassword(password),
                containerColor = SocialBlue,
                contentColor = White,
                disabledContainerColor = colorResource(R.color.color_4b4b51),
                disabledContentColor = colorResource(R.color.color_8A8C91)
            ) {
                localFocusManager.clearFocus()
                viewModel.checkAccountPassword(email = info.account, password = password)
            }

            Spacer(modifier = Modifier.height(20.dp))
        }

        DTLoading(loading) { }
    }
}

@Composable
private fun HandleCheckPwdEvent(
    event: SharedFlow<CheckPasswordEvent>,
    onCheckPwdSuccess: () -> Unit,
    onchangePwdTip: (String) -> Unit
) {
    val context = LocalContext.current

    LaunchedEffect(event) {
        event.collect {
            when (it) {
                CheckPasswordEvent.CommonError -> {
                    showToast(context.getString(R.string.network_error))
                }

                CheckPasswordEvent.PasswordError -> {
                    onchangePwdTip(context.getString(R.string.please_enter_the_correct_password))
                }

                CheckPasswordEvent.PasswordFormatError -> {
                    onchangePwdTip(context.getString(R.string.login_psw_valid))
                }

                CheckPasswordEvent.PasswordLengthError -> {
                    onchangePwdTip(context.getString(R.string.login_psw_length_valid))
                }

                CheckPasswordEvent.Success -> {
                    onCheckPwdSuccess()
                }
            }
        }
    }
}

@Composable
private fun HandleBindAccountEvent(
    info: BindAccountBean,
    event: SharedFlow<CreateAccountEvent>,
    onBindSuccess: () -> Unit,
    onchangePwdTip: (String) -> Unit,
    onBindEmail: (info: BindAccountBean) -> Unit
) {
    val context = LocalContext.current
    LaunchedEffect(event) {
        event.collect {
            when (it) {
                CreateAccountEvent.BindFail, CreateAccountEvent.CommonError -> {
                    showToast(context.getString(R.string.network_error))
                }

                CreateAccountEvent.Success -> {
                    onBindSuccess()
                }

                CreateAccountEvent.FaceBookHasBind -> {
                    showToast(context.getString(R.string.the_current_facebook_account_is_already_bound_to_another_member_account))
                }

                CreateAccountEvent.GoogleHasBind -> {
                    showToast(context.getString(R.string.the_current_google_account_is_already_bound_to_another_member_account))
                }


                CreateAccountEvent.HasBindFaceBook -> {
                    showToast(context.getString(R.string.the_member_account_has_been_linked_to_another_facebook_account))
                    onBindEmail(info)
                }

                CreateAccountEvent.HasBindGoogle -> {
                    showToast(context.getString(R.string.the_member_account_has_been_linked_to_another_google_account))
                    onBindEmail(info)
                }

                CreateAccountEvent.PasswordError -> {
                    onchangePwdTip(context.getString(R.string.please_enter_the_correct_password))
                }

                CreateAccountEvent.PasswordFormatError -> {
                    onchangePwdTip(context.getString(R.string.login_psw_valid))
                }

                CreateAccountEvent.PasswordLengthError -> {
                    onchangePwdTip(context.getString(R.string.login_psw_length_valid))
                }
            }
        }
    }
}