package com.solvibe.main.compose.ui.page.chat

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White8
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTButtonTextStyle
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.config.Constant
import com.solvibe.main.config.ReportEventUtils
import kotlin.math.max

@Composable
fun AIReplyView(
    viewModel: ChatViewModel = viewModel(),
    onClickKeyboard: () -> Unit,
    onGotoBuy: () -> Unit
) {
    val messages by rememberUpdatedState(viewModel.aiReplyState.messages)
    var showHowToUse by remember { mutableStateOf(false) }
    val context = LocalContext.current

    Column(Modifier.padding(horizontal = 20.dp)) {
        LazyColumn(verticalArrangement = Arrangement.spacedBy(12.dp)) {
            if (!showHowToUse) {
                itemsIndexed(
                    messages,
                    key = { i, _ -> i },
                    contentType = { _, _ -> "MESSAGE" }
                ) { _, it ->
                    AIReplyOption(
                        it,
                        viewModel.isVip,
                        viewModel.aiReplyState.aiReplyLastCount,
                        onClick = { text ->
                            viewModel.onTextInput(text)
                            viewModel.sendTextMsg(context, true, onGotoBuy = onGotoBuy)
                        },
                        onGotoBuy = onGotoBuy
                    )
                }
            }
        }
        DTVerticalSpacer(16.dp)
        AIRoleToolsBar(
            viewModel.isVip,
            viewModel.aiReplyState.aiReplyLastCount,
            onCancel = viewModel::foldAiReply,
            onClickKeyboard = onClickKeyboard,
        )
    }
}

@Composable
private fun AIReplyOption(
    text: String?,
    isVip: Boolean,
    aiReplyLastCount: Int,
    onClick: (String) -> Unit,
    onGotoBuy: () -> Unit
) {
    if ((!isVip) && aiReplyLastCount < 0) {
        NoFreeTrialsView(onGotoBuy = onGotoBuy)
        return
    }

    Row(
        Modifier
            .fillMaxWidth()
            .heightIn(min = 52.dp)
            .animateContentSize(tween())
            .clip(RoundedCornerShape(12.dp))
            .border(0.5.dp, Color(0xFF4B4B4B), RoundedCornerShape(12.dp))
            .clickable(enabled = text != null) {
                onClick(text ?: "")
            }
            .background(White8)
            .padding(18.dp, if (text.isNullOrEmpty()) 0.dp else 10.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (text.isNullOrEmpty()) {
            AiReplyPlaceholder()
        } else {
            Icon(
                painterResource(R.drawable.ic_ai_tips_item),
                null,
                Modifier.size(22.dp),
                Color(0xFFE7E7E7)
            )
            DTHorizontalSpacer(8.dp)
            Text(
                text,
                Modifier.weight(1f),
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.colorScheme.onPrimary,
                    lineHeight = 16.sp,
                )
            )
        }
    }
}

@Composable
private fun NoFreeTrialsView(onGotoBuy: () -> Unit) {
    Box(Modifier.height(52.dp)) {
        Image(painterResource(R.drawable.bg_ai_reply_no_free_bg), null, Modifier.fillMaxSize())

        Row(Modifier.fillMaxSize(), verticalAlignment = Alignment.CenterVertically) {
            DTHorizontalSpacer(24.dp)
            Image(
                painterResource(R.drawable.image_ai_reply_vip),
                null,
                Modifier.size(20.dp, 22.dp)
            )
            DTHorizontalSpacer(12.dp)
            Text(
                stringResource(R.string.no_free_trials),
                Modifier.weight(1f),
                style = MaterialTheme.typography.headlineLarge.copy(
                    brush = Brush.horizontalGradient(
                        listOf(
                            Color(0xFFF8D8D3), Color(0xFFFFF0EF),
                        )
                    ),
                    fontSize = 16.sp,
                    lineHeight = 16.sp,
                    fontStyle = FontStyle.Italic
                )
            )
            DTButton(
                stringResource(R.string.unlock_now),
                Brush.horizontalGradient(
                    listOf(
                        Color(0xFFFF754C),
                        Color(0xFFFF5A6B),
                        Color(0xFFF34E9C),
                    )
                ),
                Modifier
                    .width(96.dp)
                    .height(32.dp),
                textStyle = DTButtonTextStyle.copy(
                    fontSize = 12.sp
                ),
                onClick = {
                    ReportEventUtils.onEvent(
                        Constant.Purchase_page,
                        mapOf(Constant.Purchase_page to "Inspiration_prompt")
                    )
                    onGotoBuy()
                }
            )
            DTHorizontalSpacer(16.dp)
        }
    }
}

@Composable
private fun AiReplyPlaceholder() {
    Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        val composition by rememberLottieComposition(LottieCompositionSpec.Asset("animations/waitmessage.json"))
        LottieAnimation(
            composition,
            Modifier.size(52.dp),
            iterations = LottieConstants.IterateForever
        )
    }
}

private val freeCountEnoughTipsTextStyle = TextStyle.Default.copy(
    brush = Brush.horizontalGradient(
        listOf(
            Color(0xFF8C63ED),
            Color(0xFFB472E8),
            Color(0xFFEE70A1),
            Color(0xFFFDABC7),
            Color(0xFFFFC3C9),
        )
    )
)

private val freeCountNotEnoughTipsTextStyle = TextStyle.Default.copy(
    color = Color(0xFFB7B7B7)
)

@Composable
private fun AIRoleToolsBar(
    isVip: Boolean,
    aiReplyLastCount: Int,
    onCancel: () -> Unit,
    onClickKeyboard: () -> Unit
) {
    val aiReplyCountEnough = aiReplyLastCount > 0
    Row(
        Modifier.height(32.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painterResource(R.drawable.ic_keyboard),
            null,
            Modifier
                .size(26.dp)
                .clip(CircleShape)
                .clickable(onClick = onClickKeyboard)
                .padding(3.dp),
            MaterialTheme.colorScheme.onPrimary
        )
        Spacer(Modifier.weight(1f))
        if (!isVip) {
            Image(
                painterResource(if (aiReplyCountEnough) R.drawable.ic_ai_reply_enabled else R.drawable.ic_ai_reply_disable),
                null,
                Modifier
                    .width(20.dp)
                    .height(18.dp)
            )
            DTHorizontalSpacer(5.dp)
            Text(
                stringResource(R.string.number_of_free_trials, max(aiReplyLastCount, 0)),
                style = (if (aiReplyCountEnough) freeCountEnoughTipsTextStyle else freeCountNotEnoughTipsTextStyle).copy(
                    fontSize = 13.sp,
                    fontWeight = FontWeight.W600,
                    lineHeight = 16.sp
                )
            )
            Spacer(Modifier.weight(1f))
        }
        Icon(
            painterResource(R.drawable.ic_arrow_down),
            null,
            Modifier
                .size(26.dp)
                .clip(CircleShape)
                .clickable(onClick = onCancel)
                .padding(3.dp),
            MaterialTheme.colorScheme.onPrimary
        )
    }
}