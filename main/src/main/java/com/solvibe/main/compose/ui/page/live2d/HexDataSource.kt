package com.solvibe.main.compose.ui.page.live2d

import android.net.Uri
import androidx.annotation.OptIn
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.BaseDataSource
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DataSpec
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.source.MediaSource
import com.solvibe.utils.ext.loge
import java.io.IOException

/**
 * 一个用于处理HEX字符串作为音频源的自定义DataSource。
 * 它将HEX字符串转换为字节数组，然后ExoPlayer可以读取这些字节。
 */
@OptIn(UnstableApi::class)
class HexDataSource : BaseDataSource(true) {

    private var uri: Uri? = null
    private var data: ByteArray? = null
    private var bytesRemaining: Int = 0
    private var readPosition: Int = 0
    private var opened = false

    override fun getUri(): Uri? = uri

    @Throws(IOException::class)
    override fun open(dataSpec: DataSpec): Long {
        uri = dataSpec.uri
        val hexString = uri?.schemeSpecificPart?.substring(2)
        if (hexString.isNullOrEmpty()) {
            throw IOException("Hex string not found in Uri.")
        }

        try {
            // 将HEX字符串转换为字节数组
            data = hexString.decodeHex()
            bytesRemaining = data!!.size
            readPosition = 0
            opened = true
            transferStarted(dataSpec)
            return bytesRemaining.toLong()
        } catch (e: Exception) {
            // 捕获异常并打印日志，帮助我们定位问题
            loge("Failed to decode hex string: $hexString")
            throw IOException("Failed to decode hex string.", e)
        }
    }

    @Throws(IOException::class)
    override fun read(buffer: ByteArray, offset: Int, length: Int): Int {
        if (bytesRemaining == 0) {
            return C.RESULT_END_OF_INPUT
        }

        val bytesToRead = kotlin.comparisons.minOf(bytesRemaining, length)
        System.arraycopy(data!!, readPosition, buffer, offset, bytesToRead)
        readPosition += bytesToRead
        bytesRemaining -= bytesToRead
        bytesTransferred(bytesToRead)
        return bytesToRead
    }

    @Throws(IOException::class)
    override fun close() {
        if (opened) {
            opened = false
            transferEnded()
            data = null
            uri = null
        }
    }

    /**
     * 伴生对象，用于创建HexDataSource的工厂。
     */
    companion object {
        fun Factory(): DataSource.Factory {
            return DataSource.Factory { HexDataSource() }
        }

        fun create(hexString: String, mediaId: String): MediaSource {
            // 创建一个带有我们自定义的HexDataSource工厂的MediaSourceFactory
            val dataSourceFactory: DataSource.Factory = Factory()
            val mediaSourceFactory = DefaultMediaSourceFactory(dataSourceFactory)

            // 构建一个包含HEX数据的Uri
            val uri = "hexdata://$hexString"

            // 使用自定义的MediaItem来设置mediaId
            val mediaItem = MediaItem.Builder()
                .setUri(uri)
                .setMediaId(mediaId) // 设置唯一的mediaId
                .build()

            // 创建并返回MediaSource
            return mediaSourceFactory.createMediaSource(mediaItem)
        }
    }
}

/**
 * 将HEX字符串转换为字节数组的扩展函数。
 */
fun String.decodeHex(): ByteArray {
    check(length % 2 == 0) { "Must have an even length" }
    return chunked(2)
        .map { it.toInt(16).toByte() }
        .toByteArray()
}