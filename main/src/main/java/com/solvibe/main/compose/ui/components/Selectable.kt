package com.solvibe.main.compose.ui.components

import androidx.annotation.DrawableRes
import androidx.compose.runtime.Stable
import androidx.compose.ui.graphics.Color

@Stable
data class SelectableDrawable(
    @DrawableRes
    val enableDrawable: Int,
    @DrawableRes
    val disableDrawable: Int = enableDrawable,
)

@Stable
data class SelectableColor(
    val enableColor: Color,
    val disableColor: Color,
)