package com.solvibe.main.compose.navigation

import com.solvibe.main.bean.BindAccountBean
import kotlinx.serialization.Serializable

sealed interface MainRoute {
    @Serializable
    data object UserInfoInput : MainRoute

    @Serializable
    data class ZoomImage(val url: String) : MainRoute

    @Serializable
    data class VideoPlay(val url: String, val width: Int, val height: Int) : MainRoute

    @Serializable
    data object Login : MainRoute

    @Serializable
    data object SignUp : MainRoute

    @Serializable
    data object UserInfo : MainRoute

    @Serializable
    data object Mine : MainRoute

    @Serializable
    data object ForgetPassword : MainRoute

    @Serializable
    data object Product : MainRoute

    @Serializable
    data object Language : MainRoute

    @Serializable
    data object MyOrder : MainRoute

    @Serializable
    data class GooglePaySuccess(val bean: String, val needBackToMain: Boolean = false) : MainRoute

    @Serializable
    data object AboutMe : MainRoute

    @Serializable
    data object Feedback : MainRoute

    @Serializable
    data object GoogleLogin : MainRoute

    @Serializable
    data class BindEmail(val data: BindAccountBean) : MainRoute

    @Serializable
    data class CheckPassword(val data: BindAccountBean) : MainRoute

    @Serializable
    data class CreateAccount(val data: BindAccountBean) : MainRoute

    @Serializable
    data class BindAnotherEmail(val data: BindAccountBean) : MainRoute

    @Serializable
    data class ChangeAccountInfo(val type: String) : MainRoute

    @Serializable
    data object Live2DChatPage : MainRoute

    @Serializable
    data object NewProduct : MainRoute

    @Serializable
    data object ChatRecord : MainRoute
}