package com.solvibe.main.compose.ui.page.feedback

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White10
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTButtonTextStyle
import com.solvibe.main.compose.ui.components.DTPage
import com.solvibe.main.compose.ui.components.DTTextFieldNoBorder
import com.solvibe.main.compose.ui.components.DTTextFieldTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.components.SVDialog
import com.solvibe.main.compose.ui.components.SVTopBar
import com.solvibe.main.ext.showToast

@Composable
fun FeedbackPage(
    onBack: () -> Unit,
    viewModel: FeedbackViewModel = viewModel()
) {
    val context = LocalContext.current

    BackHandler {
        viewModel.showBackDialog(true)
    }

    DTPage(
        Modifier.background(Color(0xFF13111B)),
        loading = viewModel.loading,
        onDismissLoading = {
            viewModel.showLoading(false)
        }
    ) {
        Column(
            Modifier
                .statusBarsPadding()
                .navigationBarsPadding()
                .fillMaxSize()
        ) {
            SVTopBar(R.string.feedback) {
                viewModel.showBackDialog(true)
            }
            DTVerticalSpacer(8.dp)
            Title()
            DTVerticalSpacer(8.dp)
            Tips()
            DTVerticalSpacer(30.dp)
            FeedbackContent(viewModel.feedback, viewModel::onInput)
            DTVerticalSpacer(24.dp)
            Email(viewModel.email, viewModel::onInputEmail)
            DTVerticalSpacer(60.dp)
            SubmitBtn(viewModel.feedback.isNotBlank() && viewModel.email.isNotBlank()) {
                viewModel.submit { code ->
                    if (code == null) {
                        return@submit showToast(context.getString(R.string.send_failed))
                    }
                    if (code == 0) {
                        return@submit showToast(context.getString(R.string.login_email_valid))
                    }
                    showToast(context.getString(R.string.report_successful))
                    onBack()
                }
            }
        }
    }

    if (viewModel.showBackDialog) {
        SVDialog(
            title = R.string.discard_changes_and_go_back,
            cancelText = R.string.cancel,
            confirmText = R.string.Delete,
            onConfirm = {
                viewModel.showBackDialog(false)
                onBack()
            },
            onCancel = {
                viewModel.showBackDialog(false)
            }
        )
    }
}

@Composable
private fun Title() {
    Text(
        stringResource(R.string.anything_you_want_to_say),
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.headlineLarge.copy(
            color = Color(0xFFDBDBDB),
            lineHeight = 32.sp
        )
    )
}

@Composable
private fun Tips() {
    Text(
        stringResource(R.string.we_are_always_looking_for_inspirations),
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.bodySmall.copy(
            color = Color(0xFF6C6C6C),
            lineHeight = 20.sp
        )
    )
}

@Composable
private fun FeedbackContent(feedback: String, onInput: (String) -> Unit) {
    DTTextFieldNoBorder(
        value = feedback,
        onValueChange = onInput,
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(180.dp)
            .background(White10, RoundedCornerShape(12.dp)),
        placeholder = stringResource(R.string.anything_you_want_to_say),
        textStyle = DTTextFieldTextStyle.copy(
            fontSize = 15.sp,
            lineHeight = 22.sp
        ),
        singleLine = false,
        verticalAlignment = Alignment.Top,
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 15.dp)
    )
}

@Composable
fun Email(email: String, onInput: (String) -> Unit) {
    DTTextFieldNoBorder(
        value = email,
        onValueChange = onInput,
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(52.dp)
            .background(White10, RoundedCornerShape(12.dp)),
        placeholder = stringResource(R.string.email),
        textStyle = DTTextFieldTextStyle.copy(
            fontSize = 15.sp,
            lineHeight = 22.sp
        ),
        keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Email)
    )
}

@Composable
fun SubmitBtn(enable: Boolean, onClick: () -> Unit) {
    val brush = remember(enable) {
        Brush.horizontalGradient(
            if (enable) listOf(Color(0xFFBE4EFF), Color(0xFFF343A1), Color(0xFFFF5476))
            else listOf(Color(0xFF4B4B51), Color(0xFF4B4B51))
        )
    }
    DTButton(
        text = stringResource(R.string.submit),
        brush = brush,
        enable = enable,
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(44.dp),
        textStyle = DTButtonTextStyle.copy(
            color = if (enable) MaterialTheme.colorScheme.onPrimary else Color(0xFF8A8C91)
        ),
        onClick = onClick
    )
}