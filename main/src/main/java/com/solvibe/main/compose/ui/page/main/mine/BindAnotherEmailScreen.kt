package com.solvibe.main.compose.ui.page.main.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.solvibe.main.R
import com.solvibe.main.bean.BindAccountBean
import com.solvibe.main.compose.theme.SocialBlue
import com.solvibe.main.compose.theme.White
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.modifier.click
import java.net.URLEncoder

/**
 *creater:linjinhao on 2025/5/26 16:41
 */

@Composable
fun BindAnotherEmailRoute(
    info: BindAccountBean,
    onBindEmail: (info: BindAccountBean) -> Unit,
    onBack: () -> Unit
) {

    BindAnotherEmailScreen(
        info = info,
        onBindEmail = { onBindEmail(it.copy(avatar = URLEncoder.encode(info.avatar))) },
        onBack = { onBack() }
    )
}

@Composable
fun BindAnotherEmailScreen(
    info: BindAccountBean,
    onBindEmail: (info: BindAccountBean) -> Unit,
    onBack: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Image(
            painterResource(R.drawable.bg_common_bg),
            contentDescription = "background",
            modifier = Modifier.fillMaxSize()
        )
        val scrollState = rememberScrollState()
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .fillMaxSize()
                .verticalScroll(scrollState)
        ) {
            Image(
                painterResource(R.drawable.ic_top_bar_back),
                contentDescription = "iconBack",
                modifier = Modifier
                    .padding(start = 20.dp)
                    .click { onBack() }
                    .size(30.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                stringResource(R.string.bind_account_id),
                style = TextStyle(fontSize = 16.sp, color = White, fontWeight = FontWeight.W600),
                modifier = Modifier
                    .padding(start = 20.dp, end = 20.dp)
                    .fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(30.dp))
            Row(verticalAlignment = Alignment.CenterVertically) {
                Spacer(modifier = Modifier.width(16.dp))
                AsyncImage(
                    model = info.avatar,
                    contentDescription = "avatar",
                    modifier = Modifier
                        .size(36.dp)
                        .clip(RoundedCornerShape(6.dp))
                )
                Spacer(modifier = Modifier.width(16.dp))
                Image(
                    painterResource(R.drawable.ic_google),
                    contentDescription = "avatar",
                    modifier = Modifier.size(36.dp)
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = info.displayName,
                    style = TextStyle(
                        fontSize = 18.sp,
                        color = White,
                        fontWeight = FontWeight.W700
                    ),
                )
            }
            Spacer(modifier = Modifier.height(12.dp))

            Text(
                stringResource(R.string.checked_that_the_email_has_bound_to_another_google),
                style = TextStyle(fontSize = 14.sp, color = White, fontWeight = FontWeight.W700),
                modifier = Modifier
                    .padding(start = 20.dp, end = 20.dp)
                    .fillMaxWidth()
            )
            Text(
                text = info.account,
                style = TextStyle(fontSize = 18.sp, color = White, fontWeight = FontWeight.W600),
                modifier = Modifier
                    .padding(start = 20.dp, end = 20.dp)
                    .fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(72.dp))

            DTButton(
                R.string.bind_another_email,
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp)
                    .fillMaxSize()
                    .height(46.dp),
                enable = true,
                containerColor = SocialBlue,
                contentColor = White,
                disabledContainerColor = colorResource(R.color.color_4b4b51),
                disabledContentColor = colorResource(R.color.color_8A8C91)
            ) {
                onBindEmail(info)
            }
            Spacer(modifier = Modifier.height(20.dp))
        }
    }
}