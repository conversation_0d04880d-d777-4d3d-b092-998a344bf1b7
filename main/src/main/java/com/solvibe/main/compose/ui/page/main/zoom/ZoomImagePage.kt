package com.solvibe.main.compose.ui.page.main.zoom

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.solvibe.main.R
import com.solvibe.main.compose.navigation.MainRoute
import me.saket.telephoto.zoomable.rememberZoomableState
import me.saket.telephoto.zoomable.zoomable

@Composable
fun ZoomImagePage(onBack: () -> Unit, route: MainRoute.ZoomImage) {
    Box(Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        AsyncImage(
            model = route.url,
            null,
            Modifier
                .zoomable(rememberZoomableState())
                .fillMaxSize()
        )

        Image(
            painterResource(R.drawable.ic_back2),
            null,
            Modifier
                .statusBarsPadding()
                .padding(start = 20.dp, top = 10.dp)
                .size(36.dp)
                .clip(CircleShape)
                .clickable(onClick = onBack)
                .align(Alignment.TopStart),
        )
    }
}