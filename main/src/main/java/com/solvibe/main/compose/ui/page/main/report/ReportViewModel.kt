package com.solvibe.main.compose.ui.page.main.report

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.solvibe.Membership
import com.solvibe.main.compose.navigation.MainRoute
import com.solvibe.main.compose.utils.ILoadingState
import com.solvibe.main.compose.utils.loadingState
import com.solvibe.main.ext.showToast
import com.solvibe.main.repo.AppRepo
import com.solvibe.main.utils.NetWorkManager
import kotlinx.coroutines.launch

class ReportViewModel(
    handle: SavedStateHandle
) : ViewModel(),
    ILoadingState by loadingState() {
    val route = handle.toRoute<MainRoute.Report>()

    private val appRepo = AppRepo()

    var showBackDialog by mutableStateOf(false)
        private set
    var roleName by mutableStateOf(route.roleName)
        private set
    var reportContent by mutableStateOf("")
        private set
    var email by mutableStateOf(Membership.getEmail().orEmpty())
        private set

    fun showBackDialog(show: Boolean) {
        showBackDialog = show
    }

    fun onRoleNameChanged(content: String) {
        roleName = content
    }

    fun onReportContentChanged(content: String) {
        reportContent = content
    }

    fun onEmailChanged(content: String) {
        email = content
    }

    fun canSubmit() = roleName.isNotBlank() && reportContent.isNotBlank() && email.isNotBlank()

    fun submit(onResp: (Int?) -> Unit) {
        if (!NetWorkManager.isNetworkConnectedSystem()) {
            showToast("Failed to send, please try again")
            return
        }
        viewModelScope.launch {
            showLoading(true)
            val content = "$roleName : $reportContent"
            val resp = appRepo.submitFeedback(email, "Report", content)
            onResp(resp?.code)
            showLoading(false)
        }
    }
}