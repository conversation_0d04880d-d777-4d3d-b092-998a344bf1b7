package com.solvibe.main.compose.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsDraggedAsState
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun DTSlider(
    value: Float,
    draggedValue: Float,
    onValueChange: (value: Float) -> Unit,
    onValueChangeFinished: (() -> Unit)? = null,
    height: Dp = 6.dp,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    trackColor: Color = Color(0x33FFFFFF),
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() }
) {
    val isPressed by interactionSource.collectIsPressedAsState()
    val isDragged by interactionSource.collectIsDraggedAsState()
    val isInteracting = remember(isPressed, isDragged) { isPressed || isDragged }

    val displayValue = remember(value, draggedValue, isInteracting) {
        if (isInteracting) draggedValue else value
    }

    DTSlider(
        value = displayValue,
        onValueChange = {
            onValueChange(it)
        },
        onValueChangeFinished = onValueChangeFinished,
        height = height,
        modifier = modifier,
        color = color,
        trackColor = trackColor,
        interactionSource = interactionSource
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DTSlider(
    value: Float,
    onValueChange: (Float) -> Unit,
    onValueChangeFinished: (() -> Unit)? = null,
    height: Dp = 6.dp,
    modifier: Modifier = Modifier,
    color: Color = Color(0xFF9568F7),
    trackColor: Color = Color(0x33FFFFFF),
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() }
) {
    val colors = SliderDefaults.colors(
        thumbColor = color,
        activeTrackColor = color,
        inactiveTrackColor = trackColor
    )
    Slider(
        value = value,
        onValueChange = onValueChange,
        onValueChangeFinished = onValueChangeFinished,
        modifier = modifier.height(height),
        interactionSource = interactionSource,
        thumb = {
            Box(Modifier.fillMaxHeight(), contentAlignment = Alignment.CenterStart) {
                Box(
                    Modifier
                        .size(12.dp)
                        .clip(CircleShape)
                        .background(color)
                )
            }
        },
        track = {
            SliderDefaults.Track(
                colors = colors,
                sliderState = it,
                modifier = Modifier.height(6.dp),
                drawStopIndicator = {},
                thumbTrackGapSize = 0.dp
            )
        }
    )
}