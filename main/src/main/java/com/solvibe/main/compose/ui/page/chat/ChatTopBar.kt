package com.solvibe.main.compose.ui.page.chat

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.solvibe.main.R

@Composable
fun ChatTopBar(
    name: String,
    onBackClick: () -> Unit,
    onReportClick: () -> Unit
) {
    ConstraintLayout(
        Modifier
            .fillMaxWidth()
            .statusBarsPadding()
            .height(56.dp)
    ) {
        val (backRef, nameRef, reportRef, lineRef) = createRefs()

        Image(
            painterResource(R.drawable.ic_top_bar_back),
            null,
            Modifier
                .size(30.dp)
                .constrainAs(backRef) {
                    start.linkTo(parent.start, 16.dp)
                    centerVerticallyTo(parent)
                }
                .clip(CircleShape)
                .clickable(onClick = onBackClick),
        )
        Text(
            name,
            Modifier.constrainAs(nameRef) {
                start.linkTo(backRef.end, 10.dp)
                centerVerticallyTo(parent)
            },
            style = MaterialTheme.typography.headlineMedium.copy(
                color = MaterialTheme.colorScheme.onPrimary,
            )
        )
        Image(
            painterResource(R.drawable.ic_report),
            null,
            Modifier
                .size(34.dp)
                .constrainAs(reportRef) {
                    end.linkTo(parent.end, 16.dp)
                    centerVerticallyTo(parent)
                }
                .clip(CircleShape)
                .clickable(onClick = onReportClick)
        )
        HorizontalDivider(
            Modifier
                .fillMaxWidth()
                .constrainAs(lineRef) {
                    bottom.linkTo(parent.bottom)
                },
            0.5.dp,
            Color(0xFF323436)
        )
    }
}