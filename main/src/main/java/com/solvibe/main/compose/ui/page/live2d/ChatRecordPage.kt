package com.solvibe.main.compose.ui.page.live2d

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.solvibe.main.App
import com.solvibe.main.R
import com.solvibe.main.compose.theme.Black60
import com.solvibe.main.compose.ui.components.DTPage

@Composable
fun ChatRecordPage(
    onBack: () -> Unit
) {
    val appViewModel = App.viewModel
    val listState = rememberLazyListState()
    val messages by appViewModel.messageState.messagesFlow.collectAsStateWithLifecycle()

    DTPage(background = R.drawable.bg_chat_record) {
        Column(
            Modifier
                .fillMaxSize()
                .background(Black60)
                .statusBarsPadding()
                .navigationBarsPadding()
        ) {
            LazyColumn(
                Modifier.fillMaxSize(),
                state = listState,
                reverseLayout = true,
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
            ) {
                itemsIndexed(messages, key = { _, it -> it.id }) { index, msg ->
                    ChatMsg(msg, false)
                }
                item("LOADING_MORE") {
                    LaunchedEffect(Unit) {
                        appViewModel.loadMoreMsg()
                    }
                }
            }
        }

        Icon(
            painterResource(R.drawable.ic_msg_history_close),
            null,
            Modifier
                .align(Alignment.TopEnd)
                .statusBarsPadding()
                .padding(top = 16.dp, end = 16.dp)
                .size(24.dp)
                .clip(CircleShape)
                .clickable(onClick = onBack),
            tint = Color.Unspecified
        )
    }
}