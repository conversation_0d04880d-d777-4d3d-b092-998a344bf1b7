package com.solvibe.main.compose.ui.page.live2d

import androidx.annotation.OptIn
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.ExoPlayer
import com.solvibe.Membership
import com.solvibe.Membership.guestRepo
import com.solvibe.character.net.ws.WSManager
import com.solvibe.character.net.ws.WsData
import com.solvibe.live2d.LAppDelegate
import com.solvibe.live2d.LAppLive2DManager
import com.solvibe.main.App
import com.solvibe.main.R
import com.solvibe.main.bean.AskResp
import com.solvibe.main.config.Constant
import com.solvibe.main.ext.stateInViewModel
import com.solvibe.main.state.getAutoPlayEnable
import com.solvibe.main.utils.getString
import com.solvibe.utils.ext.loge
import com.solvibe.utils.ext.logv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File

@OptIn(UnstableApi::class)
class Live2DViewModel : ViewModel() {
    private val account = Membership.account
    private val permissionRepo = Membership.permissionRepo
    val memberVipStateFlow = Membership.memberVipStateFlow
    val guestVipStateFlow = Membership.guestVipStateFlow
    private var refreshMemberInfoJob: Job? = null
    private var refreshGuestInfoJob: Job? = null

    /**
     * 用户基本信息
     */
    val userFlow = account.userFlow.stateInViewModel()

    /**
     * 游客 信息
     */
    val guestFlow = guestRepo.guestInfoFlow.stateInViewModel()

    var inputText by mutableStateOf("")
        private set
    var showChatMore by mutableStateOf(false)
        private set
    var showCharacterSettings by mutableStateOf(false)
        private set
    var showSelectLLMModel by mutableStateOf(false)
        private set
    var showSelectTone by mutableStateOf(false)
        private set
    var deletingSession by mutableStateOf<Long?>(null)
    var showDeleteSessionDialog by mutableStateOf<Long?>(null)
        private set
    var showWaitingMessage by mutableStateOf(false)
        private set
    var showResetChatDialog by mutableStateOf(false)
        private set
    var showTarotFortuneDialog by mutableStateOf(false)
        private set


    val tarotFortuneTypes = listOf(
        getString(R.string.love),
        getString(R.string.career),
        getString(R.string.emotion),
        getString(R.string.choice),
        getString(R.string.other)
    )

    private var isPlayerPrepared = false
    private val voicePlayer by lazy {
        // 可选：自定义 LoadControl 以提升预加载能力
        val loadControl = DefaultLoadControl.Builder()
            .setBufferDurationsMs(
                20_000, // minBufferMs: 20秒
                60_000, // maxBufferMs: 60秒
                500,    // bufferForPlaybackMs: 0.5秒
                500     // bufferForPlaybackAfterRebufferMs: 0.5秒
            )
            .build()
        val player = ExoPlayer.Builder(App.getInstance())
            .setLoadControl(loadControl)
            .build()
        player.addListener(object : Player.Listener {
            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                LAppLive2DManager.instance.getModel()?.isSay = isPlaying
            }

            override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                // 打印当前播放的mediaId
                mediaItem?.mediaId?.let { mediaId ->
                    showLog("当前正在播放的 mediaId: $mediaId")
                }
            }

            override fun onPlayerError(error: PlaybackException) {
                super.onPlayerError(error)
                loge("ExoPlayer 播放错误: ${error.stackTraceToString()}")
                player.clearMediaItems()
                isPlayerPrepared = false
            }

            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_READY -> {
                        showLog("ExoPlayer 状态: 准备就绪")
                    }

                    Player.STATE_BUFFERING -> {
                        showLog("ExoPlayer 状态: 缓冲中")
                    }

                    Player.STATE_ENDED -> {
                        showLog("ExoPlayer 状态: 播放结束")
                        player.clearMediaItems()
                    }

                    Player.STATE_IDLE -> {
                        showLog("ExoPlayer 状态: 空闲")
                    }
                }
            }
        })
        player.playWhenReady = true
        player
    }

    var intimacyProgressData by mutableStateOf<WsData.IntimacyData?>(null)
        private set

    private var showIntimacyProgressJob: Job? = null

    suspend fun collectMsgFlow() {
        App.viewModel.messageState.uiMsgFlow.collect { (isNewMsg, askResp) ->
            if (isNewMsg) {
                stopAudio = false
                playHexMp3(askResp)
            } else if (!stopAudio) {
                playHexMp3(askResp)
            }
            showWaitingMessage = false
        }
    }

    suspend fun collectWsDataFlow() {
        WSManager.wsDataFlow.collect { wsData ->
            when (wsData) {
                is WsData.IntimacyData -> {
                    intimacyProgressData = wsData
                    showIntimacyProgress()
                }
            }
        }
    }

    private fun showIntimacyProgress() {
        showIntimacyProgressJob?.cancel()
        showIntimacyProgressJob = viewModelScope.launch {
            delay(3000)
            intimacyProgressData = null
        }
    }

    fun showChatMore(show: Boolean) {
        showChatMore = show
    }

    fun showCharacterSettings(show: Boolean) {
        showCharacterSettings = show
    }

    fun showSelectLLMModel(show: Boolean) {
        showSelectLLMModel = show
    }

    fun showSelectTone(show: Boolean) {
        showSelectTone = show
    }

    fun showDeleteSessionDialog(sessionId: Long?) {
        showDeleteSessionDialog = sessionId
    }

    fun showResetChatDialog(show: Boolean) {
        showResetChatDialog = show
    }

    fun showTarotFortuneDialog(show: Boolean) {
        showTarotFortuneDialog = show
    }

    fun onTextInput(text: String) {
        inputText = text
    }

    fun sendMessage() {
        stopAudioAndShowWaitingMessage()
        val input = inputText
        onTextInput("")
        App.viewModel.ask(input)
    }

    fun sendMessage(msg: String) {
        stopAudioAndShowWaitingMessage()
        App.viewModel.ask(msg)
    }

    fun sendTarotFortuneMessage(type: String, topic: String) {
        val input = buildString {
            append(getString(R.string.tarot_fortune_type_prefix))
            append(type)
            append("\n")
            append(getString(R.string.tarot_fortune_topic_prefix))
            append(topic)
        }
        stopAudioAndShowWaitingMessage()
        App.viewModel.messageState.tarotFortune(input)
    }

    private var stopAudio = false

    fun stopAudioAndShowWaitingMessage() {
        this.showWaitingMessage = true
        stopAudio()
    }

    fun stopAudio() {
        if (!voicePlayer.isPlaying) return
        stopAudio = true
        voicePlayer.stop()
        voicePlayer.clearMediaItems()
        isPlayerPrepared = false
    }

    /**
     * 随机播放一个表情
     */
    fun randomExpression() {
        LAppLive2DManager.instance.getModel()?.setRandomExpression(2000)
    }

    /**
     * 切换下一个模型
     */
    fun switchNextModel() {
        LAppLive2DManager.instance.getModel()?.isSay = voicePlayer.isPlaying
        LAppDelegate.instance.view?.isChangedModel = true
    }

    // 2. 播放函数
    fun playHexMp3(askResp: AskResp) {
        viewModelScope.launch {
            val isAutoPlay =
                App.viewModel.settingsState.characterSettingsFlow.value.getAutoPlayEnable()
            if (!isAutoPlay) {
                return@launch
            }
            val msgId = askResp.currentId
            val hex = askResp.audio
            if (hex.isEmpty()) {
                return@launch
            }

            val mediaId = "${msgId}_${System.currentTimeMillis()}"
            saveVoice(mediaId, hex)
            voicePlayer.addMediaSource(HexDataSource.create(hex, mediaId))
            if (!isPlayerPrepared) {
                voicePlayer.prepare()
                isPlayerPrepared = true
            }
        }
    }

    /**
     * 保存语音文件到本地, 方便调试
     * 正式包不会保存
     */
    private fun saveVoice(mediaId: String, hex: String) {
        if (!App.showLog) return
        App.launch(Dispatchers.IO) {
            val voiceDir = App.getAppDir("voices")
            val voiceFile = File(voiceDir, "$mediaId.mp3")
            if (voiceFile.exists()) {
                showLog("语音文件已存在: ${voiceFile.absolutePath}")
                return@launch
            }
            voiceFile.writeBytes(hex.decodeHex())
        }
    }

    private fun showLog(msg: String) {
        logv(msg, "tts_logging")
    }

    override fun onCleared() {
        super.onCleared()
        voicePlayer.pause()
        voicePlayer.stop()
        voicePlayer.clearMediaItems()
        voicePlayer.release()
        isPlayerPrepared = false // 记得重置
    }

    /**
     * 刷新用户信息
     */
    fun refreshUserInfo() {
        refreshMemberInfoJob?.cancel()
        refreshMemberInfoJob = viewModelScope.launch {
            async { account.refreshMemberInfo() }
            async { permissionRepo.refreshMemberVipState() }
        }
    }

    /**
     * 刷新游客信息
     */
    fun refreshGuestInfo() {
        refreshGuestInfoJob?.cancel()
        refreshGuestInfoJob = viewModelScope.launch {
            async { guestRepo.loginWithRegister(Constant.fromSite) }
            async { permissionRepo.refreshGuestVipState() }
        }
    }
}
