package com.solvibe.main.compose.ui.page.main.mine.login

import android.R.attr.onClick
import androidx.annotation.StringRes
import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.main.R
import com.solvibe.main.activity.WebActivity
import com.solvibe.main.compose.theme.Transparent
import com.solvibe.main.compose.theme.White
import com.solvibe.main.compose.theme.White10
import com.solvibe.main.compose.theme.White20
import com.solvibe.main.compose.theme.White30
import com.solvibe.main.compose.theme.White60
import com.solvibe.main.compose.theme.White70
import com.solvibe.main.compose.theme.White80
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTLoading
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.components.SelectableDrawable
import com.solvibe.main.compose.ui.modifier.click
import com.solvibe.main.compose.ui.page.main.mine.checkAccountPassword
import com.solvibe.main.compose.ui.page.main.mine.checkEmail
import com.solvibe.main.compose.utils.ChildDesign
import com.solvibe.main.compose.utils.toAnnotatedStringParameters
import com.solvibe.main.config.Constant
import com.solvibe.main.config.PASSWORD_REGEX
import com.solvibe.main.ext.showToast
import kotlinx.coroutines.flow.SharedFlow

/**
 *creater:linjinhao on 2025/5/14 16:10
 */
private const val TAG_POLICY = "TAG_POLICY"
private const val TAG_TERMS = "TAG_TERMS"
private const val TAG_AGREEMENT = "TAG_AGREEMENT"

@Composable
fun LoginRoute(
    viewModel: SvLoginViewModel = viewModel(),
    onBack: () -> Unit,
    onSignUp: () -> Unit,
    onLoginSuccess: () -> Unit,
    onForgetPwd: () -> Unit
) {
    var emailTip by remember { mutableStateOf("") }
    var pwdTip by remember { mutableStateOf("") }
    val context = LocalContext.current
    LoginScreen(
        viewModel = viewModel,
        emailTip = emailTip,
        pwdTip = pwdTip,
        onBack = onBack,
        onForgetPwd = onForgetPwd,
        onSignUp = onSignUp,
        onchangePwdTip = { pwdTip = it },
        onChangeEmailTip = { emailTip = it },
        onPolicy = { WebActivity.startBrowser(context, Constant.privacy + Constant.webParams) },
        onTerms = { WebActivity.startBrowser(context, (Constant.terms) + Constant.webParams) },
        onAgreement = { WebActivity.startBrowser(context, (Constant.eula) + Constant.webParams) },
    )
    HandleLoginEvent(
        viewModel.loginEvent,
        onLoginSuccess,
        onChangeTipPassword = { pwdTip = it },
        onChangeTipEmail = { emailTip = it })
}


@Composable
fun LoginScreen(
    viewModel: SvLoginViewModel = viewModel(),
    emailTip: String,
    pwdTip: String,
    onBack: () -> Unit,
    onSignUp: () -> Unit,
    onForgetPwd: () -> Unit,
    onchangePwdTip: (String) -> Unit,
    onChangeEmailTip: (String) -> Unit,
    onPolicy: () -> Unit,
    onTerms: () -> Unit,
    onAgreement: () -> Unit,
) {
    val state = rememberScrollState()
    val softwareKeyboardController = LocalSoftwareKeyboardController.current
    val localFocusManager = LocalFocusManager.current
    var isShowPopup by remember { mutableStateOf(false) }
    Box(
        Modifier
            .fillMaxSize()
            .click {
                localFocusManager.clearFocus()
                isShowPopup = false
                softwareKeyboardController?.hide()
            }) {
        //背景
        Image(
            painterResource(R.drawable.bg_common_bg),
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            contentDescription = "bg"
        )
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .fillMaxSize()
                .navigationBarsPadding()
                .verticalScroll(state)
        ) {
            DTVerticalSpacer(16.dp)
            BackCell(onBack)
            ConstraintLayout(Modifier.padding(horizontal = 30.dp)) {
                val (title, tvEmail, tvPwd, btn, emailSuffix) = createRefs()

                var email by remember { mutableStateOf(TextFieldValue("")) }
                var password by remember { mutableStateOf("") }

                Title(
                    Modifier.constrainAs(title) {
                        top.linkTo(parent.top, 76.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                    title = R.string.log_in,
                    des = R.string.an_account_will_be_automatically_created_if_you_haven_t_registered_yet
                )

                val context = LocalContext.current
                // 邮箱
                LoginEmail(
                    modifier = Modifier.constrainAs(tvEmail) {
                        top.linkTo(title.bottom, 40.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                    email = email, emailTip = emailTip,
                    onEmailChanged = { newEmail ->
                        email = if (newEmail.text.length > 50) {
                            val substring = newEmail.text.substring(0, 50)
                            TextFieldValue(substring, selection = TextRange(substring.length))
                        } else {
                            newEmail
                        }
                        if (email.text.isEmpty()) {
                            onChangeEmailTip(context.getString(R.string.enter_email))
                        } else {
                            onChangeEmailTip("")
                        }
                        isShowPopup = email.text.isNotEmpty()
                    }, onChangeEmailTip = { onChangeEmailTip(it) })
                // 密码
                LoginPwd(
                    modifier = Modifier.constrainAs(tvPwd) {
                        top.linkTo(tvEmail.bottom, 22.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                    pwd = password,
                    pwdTip = pwdTip,
                    onchangePwdTip = { onchangePwdTip(it) },
                    onPasswordChanged = { newPwd ->
                        password = newPwd
                        when {
                            // 未填写密码，失去焦点时，输入框下方提示“Please enter a password.”
                            password.isEmpty() -> {
                                onchangePwdTip(context.getString(R.string.enter_psw))
                            }
                            // 密码小于6个字符，失去焦点时，输入框下方提示“Password length must be 6-16 characters.”
                            password.length < 6 || password.length > 16 -> {
                                onchangePwdTip(context.getString(R.string.login_pwd_valid))
                            }
                            // 仅允许输入字母、数字、和特殊字符（见注册页的符号限制），当输入了不被允许的其他字符时，失去焦点，输入框下方提示“Password only contains alphabets, numbers and special characters.”
                            !PASSWORD_REGEX.matches(password) -> {
                                onchangePwdTip(context.getString(R.string.login_psw_valid))
                            }
                            // 密码符合情况，则清空提示
                            else -> {
                                onchangePwdTip("")
                            }
                        }
                    })

                // 登录按钮
                DTButton(
                    stringResource(R.string.login),
                    modifier = Modifier
                        .constrainAs(btn) {
                            top.linkTo(tvPwd.bottom, 50.dp)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .fillMaxSize()
                        .height(46.dp),
                    enable = checkEmail(email.text) && checkAccountPassword(password),
                    bg = SelectableDrawable(
                        R.drawable.bg_btn_login_enable_sv_new,
                        R.drawable.bg_btn_login_disable_sv
                    ),
                    contentColor = colorResource(R.color.color_121212),
                    disabledContentColor = colorResource(R.color.color_8A8C91)
                ) {
                    localFocusManager.clearFocus()
                    viewModel.login(email = email.text, pwd = password)
                }
                EmailSuffix(
                    modifier = Modifier
                        .constrainAs(emailSuffix) {
                            top.linkTo(tvEmail.bottom)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .offset(0.dp, (-5).dp),
                    isShowPopup = isShowPopup,
                    email = email,
                    onItemEmailClick = { lastedEmail ->
                        email =
                            TextFieldValue(lastedEmail, selection = TextRange(lastedEmail.length))
                        isShowPopup = false
                    })
            }
            Spacer(modifier = Modifier.height(14.dp))
            LoginHelp(onSignUp = onSignUp, onForgetPwd = onForgetPwd)
            Spacer(modifier = Modifier.weight(1f))
            UserPolicy(onPolicy = onPolicy, onTerms = onTerms, onAgreement = onAgreement)
        }
        val loading by viewModel.loadingState.collectAsState()
        DTLoading(loading = loading.loading) {}
    }
}

@Composable
fun Title(modifier: Modifier = Modifier, @StringRes title: Int, @StringRes des: Int) {
    Column(modifier = modifier.fillMaxSize()) {
        Text(
            stringResource(title),
            style = TextStyle(
                color = White,
                fontSize = 24.sp,
                fontWeight = FontWeight.W700,
                lineHeight = 32.sp
            ),
            modifier = modifier.fillMaxSize()
        )
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
        )
        Text(
            stringResource(des),
            style = TextStyle(
                color = White70,
                fontSize = 14.sp,
                fontWeight = FontWeight.W400,
                lineHeight = 20.sp
            ),
            modifier = Modifier.fillMaxSize()
        )
    }
}

@Composable
private fun BackCell(onBack: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 30.dp)
    ) {
        Image(
            painter = painterResource(R.drawable.ic_sign_in_up_close),
            contentDescription = "backIcon",
            modifier = Modifier
                .clickable {
                    onBack()
                }
                .clip(CircleShape)
                .size(24.dp),
            contentScale = ContentScale.FillBounds
        )
    }
}

@Composable
fun LoginEmail(
    modifier: Modifier = Modifier,
    email: TextFieldValue,
    emailTip: String,
    onEmailChanged: (TextFieldValue) -> Unit,
    onChangeEmailTip: (String) -> Unit
) {
    Column(modifier = modifier.fillMaxWidth()) {
        val context = LocalContext.current
        var isFocus by remember { mutableStateOf(false) }

        BasicTextField(
            value = email,
            onValueChange = {
                onEmailChanged(it)
            },
            singleLine = true,
            textStyle = TextStyle(fontSize = 15.sp, color = White),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
            modifier = Modifier
                .onFocusChanged {
                    isFocus = it.isFocused
                    if (it.isFocused) {
                        if (email.text.isEmpty()) {
                            onChangeEmailTip(context.getString(R.string.enter_email))
                        } else {
                            onChangeEmailTip("")
                        }
                    }
                }
                .background(color = colorResource(R.color.color_2E2A33), RoundedCornerShape(100.dp))
                .border(
                    if (isFocus) 1.dp else 0.dp,
                    if (isFocus) White20 else Transparent,
                    RoundedCornerShape(100.dp)
                )
                .padding(start = 16.dp)
                .height(48.dp)
                .fillMaxWidth(),
            decorationBox = { innerTextField ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(horizontal = 10.dp)
                ) {
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        if (email.text.isEmpty()) {
                            Text(
                                stringResource(R.string.email_address),
                                style = TextStyle(fontSize = 15.sp, color = White30)
                            )
                        }
                        innerTextField()
                    }
                }
            }
        )
        val backgroundColor by animateColorAsState(
            targetValue = if (emailTip.isNotEmpty()) Color.Red else Color.Transparent
        )
        Spacer(modifier = Modifier.height(5.dp))
        Text(
            emailTip, style = TextStyle(
                fontSize = 10.sp,
                color = backgroundColor
            ), modifier = Modifier
                .padding(start = 16.dp, end = 16.dp)
                .fillMaxWidth()
        )
    }

}

@Composable
fun LoginPwd(
    modifier: Modifier = Modifier,
    pwd: String,
    pwdTip: String,
    onPasswordChanged: (String) -> Unit,
    onchangePwdTip: (String) -> Unit
) {
    var isPasswordVisible by remember { mutableStateOf(false) }
    var isFocus by remember { mutableStateOf(false) }
    Column(modifier = modifier.fillMaxWidth()) {
        val context = LocalContext.current
        BasicTextField(
            value = pwd,
            onValueChange = {
                onPasswordChanged(it)
            }, textStyle = TextStyle(fontSize = 15.sp, color = White),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
            visualTransformation = if (isPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
            modifier = Modifier
                .onFocusChanged {
                    isFocus = it.isFocused
                    if (it.isFocused) {
                        when {
                            // 未填写密码，失去焦点时，输入框下方提示“Please enter a password.”
                            pwd.isEmpty() -> {
                                onchangePwdTip(context.getString(R.string.enter_psw))
                            }
                            // 密码小于6个字符，失去焦点时，输入框下方提示“Password length must be 6-16 characters.”
                            pwd.length < 6 || pwd.length > 16 -> {
                                onchangePwdTip(context.getString(R.string.login_pwd_valid))
                            }
                            // 仅允许输入字母、数字、和特殊字符（见注册页的符号限制），当输入了不被允许的其他字符时，失去焦点，输入框下方提示“Password only contains alphabets, numbers and special characters.”
                            !PASSWORD_REGEX.matches(pwd) -> {
                                onchangePwdTip(context.getString(R.string.login_psw_valid))
                            }
                            // 密码符合情况，则清空提示
                            else -> {
                                onchangePwdTip("")
                            }
                        }
                    }
                }
                .background(color = colorResource(R.color.color_2E2A33), RoundedCornerShape(100.dp))
                .border(
                    if (isFocus) 1.dp else 0.dp,
                    if (isFocus) White20 else Transparent,
                    RoundedCornerShape(100.dp)
                )
                .padding(start = 16.dp)
                .height(48.dp)
                .fillMaxWidth(),
            decorationBox = { innerTextField ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(horizontal = 10.dp)
                ) {
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        if (pwd.isEmpty()) {
                            Text(
                                stringResource(R.string.password),
                                style = TextStyle(fontSize = 15.sp, color = White30)
                            )
                        }
                        innerTextField()
                    }
                    // 眼睛图标
                    IconButton(
                        onClick = { isPasswordVisible = !isPasswordVisible },
                    ) {
                        if (isPasswordVisible) {
                            Icon(
                                painterResource(R.drawable.ic_login_pwd_show),
                                null,
                                tint = White30
                            )
                        } else {
                            Icon(
                                painterResource(R.drawable.ic_login_pwd_hide),
                                null,
                                tint = White30
                            )
                        }
                    }
                }
            }
        )
        val backgroundColor by animateColorAsState(
            targetValue = if (pwdTip.isNotEmpty()) Color.Red else Color.Transparent
        )
        Spacer(modifier = Modifier.height(5.dp))
        Text(
            pwdTip,
            fontSize = 10.sp,
            color = backgroundColor,
            modifier = Modifier
                .padding(start = 16.dp, end = 16.dp)
                .fillMaxWidth()
        )
    }
}


@Composable
fun LoginHelp(modifier: Modifier = Modifier, onSignUp: () -> Unit, onForgetPwd: () -> Unit) {
    Row(
        modifier = modifier
            .padding(horizontal = 30.dp)
            .fillMaxWidth()
    ) {
        Text(
            stringResource(R.string.sign_up),
            style = TextStyle(
                fontSize = 14.sp,
                color = White60
            ),
            modifier = Modifier
                .weight(1f)
                .click {
                    onSignUp()
                }
        )
        Text(
            stringResource(R.string.login_forgot_password),
            modifier = Modifier
                .weight(1f)
                .click {
                    onForgetPwd()
                },
            style = TextStyle(
                fontSize = 14.sp,
                color = White60,
                textAlign = TextAlign.End
            )
        )
    }
}

@Composable
fun UserPolicy(
    modifier: Modifier = Modifier,
    onPolicy: () -> Unit,
    onTerms: () -> Unit,
    onAgreement: () -> Unit
) {
    Box(
        modifier = modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth(), contentAlignment = Alignment.Center
    ) {
        val text = rememberUserPolicyAnnotationString(colorResource(R.color.color_FF5476))
        ClickableText(
            text = text, onClick = { offset ->
                text.getStringAnnotations(TAG_POLICY, start = offset, end = offset).firstOrNull()
                    ?.let {
                        onPolicy()
                    }
                text.getStringAnnotations(TAG_TERMS, start = offset, end = offset).firstOrNull()
                    ?.let {
                        onTerms()
                    }
                text.getStringAnnotations(TAG_AGREEMENT, start = offset, end = offset).firstOrNull()
                    ?.let {
                        onAgreement()
                    }
            }, style = TextStyle(
                fontSize = 12.sp,
                color = colorResource(R.color.color_A4A4A4),
                lineHeight = 19.sp,
                textAlign = TextAlign.Center
            ), modifier = modifier.fillMaxWidth()
        )
    }
}

@Composable
fun rememberUserPolicyAnnotationString(color: Color): AnnotatedString {
    val mainString = stringResource(id = R.string.login_policy_eula)
    val policy = stringResource(id = R.string.policy)
    val terms = stringResource(id = R.string.terms)
    val agreement = stringResource(id = R.string.eula_detail)
    return remember(mainString, policy, terms, agreement, color) {
        mainString.toAnnotatedStringParameters(
            ChildDesign(
                childString = policy,
                annotatedTag = TAG_POLICY,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.Normal
                )
            ),
            ChildDesign(
                childString = terms,
                annotatedTag = TAG_TERMS,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.Normal,
                )
            ),
            ChildDesign(
                childString = agreement,
                annotatedTag = TAG_AGREEMENT,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.Normal,
                )
            )
        )
    }
}


@Composable
private fun HandleLoginEvent(
    event: SharedFlow<LoginEvent>,
    onLoginSuccess: () -> Unit,
    onChangeTipEmail: (String) -> Unit,
    onChangeTipPassword: (String) -> Unit
) {
    val context = LocalContext.current
    LaunchedEffect(event) {
        event.collect {
            when (it) {
                LoginEvent.LoginSuccess -> {
                    // 账号和密码正确，点击登录成功后返回原页面
                    onLoginSuccess()
                }

                LoginEvent.AccountNotExist -> {
                    // 账号未注册会员账号，邮箱输入框下方提示“The account doesn't exist"
                    onChangeTipEmail(context.getString(R.string.login_email_no_exist))
                }

                LoginEvent.PasswordInvalid -> {
                    // 密码错误，密码输入框下方提示“Please enter the correct password.”
                    onChangeTipPassword(context.getString(R.string.please_enter_the_correct_password))
                }

                else -> {
                    showToast(context.getString(R.string.not_network))
                }
            }
        }
    }
}

@Composable
fun EmailSuffix(
    modifier: Modifier,
    isShowPopup: Boolean,
    email: TextFieldValue,
    onItemEmailClick: (String) -> Unit
) {
    //邮箱后缀
    if (isShowPopup) {
        Box(
            modifier = modifier
                .background(
                    color = colorResource(R.color.color_39393C),
                    shape = RoundedCornerShape(12.dp)
                )
                .fillMaxWidth()

        ) {
            val current = LocalContext.current
            val suffixArray by
            remember { mutableStateOf(current.resources.getStringArray(R.array.recommend_mail_box)) }
            val data = remember(email) { mutableStateListOf<String>() }

            if (email.text.contains("@")) {
                val index = email.text.indexOfLast { it == '@' }
                val suffix = email.text.subSequence(index, email.text.length).toString()
                val startStr = email.text.substring(0, index)
                suffixArray.forEach {
                    if (it.contains(suffix)) {
                        val str = startStr.plus(it)
                        if (str.length > 50) {
                            val end = 50 - it.length
                            val textStr = email.text.substring(0, end)
                            data.add(textStr.plus(it))
                        } else {
                            data.add(email.text.replace(suffix, "", true).plus(it))
                        }
                    }
                }
            } else {
                suffixArray.forEach {
                    if (email.text.plus(it).length > 50) {
                        val end = 50 - it.length
                        val textStr = email.text.substring(0, end)
                        data.add(textStr.plus(it))
                    } else {
                        data.add(email.text.plus(it))
                    }
                }
            }

            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 210.dp)
            ) {
                itemsIndexed(items = data) { index, item ->
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 10.98.dp, end = 10.98.dp)
                    ) {
                        Spacer(modifier = Modifier.height(12.dp))
                        Text(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = 10.02.dp)
                                .click {
                                    onItemEmailClick(item)
                                },
                            text = item,
                            style = TextStyle(fontSize = 13.sp, color = White80),
                        )
                        Spacer(modifier = Modifier.height(7.dp))
                        if (index != data.lastIndex) {
                            HorizontalDivider(
                                modifier = Modifier.fillMaxWidth(),
                                thickness = 1.dp,
                                color = White10
                            )
                        }
                    }
                }
            }
        }
    }
}
