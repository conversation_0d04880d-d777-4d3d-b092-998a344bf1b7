package com.solvibe.main.compose.ui.page.chat

import com.solvibe.Membership
import com.solvibe.character.net.Response
import com.solvibe.character.net.getSuccessData
import com.solvibe.character.net.runHttp
import com.solvibe.main.R
import com.solvibe.main.api.appApi
import com.solvibe.main.bean.AIReplyResp
import com.solvibe.main.db.table.MsgRecord
import com.solvibe.main.db.table.newMyTextMsg
import com.solvibe.main.ext.showToast
import com.solvibe.main.repo.BaseDeepTalkieRepo
import com.solvibe.main.utils.ContextUtil
import com.solvibe.main.utils.PromptUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext

class MsgRecordRepo : BaseDeepTalkieRepo() {
    fun queryAIRoleSession(userId: String, roleId: Long) =
        aiRoleSessionDao.queryAIRoleSession(userId, roleId)

    fun queryCurrentSessionMsg(roleId: Long): Flow<List<MsgRecord>> {
        val userId = Membership.getUserId() ?: ""
        return msgRecordDao.queryCurrentSessionMsg(userId, roleId)
    }

    suspend fun sendTextMsg(
        userId: String,
        roleId: Long,
        sessionId: Long,
        text: String,
        replyId: Long = 0,
        reply: String? = null,
    ) = withContext(Dispatchers.IO) {
        val msgRecord = newMyTextMsg(
            roleId = roleId,
            text = text,
            userId = userId,
            replyId = replyId,
            reply = reply,
        )

        val localId = msgRecordDao.insertOrIgnore(msgRecord)

        val labels = PromptUtils.checkSensitive(text)
        if (labels == "regional") {
            val string = ContextUtil.getContext().getString(R.string.content_sensitive)
            withContext(Dispatchers.Main) { showToast(string) }
            return@withContext null
        }
        val (id) = appApi.sendMsg(sessionId, text, replyId).getSuccessData()
            ?: return@withContext null
        msgRecordDao.updateMsgId(id, localId)
        msgRecordDao.updateAIRoleMsg(userId, roleId)
    }

    suspend fun setRoleMsgIsRead(userId: String, roleId: Long) =
        msgRecordDao.setRoleMsgIsRead(userId, roleId)

    suspend fun deleteMsg(userId: String, roleId: Long, msgId: Long) {
        msgRecordDao.delMsg(userId, roleId, msgId)
        msgRecordDao.updateAIRoleMsg(userId, roleId)

        runHttp { appApi.deleteMsg(msgId) }
    }

    suspend fun getInspirations(sessionId: Long, content: String): Response<AIReplyResp>? {
        return runHttp { appApi.inspiration(sessionId, content) }
    }

    suspend fun unlockAllMsg(userId: String) {
        msgRecordDao.unlockAllMsg(userId)
    }
}