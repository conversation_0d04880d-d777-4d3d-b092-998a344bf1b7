package com.solvibe.main.compose.ui.dialog

import android.view.View
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomSheetDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.ModalBottomSheetDefaults
import androidx.compose.material3.ModalBottomSheetProperties
import androidx.compose.material3.SheetState
import androidx.compose.material3.contentColorFor
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.solvibe.main.compose.theme.DTTheme
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.ext.toDp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SVBottomSheet(
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    sheetState: SheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
    sheetMaxWidth: Dp = BottomSheetDefaults.SheetMaxWidth,
    shape: Shape = BottomSheetDefaults.ExpandedShape,
    containerColor: Color = Color.White,
    contentColor: Color = contentColorFor(containerColor),
    tonalElevation: Dp = 0.dp,
    scrimColor: Color = BottomSheetDefaults.ScrimColor,
    showGestureBar: Boolean = true,
    contentWindowInsets: @Composable () -> WindowInsets = { WindowInsets(0, 0, 0, 0) },
    properties: ModalBottomSheetProperties = ModalBottomSheetDefaults.properties,
    content: @Composable ColumnScope.() -> Unit,
) {
    val navigationHeight = WindowInsets.navigationBars.getBottom(LocalDensity.current).toDp
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = modifier,
        sheetState = sheetState,
        sheetMaxWidth = sheetMaxWidth,
        shape = shape,
        containerColor = containerColor,
        contentColor = contentColor,
        tonalElevation = tonalElevation,
        scrimColor = scrimColor,
        dragHandle = null,
        contentWindowInsets = contentWindowInsets,
        properties = properties,
        content = {
            NavigationBarTransparentEffect()
            DTTheme {
                Column {
                    if (showGestureBar) {
                        GestureBar()
                    }
                    content()
                    DTVerticalSpacer(navigationHeight)
                }
            }
        },
    )
}

@Composable
fun GestureBar() {
    Box(Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
        Box(
            Modifier
                .padding(vertical = 6.dp)
                .width(61.dp)
                .height(5.dp)
                .background(Color.White, RoundedCornerShape(50))
        )
    }
}

@Composable
fun NavigationBarTransparentEffect() {
    val view = LocalView.current
    LaunchedEffect(view) {
        runCatching {
            view.rootView.findViewById<View>(android.R.id.navigationBarBackground)?.run {
                setBackgroundColor(android.graphics.Color.TRANSPARENT)
                background = null
                visibility = View.GONE
            }
        }
    }
}
