package com.solvibe.main.compose.ui.page.main.mine

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.solvibe.Membership
import com.solvibe.main.config.Constant
import com.solvibe.main.ext.stateInViewModel
import com.solvibe.utils.ext.logd
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

/**
 *creater:lin<PERSON><PERSON> on 2025/5/14 14:37
 */
class MineViewModel : ViewModel() {
    private var refreshMemberInfoJob: Job? = null
    private var refreshGuestInfoJob: Job? = null
    private val account = Membership.membershipClient.account
    private val guestRepo = Membership.membershipClient.guest
    private val permissionRepo = Membership.permissionRepo
    /**
     * 用户基本信息
     */
    val userFlow = account.userFlow.stateInViewModel()

    /**
     * 用户权益信息
     */
    val memberFlow = account.memberFlow.stateInViewModel()

    /**
     * 游客 信息
     */
    val guestFlow = guestRepo.guestInfoFlow.stateInViewModel()

    /**
     * 用户会员状态信息
     */

    val memberVipState = Membership.memberVipStateFlow

    /**
     * 游客会员状态信息
     */
    val guestVipState = Membership.guestVipStateFlow

    init {
        viewModelScope.launch {
            memberVipState.collect {
                logd("MineViewModel: memberVipState: $it")
            }
        }
    }
    /**
     * 刷新用户信息
     */
    fun refreshUserInfo() {
        refreshMemberInfoJob?.cancel()
        refreshMemberInfoJob = viewModelScope.launch {
            val infoJob = async { account.refreshMemberInfo() }
            val permissionJob = async { permissionRepo.refreshMemberVipState() }
            infoJob.await()
            permissionJob.await()
        }
    }

    /**
     * 刷新游客信息
     */
    fun refreshGuestInfo() {
        refreshGuestInfoJob?.cancel()
        refreshGuestInfoJob = viewModelScope.launch {
            val guestInfoJob = async { guestRepo.loginWithRegister(Constant.fromSite) }
            val guestPermissionJob = async { permissionRepo.refreshGuestVipState() }
            guestInfoJob.await()
            guestPermissionJob.await()
        }
    }


}