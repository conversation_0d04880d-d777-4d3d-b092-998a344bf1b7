package com.solvibe.main.compose.ui.page.chat

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.max
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White14
import com.solvibe.main.compose.theme.White20
import com.solvibe.main.compose.theme.White8
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTTextFieldIcon
import com.solvibe.main.compose.ui.components.DTTextFieldNoBorder
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.db.table.MsgRecord
import com.solvibe.main.ext.toDp
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

private fun Modifier.clipChatBottomBar(): Modifier {
    val shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
    return then(
        Modifier
            .clip(shape)
            .border(1.dp, White8, shape)
    )
}

@Composable
fun ChatBottomBar(
    focusRequester: FocusRequester,
    modifier: Modifier = Modifier,
    viewModel: ChatViewModel = viewModel(),
    onGotoBuy: () -> Unit,
) {
    val imeHeight = WindowInsets.ime.getBottom(LocalDensity.current).toDp
    val navigationHeight = WindowInsets.navigationBars.getBottom(LocalDensity.current).toDp

    if (viewModel.aiReplyState.enabled) {
        BackHandler {
            viewModel.foldAiReply()
        }
    }
    Column(
        modifier
            .clipChatBottomBar()
            .background(Color(0xFF232229))
    ) {
        if (viewModel.selectedReplyMsg != null) {
            DTVerticalSpacer(10.dp)
            ReplyMsgView(viewModel.selectedReplyMsg!!, viewModel::cancelReply)
            DTVerticalSpacer(10.dp)
        } else {
            DTVerticalSpacer(16.dp)
        }
        ChatBottomInputBar(focusRequester, onGotoBuy = onGotoBuy)
        DTVerticalSpacer(16.dp)
        AnimatedContent(
            viewModel.aiReplyState.enabled,
            Modifier.fillMaxWidth(),
            transitionSpec = {
                fadeIn(animationSpec = tween(300))
                    .togetherWith(fadeOut(animationSpec = tween(300)))
            }
        ) { enabled ->
            if (enabled) {
                Column(Modifier.heightIn(imeHeight)) {
                    DTVerticalSpacer(2.dp)
                    AIReplyView(
                        onClickKeyboard = {
                            focusRequester.requestFocus()
                        },
                        onGotoBuy = onGotoBuy
                    )
                    DTVerticalSpacer(10.dp)
                    Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.navigationBars))
                }
            } else {
                DTVerticalSpacer(max(imeHeight, navigationHeight))
            }
        }
    }
}

@Composable
fun ReplyMsgView(
    msg: MsgRecord,
    onClose: () -> Unit,
) {
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .background(White8, RoundedCornerShape(10.dp))
            .padding(8.dp, 7.dp, 12.dp, 7.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            msg.content,
            Modifier
                .weight(1f)
                .animateContentSize(),
            style = MaterialTheme.typography.labelMedium.copy(
                color = Color(0xFFB3B4BD),
                lineHeight = 15.sp,
            ),
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )
        DTHorizontalSpacer(10.dp)
        Icon(
            painterResource(R.drawable.ic_reply_close_circle),
            null,
            Modifier
                .size(20.dp)
                .clip(CircleShape)
                .clickable(onClick = onClose),
            tint = White20,
        )
    }
}

@Composable
fun ChatBottomInputBar(
    focusRequester: FocusRequester,
    viewModel: ChatViewModel = viewModel(),
    onGotoBuy: () -> Unit
) {
    val context = LocalContext.current

    Row(
        Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        val aiReplyEnabled = viewModel.aiReplyState.enabled
        val keyboardController = LocalSoftwareKeyboardController.current
        val focusManager = LocalFocusManager.current
        val scope = rememberCoroutineScope()

        DTHorizontalSpacer(16.dp)
        DTTextFieldNoBorder(
            value = viewModel.inputText,
            onValueChange = viewModel::onTextInput,
            modifier = Modifier
                .weight(1f)
                .height(44.dp)
                .focusRequester(focusRequester)
                .onFocusChanged { focusState ->
                    if (focusState.isFocused) {
                        scope.launch {
                            if (aiReplyEnabled) {
                                delay(360)
                            }
                            viewModel.foldAiReply()
                        }
                    }
                }
                .background(White14, RoundedCornerShape(50)),
            placeholder = stringResource(R.string.chat_page_input_placeholder),
            placeholderColor = Color(0xFF7A7C91),
            rightIcon = DTTextFieldIcon(
                painterResource(if (aiReplyEnabled) R.drawable.ic_refresh else R.drawable.ic_ai_tips),
                onClick = {
                    focusManager.clearFocus()
                    keyboardController?.hide()
                    viewModel.requestAITips(context, onGotoBuy = onGotoBuy)
                },
                size = 42.dp,
                padding = 9.dp,
            ),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
            keyboardActions = KeyboardActions(onSend = {
                if (viewModel.inputText.isBlank()) {
                    return@KeyboardActions
                }
                viewModel.sendTextMsg(context, onGotoBuy = onGotoBuy)
            })
        )
        DTHorizontalSpacer(10.dp)
        val enable by remember { derivedStateOf { viewModel.inputText.isNotBlank() } }
        Image(
            if (enable) painterResource(R.drawable.ic_send_msg_enable) else painterResource(R.drawable.ic_send_msg_disable),
            null,
            Modifier
                .size(44.dp)
                .clip(CircleShape)
                .clickable(enabled = enable, onClick = {
                    viewModel.sendTextMsg(context, onGotoBuy = onGotoBuy)
                }),
            contentScale = ContentScale.Crop
        )
        DTHorizontalSpacer(16.dp)
    }
}