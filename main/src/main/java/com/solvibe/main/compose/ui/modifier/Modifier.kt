package com.solvibe.main.compose.ui.modifier

import androidx.compose.foundation.clickable
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.CompositingStrategy
import androidx.compose.ui.graphics.graphicsLayer

@Composable
fun Modifier.click(onClick: () -> Unit): Modifier {
    return clickable(
        onClick = onClick,
        indication = null,
        interactionSource = null
    )
}

fun Modifier.verticalAlphaGradient(
    colors: List<Color>
): Modifier =
    this then Modifier
        .graphicsLayer(compositingStrategy = CompositingStrategy.Offscreen)
        .drawWithContent {
            drawContent()
            drawRect(
                brush = Brush.verticalGradient(
                    colors,
                    startY = 0.5f * size.height,
                    endY = 0.6f * size.height
                ),
                blendMode = BlendMode.DstIn
            )
        }
