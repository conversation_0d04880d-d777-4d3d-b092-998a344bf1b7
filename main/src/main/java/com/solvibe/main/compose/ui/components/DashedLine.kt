package com.solvibe.main.compose.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * @param dashLength: 虚线的长度
 * @param gapLength: 虚线的间隔长度
 * @param strokeWidth: 虚线的宽度
 */
@Composable
fun DashedLine(
    color: Color,
    dashLength: Dp = 3.dp,
    gapLength: Dp = 2.dp,
    strokeWidth: Dp = 0.5.dp
) {
    val dashLengthPx = dashLength.value
    val gapLengthPx = gapLength.value
    val strokeWidthPx = strokeWidth.value
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(strokeWidth)
            .drawBehind {
                val stroke = Stroke(
                    width = strokeWidthPx,
                    pathEffect = PathEffect.dashPathEffect(
                        floatArrayOf(dashLengthPx, gapLengthPx),
                        phase = 0f
                    )
                )
                // 从 (x1, y1) 到 (x2, y2) 画线
                drawLine(
                    color = color,
                    start = Offset(x = 0f, y = 0f),
                    end = Offset(x = size.width, y = 0f),
                    strokeWidth = strokeWidthPx,
                    pathEffect = stroke.pathEffect
                )
            }
    )
}