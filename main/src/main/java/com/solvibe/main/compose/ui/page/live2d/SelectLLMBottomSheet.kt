package com.solvibe.main.compose.ui.page.live2d

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.solvibe.main.App
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White16
import com.solvibe.main.compose.theme.White8
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTButtonTextStyle
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.dialog.SVBottomSheet
import com.solvibe.main.state.LLMModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SelectLLMBottomSheet(onDismiss: () -> Unit) {
    val llmModels by App.viewModel.settingsState.llmModelsStateFlow.collectAsStateWithLifecycle()
    val characterSettings by App.viewModel.settingsState.characterSettingsFlow.collectAsStateWithLifecycle()
    var selectedModelId by remember { mutableStateOf(characterSettings?.modelId) }

    SVBottomSheet(
        onDismiss = onDismiss,
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        containerColor = Color(0xFF19171F)
    ) {
        DTVerticalSpacer(8.dp)
        Row(verticalAlignment = Alignment.CenterVertically) {
            BSTitle(R.string.model_settings)
            DTHorizontalSpacer(10.dp)
            VIPLabel()
        }
        DTVerticalSpacer(10.dp)
        LLMList(selectedModelId, llmModels) {
            selectedModelId = it.id
        }
        DTVerticalSpacer(20.dp)
        ConfirmBtnGroup(onDismiss) {
            App.viewModel.settingsState.selectLLMModel(selectedModelId)
            onDismiss()
        }
        DTVerticalSpacer(20.dp)
    }
}

@Composable
fun LLMList(selectedModelId: Long?, models: List<LLMModel>, onClick: (LLMModel) -> Unit) {
    Column(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(White8)
    ) {
        models.forEachIndexed { index, model ->
            LLMItem(model, model.id == selectedModelId, onClick)
            if (index < models.lastIndex) {
                HorizontalDivider(
                    Modifier
                        .padding(horizontal = 16.dp)
                        .fillMaxWidth(),
                    0.5.dp,
                    color = Color(0xFF555555)
                )
            }
        }
    }
}

@Composable
fun LLMItem(model: LLMModel, selected: Boolean, onClick: (LLMModel) -> Unit) {
    Row(
        Modifier
            .fillMaxWidth()
            .height(42.dp)
            .clickable {
                onClick(model)
            }
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            model.name,
            maxLines = 1,
            style = MaterialTheme.typography.bodySmall.copy(
                color = Color.White
            )
        )
        DTHorizontalSpacer(10.dp)
        LazyRow(Modifier.weight(1f), horizontalArrangement = Arrangement.spacedBy(7.dp)) {
            items(model.tagsList, key = { it }) { tag ->
                Text(
                    "Reality",
                    Modifier
                        .height(24.dp)
                        .background(White16, RoundedCornerShape(8.dp))
                        .wrapContentHeight(Alignment.CenterVertically)
                        .padding(horizontal = 7.dp),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = Color.White
                    )
                )
            }
        }
        if (selected) {
            Icon(
                painterResource(R.drawable.ic_language_check),
                null,
                Modifier.size(18.dp),
                tint = Color.Unspecified
            )
        }
    }
}

@Composable
fun ConfirmBtnGroup(onCancel: () -> Unit, onConfirm: () -> Unit) {
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(46.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        OutlinedButton(
            onClick = onCancel,
            Modifier
                .width(163.dp)
                .fillMaxHeight(),
            shape = RoundedCornerShape(12.dp),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = Color(0xFF999999)
            ),
            border = BorderStroke(1.dp, Color(0x99999999))
        ) {
            Text(
                stringResource(R.string.cancel),
                style = DTButtonTextStyle.copy(
                    fontWeight = FontWeight.Normal
                )
            )
        }
        DTButton(
            stringResource(R.string.confirm),
            brush = Brush.horizontalGradient(
                listOf(
                    Color(0xFFBE4EFF),
                    Color(0xFFF343A1),
                    Color(0xFFFF5476),
                )
            ),
            modifier = Modifier
                .width(163.dp)
                .fillMaxHeight(),
            shape = RoundedCornerShape(12.dp),
            onClick = onConfirm
        )
    }
}