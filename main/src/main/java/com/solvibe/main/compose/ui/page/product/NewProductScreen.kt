package com.solvibe.main.compose.ui.page.product

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.gson.Gson
import com.imyfone.membership.api.bean.SKUBean
import com.solvibe.Membership
import com.solvibe.main.R
import com.solvibe.main.activity.WebActivity
import com.solvibe.main.bean.ProductBean
import com.solvibe.main.bean.UserManager
import com.solvibe.main.compose.theme.Black
import com.solvibe.main.compose.theme.White
import com.solvibe.main.compose.theme.White10
import com.solvibe.main.compose.theme.White50
import com.solvibe.main.compose.theme.White60
import com.solvibe.main.compose.theme.White80
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTLoading
import com.solvibe.main.compose.ui.components.DTPage
import com.solvibe.main.compose.ui.components.DTTextFieldTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.components.SelectableDrawable
import com.solvibe.main.compose.ui.modifier.click
import com.solvibe.main.config.Constant
import com.solvibe.main.config.ReportEventUtils
import com.solvibe.main.config.UmConstant
import com.solvibe.main.ext.showToast
import kotlin.math.roundToInt

/**
 *creater:linjinhao on 2025/9/18 17:52
 */
@Composable
fun NewProductRoute(
    onLogin: () -> Unit,
    onBack: () -> Unit,
    onGooglePurchaseSuccess: (bean: String) -> Unit,
) {
    val viewModel: NewProductViewModel = viewModel()
    val context = LocalContext.current
    val state by viewModel.state.collectAsState()

    NewProductScreen(
        onBack = onBack,
        onPrivacy = {
            WebActivity.startBrowser(
                context,
                Constant.privacy + Constant.webParams
            )
        },
        onTerm = {
            WebActivity.startBrowser(context, Constant.terms + Constant.webParams)
        },
        onEula = {
            WebActivity.startBrowser(context, Constant.eula + Constant.webParams)
        },
        onCancelSubscript = {
            Membership.toGoogleSubscription(context)
        }
    )
    DTLoading(loading = state.skuState is SkuUIState.Loading || state.purchaseLoading) {}

    LaunchedEffect(viewModel.state) {
        when (state.skuState) {
            SkuUIState.Fail -> {
                showToast(context.getString(R.string.not_network))
            }

            SkuUIState.Init, SkuUIState.Loading -> {

            }

            is SkuUIState.Success -> {
            }
        }
    }

    LaunchedEffect(viewModel.event) {
        viewModel.event.collect {
            when (it) {
                is ProductEvent.GooglePurchaseSuccess -> {
                    val gson = Gson()
                    val jsonString = gson.toJson(it.bean)
                    onGooglePurchaseSuccess(jsonString)
                }

                is ProductEvent.LoginTip -> {
                    showToast(context.getString(R.string.payment_successful))
                }

                ProductEvent.NeedLogin -> onLogin()

                ProductEvent.CancelGooglePurchase -> showToast(context.getString(R.string.pay_cancel))

                is ProductEvent.GooglePurchaseFail -> {
                    showToast(context.getString(R.string.pay_cancel))
                }

                ProductEvent.ICartPurchaseFinish -> {
                    showToast(context.getString(R.string.payment_successful))
                }

                ProductEvent.GoogleConfirmOrderFail -> {
                    showToast(context.getString(R.string.pay_cancel))
                }
            }
        }
    }
    ReportEventUtils.onEvent(UmConstant.Purchase_page, mapOf(UmConstant.Purchase_page to "Open_up"))
    SideEffect {
        UserManager.setIsShowNewProductPage()
    }
}

@Composable
private fun NewProductScreen(
    onBack: () -> Unit,
    onPrivacy: () -> Unit,
    onTerm: () -> Unit,
    onEula: () -> Unit,
    onCancelSubscript: () -> Unit
) {
    val viewModel: NewProductViewModel = viewModel()
    DTPage(background = R.drawable.bg_new_bug_sv) {
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .navigationBarsPadding()
                .fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                BackIcon(onBack)
                DTVerticalSpacer(238.dp)
                WelcomeTxt()
                DTVerticalSpacer(7.dp)
                ContentDescTxt()
                DTVerticalSpacer(27.dp)
                SKUList(
                    data = viewModel.skus,
                    selectIndex = viewModel.selectIndex,
                    onSelect = {
                        viewModel.selectIndex = it
                    })
            }

            LayBottom(
                viewModel = viewModel,
                onBuyProduct = {
                    if (viewModel.skus.isEmpty()) return@LayBottom
                    ReportEventUtils.onEvent(
                        UmConstant.Purchase_page,
                        mapOf(UmConstant.Purchase_page to if (viewModel.skus[viewModel.selectIndex].isMonthSubscribe()) "Welcome_Offer_1_Month" else "Welcome_Offer_1_Year")
                    )
                    viewModel.purchase(viewModel.skus[viewModel.selectIndex].skuBean)
                },
                onPrivacy = onPrivacy,
                onTerm = onTerm,
                onEula = onEula,
                onCancelSubscript = onCancelSubscript,
            )
        }
    }
}

@Composable
private fun SKUList(
    data: List<ProductBean>,
    selectIndex: Int,
    onSelect: (Int) -> Unit
) {
    LazyColumn(modifier = Modifier.fillMaxWidth()) {
        itemsIndexed(
            data.filter { it.isMonthSubscribe() || it.isYearSubscribe() },
            key = { _, it -> it.skuBean.skuID }) { index, item ->
            if (item.isMonthSubscribe()) {
                ItemMonthSku(
                    productBean = item,
                    selected = selectIndex == index,
                    onSelect = { onSelect(index) }
                )
                DTVerticalSpacer(18.dp)
            } else {
                ItemYearSku(
                    productBean = item,
                    selected = selectIndex == index,
                    onSelect = { onSelect(index) }
                )
                DTVerticalSpacer(18.dp)
            }
        }
    }
}

@Composable
fun LayBottom(
    viewModel: NewProductViewModel = viewModel(),
    onBuyProduct: (skuBean: SKUBean) -> Unit,
    onPrivacy: () -> Unit,
    onTerm: () -> Unit,
    onEula: () -> Unit,
    onCancelSubscript: () -> Unit
) {
    Column(
        modifier = Modifier
            .background(Black, shape = RoundedCornerShape(topStart = 14.dp, topEnd = 14.dp))
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (viewModel.skus.isNotEmpty()) {
                Text(
                    text = "${viewModel.skus[viewModel.selectIndex].getMonthlyPrice()}/Month",
                    style = TextStyle(
                        fontSize = 12.sp,
                        color = colorResource(R.color.color_B8B8B8),
                        lineHeight = 18.sp
                    )
                )
                DTHorizontalSpacer(2.dp)
            }
            Text(
                stringResource(R.string.auto_renewal_text),
                style = MaterialTheme.typography.bodySmall.copy(
                    fontSize = 12.sp,
                    color = colorResource(R.color.color_B8B8B8),
                    lineHeight = 18.sp
                )
            )
        }
        DTVerticalSpacer(24.dp)
        DTButton(
            text = viewModel.getBuyBtnTxt(),
            contentColor = White,
            bg = SelectableDrawable(R.drawable.bg_btn_buy, R.drawable.bg_btn_buy),
            textStyle = TextStyle(
                fontSize = 15.sp,
                fontWeight = FontWeight.W700,
                lineHeight = 24.sp
            ),
            modifier = Modifier
                .padding(start = 16.dp, end = 16.dp)
                .fillMaxWidth()
                .height(48.dp),
            shape = RoundedCornerShape(22.dp)
        ) {
            if (viewModel.skus.isEmpty()) return@DTButton
            onBuyProduct(viewModel.skus[viewModel.selectIndex].skuBean)
        }

        DTVerticalSpacer(12.dp)
        Text(
            text = stringResource(R.string.how_to_cancel_subscript),
            style = TextStyle(fontSize = 12.sp, color = colorResource(R.color.color_B8B8B8)),
            modifier = Modifier.click {
                onCancelSubscript()
            }
        )
        DTVerticalSpacer(6.dp)
        TermsAndPolicy(onTerm = onTerm, onPrivacy = onPrivacy)
    }
}

@Composable
private fun BackIcon(onBack: () -> Unit) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.End
    ) {
        IconButton(onClick = onBack) {
            Image(
                painter = painterResource(id = R.drawable.ic_new_buy_close),
                contentDescription = "closeIcon",
                contentScale = ContentScale.FillBounds, modifier = Modifier.size(14.dp)
            )
        }
    }
}

@Composable
private fun WelcomeTxt() {
    Text(
        text = stringResource(R.string.welcome_offer), style = DTTextFieldTextStyle.copy(
            fontSize = 24.sp,
            color = White,
            lineHeight = 28.sp,
        )
    )
}

@Composable
private fun TermsAndPolicy(onTerm: () -> Unit, onPrivacy: () -> Unit) {
    Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        Text(
            text = stringResource(R.string.terms),
            style = DTTextFieldTextStyle.copy(
                color = Color(0x99D7D7D7),
                fontSize = 12.sp,
                lineHeight = 18.sp, textDecoration = TextDecoration.Underline
            ), modifier = Modifier.clickable(onClick = onTerm)
        )
        DTHorizontalSpacer(37.dp)
        Text(
            text = stringResource(R.string.policy),
            style = DTTextFieldTextStyle.copy(
                color = Color(0x99D7D7D7),
                fontSize = 12.sp,
                lineHeight = 18.sp, textDecoration = TextDecoration.Underline
            ), modifier = Modifier.clickable(onClick = onPrivacy)
        )
    }
}

@Composable
private fun ContentDescTxt() {
    Text(
        text = stringResource(R.string.go_premium_with_solvibe_unlock_all_features_andget_14_800mcoins_instantly),
        style = DTTextFieldTextStyle.copy(
            fontSize = 14.sp,
            color = White80,
            lineHeight = 20.sp,
            fontWeight = FontWeight.W600
        )
    )
}

@Composable
private fun BuyBtn(onBuy: () -> Unit) {
    val brush = Brush.linearGradient(
        listOf(
            Color(0xFFBE4EFF),
            Color(0xFFF343A1),
            Color(0xFFFF5476),
        )
    )
    DTButton(
        text = stringResource(R.string.buy_now),
        brush = brush,
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(46.dp)
    ) {
        onBuy()
    }
}

@Composable
private fun ItemMonthSku(
    productBean: ProductBean,
    selected: Boolean,
    onSelect: () -> Unit
) {
    val brushBorder = if (selected) {
        Brush.linearGradient(
            listOf(
                Color(0xFFFF5476),
                Color(0xFFF5469A),
                Color(0xFFBE4EFF),
            )
        )
    } else {
        Brush.linearGradient(
            listOf(
                White10,
                White10
            )
        )
    }
    val brushBg = if (selected) {
        Brush.verticalGradient(
            listOf(
                Color(0xFF2C031C),
                Color(0xFF120F1B),
                Color(0xFF120F1B)
            )
        )
    } else {
        Brush.linearGradient(
            listOf(
                White10,
                White10
            )
        )
    }
    Row(
        modifier = Modifier
            .border(
                brush = brushBorder,
                width = 1.dp, shape = RoundedCornerShape(12.dp)
            )
            .click(onClick = onSelect)
            .background(brush = brushBg, shape = RoundedCornerShape(12.dp))
            .fillMaxWidth()
            .padding(vertical = 36.dp, horizontal = 14.dp)
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = productBean.licenseName,
                style = DTTextFieldTextStyle.copy(
                    color = if (selected) White else White60,
                    fontSize = 16.sp,
                    lineHeight = 24.sp
                )
            )
        }
        Column(modifier = Modifier.weight(1f), horizontalAlignment = Alignment.End) {
            Text(
                text = "${productBean.currencyCode}${productBean.actualPriceAmount}/Month",
                style = DTTextFieldTextStyle.copy(
                    color = if (selected) White else White60,
                    fontSize = 16.sp,
                    lineHeight = 24.sp
                )
            )
        }
    }
}

@Composable
private fun ItemYearSku(
    productBean: ProductBean,
    selected: Boolean,
    onSelect: () -> Unit
) {
    val viewModel: NewProductViewModel = viewModel()
    val context = LocalContext.current


    val brushBorder = if (selected) {
        Brush.linearGradient(
            listOf(
                Color(0xFF565656),
                Color(0xFF9C9C9C),
            )
        )
    } else {
        Brush.linearGradient(
            listOf(
                White10,
                White10
            )
        )
    }
    Box(
        modifier = Modifier
            .border(brush = brushBorder, width = 1.dp, shape = RoundedCornerShape(12.dp))
            .clip(RoundedCornerShape(12.dp))
            .click(onClick = onSelect)
            .fillMaxWidth()
            .aspectRatio(343 / 134f)
    ) {
        Image(
            painter = painterResource(if (selected) R.drawable.bg_year_sku_sel else R.drawable.bg_year_sku_def),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop,
        )
        Column(
            modifier = Modifier
                .padding(vertical = 24.dp, horizontal = 14.dp)
                .fillMaxWidth()
        ) {
            Text(
                text = productBean.licenseName,
                style = DTTextFieldTextStyle.copy(
                    color = if (selected) White else White60,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.W600,
                    lineHeight = 24.sp
                )
            )
            Row(modifier = Modifier.fillMaxWidth()) {
                Column {
                    Text(
                        text = "${productBean.getAveragePrice()}${
                            viewModel.getSkuUnitStr(
                                productBean
                            )
                        }",
                        style = DTTextFieldTextStyle.copy(
                            color = if (selected) White else White60,
                            fontSize = 14.sp,
                            lineHeight = 24.sp
                        )
                    )
                }
                Column(modifier = Modifier.weight(1f), horizontalAlignment = Alignment.End) {
                    Text(
                        text = "${productBean.currencyCode}${(productBean.actualPriceAmount)}",
                        style = DTTextFieldTextStyle.copy(
                            color = if (selected) White else White60,
                            fontSize = 14.sp,
                            lineHeight = 24.sp
                        )
                    )
                }
            }
            DTVerticalSpacer(11.dp)
            HorizontalDivider(
                modifier = Modifier.fillMaxWidth(),
                color = White50,
                thickness = 1.dp
            )
            DTVerticalSpacer(8.dp)
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "${(100 - productBean.localPercent).roundToInt()} ${context.getString(R.string.off_welcome_offer)}",
                    style = DTTextFieldTextStyle.copy(
                        color = if (selected) White else White60,
                        fontSize = 14.sp,
                        lineHeight = 24.sp
                    ), modifier = Modifier.weight(1f)
                )
                Text(
                    text = "${productBean.currencyCode}${(productBean.virtualPriceAmount)}",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = if (selected) White else White60,
                        fontSize = 14.sp,
                        lineHeight = 24.sp, textDecoration = TextDecoration.LineThrough
                    )
                )
            }
        }
    }
}

