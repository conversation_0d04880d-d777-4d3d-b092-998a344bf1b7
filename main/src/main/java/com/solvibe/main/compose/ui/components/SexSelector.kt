package com.solvibe.main.compose.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White60
import com.solvibe.main.compose.theme.White8
import com.solvibe.main.compose.ui.modifier.click

@Composable
fun SexSelector(index: Int, onSelected: (index: Int) -> Unit) {
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        SexSelectBox(
            index == 0,
            painterResource(R.drawable.ic_male),
            stringResource(R.string.new_user_info_select_page_male)
        ) {
            onSelected(0)
        }
        SexSelectBox(
            index == 1,
            painterResource(R.drawable.ic_female),
            stringResource(R.string.new_user_info_select_page_female)
        ) {
            onSelected(1)
        }
        SexSelectBox(
            index == 2,
            painterResource(R.drawable.ic_non_binary),
            stringResource(R.string.new_user_info_select_page_non_binary)
        ) {
            onSelected(2)
        }
    }
}

@Composable
fun SexSelectBox(selected: Boolean, icon: Painter, text: String, onSelected: () -> Unit) {
    Column(
        Modifier
            .size(106.dp, 113.dp)
            .click(onClick = onSelected)
            .sexSelectBg(selected),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Box(
            Modifier
                .size(60.dp),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                icon,
                null,
                Modifier.size(60.dp),
                tint = Color.Unspecified
            )
        }
        DTVerticalSpacer(8.dp)
        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall.copy(
                color = if (selected) MaterialTheme.colorScheme.onPrimary else White60,
            ),
        )
    }
}

private fun Modifier.sexSelectBg(selected: Boolean): Modifier {
    return then(
        if (selected) {
            Modifier
                .clip(RoundedCornerShape(12.dp))
                .border(1.dp, Color(0xFFFF5476), RoundedCornerShape(12.dp))
                .background(Color(0x1AFF5476))
        } else {
            Modifier
                .clip(RoundedCornerShape(12.dp))
                .background(White8)
        }
    )
}

@Preview
@Composable
fun SexSelectorPrev() {
    SexSelector(0) {}
}