package com.solvibe.main.compose.ui.dialog

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.solvibe.main.R
import com.solvibe.main.compose.theme.Black
import com.solvibe.main.compose.theme.Black30
import com.solvibe.main.compose.theme.White
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.VerifyCodeButton
import com.solvibe.main.compose.utils.ChildDesign
import com.solvibe.main.compose.utils.toAnnotatedStringParameters

/**
 *creater:linjinhao on 2025/5/27 16:50
 */
private val TAG_EMAIL = "email"

@Composable
fun LogoutDialog(show: Boolean, onConfirm: () -> Unit, onDismissRequest: () -> Unit) {
    if (show) {
        Dialog(onDismissRequest = { onDismissRequest() }) {
            Column(
                modifier = Modifier
                    .background(White, shape = RoundedCornerShape(22.dp))
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(32.dp))
                Text(
                    stringResource(R.string.are_you_sure_you_want_to_log_out),
                    style = TextStyle(
                        textAlign = TextAlign.Center,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.W500,
                        color = Black
                    ),
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .fillMaxWidth(),
                )

                Spacer(modifier = Modifier.height(32.dp))

                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                ) {
                    DTButton(
                        text = R.string.yes,
                        modifier = Modifier.size(120.dp, 42.dp),
                        textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                        shape = RoundedCornerShape(100.dp),
                        contentColor = colorResource(R.color.color_EA557F),
                        border = BorderStroke(
                            width = 2.dp,
                            color = colorResource(R.color.color_EA557F)
                        )
                    ) {
                        onConfirm()
                        onDismissRequest()
                    }
                    DTButton(
                        text = R.string.cancel,
                        modifier = Modifier.size(120.dp, 42.dp),
                        textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                        shape = RoundedCornerShape(100.dp),
                        contentColor = White,
                        containerColor = colorResource(R.color.color_EA557F)
                    ) { onDismissRequest() }
                }
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}

@Composable
fun CancelAccountSuccessDialog(show: Boolean, onConfirm: () -> Unit, onDismissRequest: () -> Unit) {
    if (show) {
        Dialog(onDismissRequest = { onDismissRequest() }) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(),
                contentAlignment = Alignment.TopCenter
            ) {
                val brush =
                    Brush.verticalGradient(listOf(colorResource(R.color.color_FFEBFA), White))
                Column(
                    modifier = Modifier
                        .padding(top = 40.dp)
                        .background(brush, shape = RoundedCornerShape(22.dp))
                        .fillMaxWidth()
                        .padding(24.dp),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Spacer(modifier = Modifier.height(66.dp))
                    Text(
                        stringResource(R.string.your_account_has_been_successfully_deleted),
                        style = TextStyle(
                            textAlign = TextAlign.Center,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.W500,
                            lineHeight = 24.sp,
                            color = Black
                        ),
                        modifier = Modifier
                            .padding(horizontal = 16.dp)
                            .fillMaxWidth(),
                    )

                    Spacer(modifier = Modifier.height(32.dp))
                    DTButton(
                        text = R.string.ok,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                        shape = RoundedCornerShape(100.dp),
                        containerColor = colorResource(R.color.color_EA557F),
                        contentColor = White,
                    ) {
                        onConfirm()
                        onDismissRequest()
                    }
                    Spacer(modifier = Modifier.height(24.dp))
                }
                Column {
                    Image(
                        painterResource(R.drawable.ic_cancel_acount_success),
                        contentDescription = "icon", modifier = Modifier.size(82.dp, 74.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun VerifyCodeAccountDialog(
    show: Boolean,
    verifyCode: String,
    codeTip: String,
    onChangeVerifyCode: (code: String) -> Unit,
    lastGetCodeTime: Long,
    email: String,
    onConfirm: () -> Unit,
    getCode: () -> Unit,
    onDismissRequest: () -> Unit
) {
    if (show) {
        Dialog(onDismissRequest = { onDismissRequest() }) {
            Column(
                modifier = Modifier
                    .background(White, shape = RoundedCornerShape(22.dp))
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(24.dp))
                Text(
                    stringResource(R.string.confirm_your_account_email),
                    style = TextStyle(
                        textAlign = TextAlign.Start,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.W600,
                        color = Black
                    ),
                    modifier = Modifier
                        .padding(horizontal = 24.dp)
                        .fillMaxWidth(),
                )
                Spacer(modifier = Modifier.height(5.dp))

                Text(
                    text = email,
                    style = TextStyle(
                        textAlign = TextAlign.Start,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.W600,
                        color = colorResource(R.color.color_FB8920)
                    ),
                    modifier = Modifier
                        .padding(horizontal = 24.dp)
                        .fillMaxWidth(),
                )

                Spacer(modifier = Modifier.height(16.dp))
                Row(
                    modifier = Modifier
                        .padding(horizontal = 24.dp)
                        .background(
                            color = colorResource(R.color.color_ECECEC),
                            shape = RoundedCornerShape(6.dp)
                        )
                        .fillMaxWidth()
                        .height(36.dp), verticalAlignment = Alignment.CenterVertically
                ) {
                    VerifyCodeTextField(modifier = Modifier.weight(4f), verifyCode = verifyCode) {
                        onChangeVerifyCode(it)
                    }
                    VerticalDivider(
                        modifier = Modifier.size(0.dp, 12.dp),
                        thickness = 1.dp,
                        color = colorResource(R.color.color_D3D3D3)
                    )
                    VerifyCodeButton(
                        lastGetCodeTime = lastGetCodeTime,
                        buttonText = stringResource(R.string.get),
                        modifier = Modifier.weight(1f),
                        contentColor = colorResource(R.color.color_EA557F),
                        disabledContentColor = Black
                    ) { getCode() }
                }
                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = codeTip,
                    style = TextStyle(
                        textAlign = TextAlign.Start,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W400,
                        color = colorResource(R.color.color_FF4444)
                    ),
                    modifier = Modifier
                        .padding(horizontal = 24.dp)
                        .fillMaxWidth(),
                )

                Spacer(modifier = Modifier.height(24.dp))

                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                ) {
                    DTButton(
                        text = R.string.cancel,
                        modifier = Modifier
                            .weight(1f)
                            .height(49.dp),
                        textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                        shape = RoundedCornerShape(bottomStart = 22.dp),
                        contentColor = Black,
                        border = BorderStroke(
                            width = 0.5.dp,
                            color = colorResource(R.color.colorLinePrimary)
                        )
                    ) {
                        onDismissRequest()
                    }
                    DTButton(
                        text = R.string.confirm,
                        enable = verifyCode.isNotBlank(),
                        modifier = Modifier
                            .weight(1f)
                            .height(49.dp),
                        textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                        shape = RoundedCornerShape(bottomEnd = 22.dp),
                        border = BorderStroke(
                            width = 0.5.dp,
                            color = colorResource(R.color.colorLinePrimary)
                        ),
                        contentColor = colorResource(R.color.color_EA557F),
                        disabledContentColor = colorResource(R.color.color_80EA558B)
                    ) {
                        onConfirm()
                    }
                }
            }
        }
    }
}

@Composable
fun CancelAccountDialogTip(show: Boolean, onConfirm: () -> Unit, onDismissRequest: () -> Unit) {
    if (show) {
        Dialog(onDismissRequest = { onDismissRequest() }) {
            Column(
                modifier = Modifier
                    .background(White, shape = RoundedCornerShape(22.dp))
                    .padding(horizontal = 24.dp)
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(24.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 5.dp),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Image(
                        painterResource(R.drawable.ic_warning),
                        contentDescription = "icon",
                        modifier = Modifier.size(28.dp, 28.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        stringResource(R.string.permanently_delete_your_account_and_personal_data),
                        style = TextStyle(
                            textAlign = TextAlign.Start,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.W600,
                            color = Black
                        )
                    )
                }


                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    stringResource(R.string._1_deleting_an_account_is_irreversible_and_you_will_not_be_able_to_use_the_account_to_enjoy_products_and_services_please_be_careful),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W600,
                        lineHeight = 20.sp
                    )
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    stringResource(R.string._2_if_you_confirm_that_you_want_to_delete_your_account_please_check_your_account_email_and_fill_in_the_verification_code_to_ensure_that_it_is_your_own_action),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W600,
                        lineHeight = 20.sp
                    )
                )
                Spacer(modifier = Modifier.height(24.dp))
                DTButton(
                    text = R.string.cancel,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                    shape = RoundedCornerShape(100.dp),
                    contentColor = White,
                    containerColor = colorResource(R.color.color_EA557F)
                ) { onDismissRequest() }

                Spacer(modifier = Modifier.height(10.dp))

                DTButton(
                    text = R.string.next,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                    shape = RoundedCornerShape(100.dp),
                    contentColor = colorResource(R.color.color_EA557F),
                    border = BorderStroke(
                        width = 2.dp,
                        color = colorResource(R.color.color_EA557F)
                    )
                ) {
                    onConfirm()
                    onDismissRequest()
                }
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }


}

@Composable
fun AccountExistPermissionDialog(
    show: Boolean,
    email: String,
    onConfirm: () -> Unit,
    onDismissRequest: () -> Unit
) {
    if (show) {
        Dialog(onDismissRequest = { onDismissRequest() }) {
            Column(
                modifier = Modifier
                    .background(White, shape = RoundedCornerShape(22.dp))
                    .padding(horizontal = 24.dp)
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(24.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 5.dp),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Image(
                        painterResource(R.drawable.ic_warning),
                        contentDescription = "icon",
                        modifier = Modifier.size(28.dp, 28.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        stringResource(R.string.your_account_still_has_valid_rights),
                        style = TextStyle(
                            textAlign = TextAlign.Start,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.W600,
                            color = Black
                        )
                    )
                }
                Spacer(modifier = Modifier.height(16.dp))
                val annotatedString =
                    accountHasPermissionAnnotatedString(email = email)
                Text(
                    text = annotatedString,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W600,
                        lineHeight = 20.sp
                    )
                )
                Spacer(modifier = Modifier.height(24.dp))
                DTButton(
                    text = R.string.cancel,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                    shape = RoundedCornerShape(100.dp),
                    contentColor = White,
                    containerColor = colorResource(R.color.color_EA557F)
                ) { onDismissRequest() }

                Spacer(modifier = Modifier.height(10.dp))

                DTButton(
                    text = R.string.next,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                    shape = RoundedCornerShape(100.dp),
                    contentColor = colorResource(R.color.color_EA557F),
                    border = BorderStroke(
                        width = 2.dp,
                        color = colorResource(R.color.color_EA557F)
                    )
                ) {
                    onConfirm()
                    onDismissRequest()
                }
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}
@Composable
fun AccountExistSubscriptPermissionDialog(
    show: Boolean,
    email: String,
    onDismissRequest: () -> Unit
) {
    if (show) {
        Dialog(onDismissRequest = { onDismissRequest() }) {
            Column(
                modifier = Modifier
                    .background(White, shape = RoundedCornerShape(22.dp))
                    .padding(horizontal = 24.dp)
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(24.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 5.dp),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Image(
                        painterResource(R.drawable.ic_warning),
                        contentDescription = "icon",
                        modifier = Modifier.size(28.dp, 28.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        stringResource(R.string.your_account_still_has_valid_rights),
                        style = TextStyle(
                            textAlign = TextAlign.Start,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.W600,
                            color = Black
                        )
                    )
                }
                Spacer(modifier = Modifier.height(16.dp))
                val annotatedString =
                    accountHasPermissionAnnotatedString(email = email)
                Text(
                    text = annotatedString,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W600,
                        lineHeight = 20.sp
                    )
                )
                Spacer(modifier = Modifier.height(24.dp))
                DTButton(
                    text = R.string.cancel,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                    shape = RoundedCornerShape(100.dp),
                    contentColor = White,
                    containerColor = colorResource(R.color.color_EA557F)
                ) { onDismissRequest() }
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}


@Composable
private fun accountHasPermissionAnnotatedString(email: String): AnnotatedString {
    val mainStr = stringResource(R.string.tip_email_has_rights)
    val color = colorResource(R.color.color_FB8920)
    return remember(email) {
        mainStr.toAnnotatedStringParameters(
            ChildDesign(
                childString = email,
                annotatedTag = TAG_EMAIL,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.W600,
                    fontSize = 12.sp
                )
            )
        )
    }
}


@Composable
fun VerifyCodeTextField(
    modifier: Modifier = Modifier,
    verifyCode: String,
    onChangeVerifyCode: (String) -> Unit,
) {
    BasicTextField(
        value = verifyCode,
        onValueChange = {
            onChangeVerifyCode(it)
        },
        singleLine = true,
        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
        decorationBox = { innerTextField ->
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(horizontal = 10.dp)
            ) {
                Box(
                    modifier = Modifier.weight(1f),
                    contentAlignment = Alignment.CenterStart
                ) {
                    if (verifyCode.isEmpty()) {
                        Text(
                            stringResource(R.string.verification_code),
                            style = TextStyle(
                                fontSize = 12.sp,
                                fontWeight = FontWeight.W500,
                                color = Black30
                            ), maxLines = 1
                        )
                    }
                    innerTextField()
                }
            }
        },
        modifier = modifier
            .fillMaxHeight()
            .fillMaxWidth()

    )
}
