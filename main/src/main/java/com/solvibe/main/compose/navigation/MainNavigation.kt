package com.solvibe.main.compose.navigation

import androidx.activity.compose.BackHandler
import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import androidx.navigation.toRoute
import androidx.savedstate.SavedState
import com.solvibe.main.bean.BindAccountBean
import com.solvibe.main.bean.UserManager
import com.solvibe.main.compose.ui.page.about.AboutMePage
import com.solvibe.main.compose.ui.page.feedback.FeedbackPage
import com.solvibe.main.compose.ui.page.live2d.ChatRecordPage
import com.solvibe.main.compose.ui.page.live2d.Live2DChatPage
import com.solvibe.main.compose.ui.page.main.UserInfoInputPage
import com.solvibe.main.compose.ui.page.main.language.LanguagePage
import com.solvibe.main.compose.ui.page.main.mine.BindAnotherEmailRoute
import com.solvibe.main.compose.ui.page.main.mine.BindEmailRoute
import com.solvibe.main.compose.ui.page.main.mine.ChangeUserInfoRoute
import com.solvibe.main.compose.ui.page.main.mine.CheckExistAccountPwdRoute
import com.solvibe.main.compose.ui.page.main.mine.CreateAccountRoute
import com.solvibe.main.compose.ui.page.main.mine.GoogleLoginRoute
import com.solvibe.main.compose.ui.page.main.mine.MineRoute
import com.solvibe.main.compose.ui.page.main.mine.forgetpwd.ForgetPasswordPage
import com.solvibe.main.compose.ui.page.main.mine.login.LoginRoute
import com.solvibe.main.compose.ui.page.main.mine.register.SignUpRoute
import com.solvibe.main.compose.ui.page.main.mine.userInfo.UserInfoRoute
import com.solvibe.main.compose.ui.page.main.video.VideoPlayPage
import com.solvibe.main.compose.ui.page.main.zoom.ZoomImagePage
import com.solvibe.main.compose.ui.page.order.OrderPage
import com.solvibe.main.compose.ui.page.product.NewProductRoute
import com.solvibe.main.compose.ui.page.product.ProductRoute
import com.solvibe.main.compose.ui.page.product.PurchaseSuccessRoute
import kotlinx.serialization.json.Json
import kotlin.reflect.typeOf

@Composable
fun MainNavigation(navController: NavHostController = rememberNavController()) {
    val startDestination =
        if (!UserManager.isShowNewProductPage()) {
            MainRoute.NewProduct
        } else {
            if (UserManager.isFirstTimeLaunchApp()) {
                MainRoute.UserInfoInput
            } else {
                MainRoute.Live2DChatPage
            }
        }
    NavHost(navController, startDestination) {
        animComposable<MainRoute.UserInfoInput> {
            UserInfoInputPage {
                navController.navigate(MainRoute.Live2DChatPage) {
                    popUpTo(MainRoute.UserInfoInput) {
                        inclusive = true
                    }
                }
            }
        }
        animComposable<MainRoute.ZoomImage> { backStackEntry ->
            ZoomImagePage(onBack = { navController.navigateUp() }, backStackEntry.toRoute())
        }
        animComposable<MainRoute.VideoPlay> { backStackEntry ->
            VideoPlayPage(backStackEntry.toRoute()) {
                navController.navigateUp()
            }
        }
        animComposable<MainRoute.Mine> {
            MineRoute(
                onEnterUserInfo = { navController.navigate(MainRoute.UserInfo) },
                onLogin = { navController.navigate(MainRoute.GoogleLogin) },
                onSignUp = { navController.navigate(MainRoute.SignUp) },
                onEnterProduct = {
                    navController.navigate(MainRoute.Product)
                },
                onNavigate = { navController.navigate(it) },
                onBack = { navController.navigateUp() }
            )
        }
        animComposable<MainRoute.Login> {
            LoginRoute(
                onBack = {
                    navController.navigateUp()
                },
                onSignUp = {
                    navController.navigate(MainRoute.SignUp) {
                        popUpTo(MainRoute.Login) {
                            inclusive = true
                        }
                    }
                },
                onLoginSuccess = {
                    navController.navigate(MainRoute.Live2DChatPage) {
                        popUpTo(navController.graph.startDestinationId) {
                            inclusive = true
                        }
                    }
                },
                onForgetPwd = { navController.navigate(MainRoute.ForgetPassword) },
            )
        }
        animComposable<MainRoute.SignUp> {
            SignUpRoute(
                onBack = {
                    navController.navigateUp()
                },
                onLogin = {
                    navController.navigate(MainRoute.Login) {
                        popUpTo(MainRoute.SignUp) {
                            inclusive = true
                        }
                    }
                },
                onSignUpSuccess = {
                    navController.navigate(MainRoute.Live2DChatPage) {
                        popUpTo(navController.graph.startDestinationId) {
                            inclusive = true
                        }
                    }
                }
            )
        }
        animComposable<MainRoute.Product> {
            ProductRoute(
                onGooglePurchaseSuccess = { bean: String ->
                    navController.navigate(MainRoute.GooglePaySuccess(bean = bean)) {
                        popUpTo(MainRoute.Product) {
                            inclusive = true
                        }
                    }
                },
                onBack = { navController.navigateUp() },
                onLogin = { navController.navigate(MainRoute.GoogleLogin) },
            )
        }
        animComposable<MainRoute.Language> {
            LanguagePage(onBack = { navController.navigateUp() })
        }
        animComposable<MainRoute.MyOrder> {
            OrderPage(onBack = { navController.navigateUp() })
        }
        animComposable<MainRoute.GooglePaySuccess> { backStackEntry ->
            val paySuccess = backStackEntry.toRoute<MainRoute.GooglePaySuccess>()
            BackHandler {
                if (paySuccess.needBackToMain) {
                    navController.navigate(MainRoute.UserInfoInput) {
                        popUpTo(navController.graph.startDestinationId) {
                            inclusive = true
                        }
                    }
                    return@BackHandler
                }
                navController.navigateUp()
            }
            PurchaseSuccessRoute(paySuccess.bean) {
                if (paySuccess.needBackToMain) {
                    navController.navigate(MainRoute.UserInfoInput) {
                        popUpTo(navController.graph.startDestinationId) {
                            inclusive = true
                        }
                    }
                    return@PurchaseSuccessRoute
                }
                navController.navigateUp()
            }
        }
        animComposable<MainRoute.AboutMe> {
            AboutMePage(onBack = { navController.navigateUp() })
        }
        animComposable<MainRoute.Feedback> {
            FeedbackPage(onBack = { navController.navigateUp() })
        }
        animComposable<MainRoute.GoogleLogin> {
            BackHandler {
                navController.navigateUp()
            }
            GoogleLoginRoute(
                onBindEmail = { info ->
                    navController.navigate(MainRoute.BindEmail(data = info))
                }, onLoginSuccess = {
                    navController.navigate(MainRoute.Live2DChatPage) {
                        popUpTo(navController.graph.startDestinationId) {
                            inclusive = true
                        }
                    }
                },
                onLogin = {
                    navController.navigate(MainRoute.Login)
                },
                onBack = {
                    navController.navigateUp()
                }
            )
        }
        animComposable<MainRoute.BindEmail>(typeMap = mapOf(typeOf<BindAccountBean>() to bindAccountBean)) { backStackEntry ->
            val bean = backStackEntry.toRoute<MainRoute.BindEmail>()
            BindEmailRoute(
                info = bean.data,
                onBack = { navController.popBackStack() },
                onNeedCreateAccount = {
                    navController.navigate(MainRoute.CreateAccount(it))
                },
                onCheckPassword = {
                    navController.navigate(MainRoute.CheckPassword(it))
                },
                onBindAnotherAccount = {
                    navController.navigate(MainRoute.BindAnotherEmail(data = it))
                })
        }
        animComposable<MainRoute.CheckPassword>(typeMap = mapOf(typeOf<BindAccountBean>() to bindAccountBean)) { backStackEntry ->
            val bean = backStackEntry.toRoute<MainRoute.CheckPassword>()
            CheckExistAccountPwdRoute(
                info = bean.data,
                onBindSuccess = {
                    navController.navigate(MainRoute.Live2DChatPage) {
                        popUpTo(navController.graph.startDestinationId) {
                            inclusive = true
                        }
                    }
                },
                onBindEmail = {
                    navController.navigate(MainRoute.BindEmail(it))
                },
                onBack = {
                    navController.popBackStack()
                })

        }
        animComposable<MainRoute.BindAnotherEmail>(typeMap = mapOf(typeOf<BindAccountBean>() to bindAccountBean)) { backStackEntry ->
            val bean = backStackEntry.toRoute<MainRoute.BindAnotherEmail>()
            BindAnotherEmailRoute(
                info = bean.data,
                onBindEmail = {
                    navController.navigate(MainRoute.BindAnotherEmail(it))
                },
                onBack = {
                    navController.popBackStack()
                })
        }
        animComposable<MainRoute.CreateAccount>(typeMap = mapOf(typeOf<BindAccountBean>() to bindAccountBean)) { backStackEntry ->
            val bean = backStackEntry.toRoute<MainRoute.CreateAccount>()
            CreateAccountRoute(
                info = bean.data,
                onBindAnotherAccount = {
                    navController.navigate(MainRoute.BindEmail(it))
                },
                onBindEmail = { navController.navigate(MainRoute.BindEmail(bean.data)) },
                onCreateAccountSuccess = {
                    navController.navigate(MainRoute.Live2DChatPage) {
                        popUpTo(navController.graph.startDestinationId) {
                            inclusive = true
                        }
                    }
                }, onBack = {
                    navController.popBackStack()
                })
        }
        animComposable<MainRoute.UserInfo> {
            UserInfoRoute(
                onBack = { navController.popBackStack() },
                onChangeFirstName = {
                    navController.navigate(MainRoute.ChangeAccountInfo(it))
                },
                onChangeLastName = {
                    navController.navigate(MainRoute.ChangeAccountInfo(it))
                },
                onGoogleLogin = {
                    navController.navigate(MainRoute.GoogleLogin) {
                        popUpTo(MainRoute.UserInfo) {
                            inclusive = true
                        }
                    }
                }
            )
        }
        animComposable<MainRoute.ChangeAccountInfo> { backStackEntry ->
            val bean = backStackEntry.toRoute<MainRoute.ChangeAccountInfo>()
            ChangeUserInfoRoute(bean.type) {
                navController.popBackStack()
            }
        }
        animComposable<MainRoute.ForgetPassword> {
            ForgetPasswordPage(onBack = { navController.navigateUp() })
        }
        animComposable<MainRoute.Live2DChatPage> {
            Live2DChatPage(
                onAvatarClick = { navController.navigate(MainRoute.UserInfo) },
                onLoginClick = { navController.navigate(MainRoute.GoogleLogin) },
                onSignUpClick = { navController.navigate(MainRoute.SignUp) },
                onMoreClick = { navController.navigate(MainRoute.Mine) },
                onGotoPay = { navController.navigate(MainRoute.Product) },
                onReport = { navController.navigate(MainRoute.Feedback) },
                onChatRecord = { navController.navigate(MainRoute.ChatRecord) }
            )
        }
        animComposable<MainRoute.NewProduct> {
            NewProductRoute(
                onBack = {
                    navController.navigate(MainRoute.UserInfoInput) {
                        popUpTo(navController.graph.startDestinationId) {
                            inclusive = true
                        }
                    }
                },
                onGooglePurchaseSuccess = {
                    navController.navigate(MainRoute.GooglePaySuccess(it, true)) {
                        popUpTo(navController.graph.startDestinationId) {
                            inclusive = true
                        }
                    }
                },
                onLogin = {
                    navController.navigate(MainRoute.Login)
                }
            )
        }
        animComposable<MainRoute.ChatRecord> {
            ChatRecordPage(
                onBack = { navController.navigateUp() }
            )
        }
    }
}


internal val bindAccountBean = object : NavType<BindAccountBean>(true) {
    override fun get(bundle: SavedState, key: String): BindAccountBean? {
        val json = bundle.getString(key) ?: return null
        return parseValue(json)
    }

    override fun put(bundle: SavedState, key: String, value: BindAccountBean) {
        bundle.putString(key, serializeAsValue(value))
    }

    override fun parseValue(value: String): BindAccountBean {
        return Json.decodeFromString(value)
    }

    override fun serializeAsValue(value: BindAccountBean): String {
        return Json.encodeToString(value)
    }
}