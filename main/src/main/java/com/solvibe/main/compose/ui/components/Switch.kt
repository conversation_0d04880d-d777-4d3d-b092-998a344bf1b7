package com.solvibe.main.compose.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.selection.toggleable
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

@Composable
fun SVSwitch(
    checked: <PERSON>olean,
    onCheckedChange: (<PERSON>olean) -> Unit
) {
    val trackColor by animateColorAsState(
        Color(if (checked) 0xFF34C759 else 0xFF505056),
        animationSpec = tween(300)
    )
    val offsetX by animateDpAsState(
        if (checked) 18.dp else 2.dp,
        animationSpec = tween(300)
    )

    Box(
        Modifier
            .size(36.dp, 20.dp)
            .clip(RoundedCornerShape(50))
            .toggleable(
                value = checked,
                onValueChange = onCheckedChange,
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            )
            .background(trackColor),
        contentAlignment = Alignment.CenterStart
    ) {
        Box(
            Modifier
                .padding(start = offsetX)
                .size(16.dp)
                .clip(CircleShape)
                .background(Color.White)
        )
    }
}