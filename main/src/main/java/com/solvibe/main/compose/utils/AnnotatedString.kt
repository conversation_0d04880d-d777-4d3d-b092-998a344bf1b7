package com.solvibe.main.compose.utils

import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.ExperimentalTextApi
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withAnnotation
import androidx.compose.ui.text.withStyle

/**
 *creater:<PERSON><PERSON><PERSON><PERSON> on 2025/5/16 11:29
 */
@OptIn(ExperimentalTextApi::class)
internal fun String.toAnnotatedString(vararg childDesign: ChildDesign): AnnotatedString {
    val mainString = this
    val childs = childDesign
        // 标记在主串的位置
        .map { child -> child to indexOf(child.childString) }
        // 过滤子串不在主串
        .filter { it.second != -1 }
        // 排序
        .sortedWith { o1, o2 -> o1.second - o2.second }
    var end = 0
    return buildAnnotatedString {
        if (childs.isEmpty()) {
            append(mainString)
        } else {
            childs.forEach {

                // step1: 前面有剩余字符串，需要添加
                if (it.second != end) {
                    append(substring(end, it.second))
                    end = it.second
                }

                // step2: 增加childDesign进去
                val (childString, spanStyle, annotatedTag) = it.first
                withStyle(spanStyle) {
                    if (annotatedTag != null) {
                        withAnnotation(annotatedTag, annotatedTag) { append(childString) }
                    } else {
                        append(childString)
                    }
                }
                end += childString.length
            }
        }
    }
}

@OptIn(ExperimentalTextApi::class)
internal fun String.toAnnotatedStringParameters(vararg childDesign: ChildDesign): AnnotatedString {
    val mainString = this
    val childs = childDesign
        // 标记在主串的位置
        .mapIndexed { index, child ->
            child to indexOf("%${index + 1}\$s")
        }
        // 过滤子串不在主串
        .filter { it.second != -1 }

    var end = 0
    return buildAnnotatedString {
        if (childs.isEmpty()) {
            append(mainString)
        } else {
            childs.forEachIndexed { index, it ->
                // step1: 前面有剩余字符串，需要添加
                if (it.second != end) {
                    append(substring(end, it.second))
                    end = it.second
                }
                // step2: 增加childDesign进去
                val key = "%${index + 1}\$s"
                val (childString, spanStyle, annotatedTag) = it.first
                withStyle(spanStyle) {
                    if (annotatedTag != null) {
                        withAnnotation(annotatedTag, annotatedTag) { append(childString) }
                    } else {
                        append(childString)
                    }
                }
                end += key.length
            }
            // 补充剩余部分
            if (end != mainString.length) {
                append(substring(end))
            }
        }
    }
}

data class ChildDesign(
    val childString: String,
    val spanStyle: SpanStyle = SpanStyle(),
    val annotatedTag: String? = null
)
