package com.solvibe.main.compose.ui.page.main.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.imyfone.membership.ext.googlelogin.GoogleLoginData
import com.imyfone.membership.ext.googlelogin.GoogleLoginEvent
import com.imyfone.membership.ext.googlelogin.rememberLauncherGoogleLoginWithResult
import com.solvibe.main.R
import com.solvibe.main.activity.WebActivity
import com.solvibe.main.bean.BindAccountBean
import com.solvibe.main.compose.theme.White
import com.solvibe.main.compose.theme.White15
import com.solvibe.main.compose.theme.White50
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTLoading
import com.solvibe.main.compose.ui.components.DTTextFieldTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.utils.ChildDesign
import com.solvibe.main.compose.utils.GOOGLE_LOGIN_TIMEOUT
import com.solvibe.main.compose.utils.GOOGLE_LOGIN_TOKEN
import com.solvibe.main.compose.utils.toAnnotatedStringParameters
import com.solvibe.main.config.Constant
import com.solvibe.main.ext.showToast
import kotlinx.coroutines.flow.SharedFlow

/**
 *creater:linjinhao on 2025/5/22 14:12
 */

private val TAG_TERMS = "terms"
private val TAG_PRIVACY = "privacy"


@Composable
fun GoogleLoginRoute(
    viewModel: GoogleLoginViewModel = viewModel(),
    onBindEmail: (info: BindAccountBean) -> Unit,
    onLoginSuccess: () -> Unit,
    onLogin: () -> Unit,
    onBack: () -> Unit
) {
    val context = LocalContext.current
    GoogleLoginScreen(
        onLogin = onLogin,
        onTerms = {
            WebActivity.startBrowser(context, Constant.terms + Constant.webParams)
        },
        onPrivacy = {
            WebActivity.startBrowser(context, Constant.privacy + Constant.webParams)
        }, onBack = { onBack() }
    )

    HandleGoogleLoginEvent(
        viewModel.googleLoginEvent,
        onBindEmail = { onBindEmail(it) },
        onLoginSuccess = { onLoginSuccess() }
    )
}

@Composable
fun GoogleLoginScreen(
    viewModel: GoogleLoginViewModel = viewModel(),
    onLogin: () -> Unit,
    onTerms: () -> Unit,
    onPrivacy: () -> Unit,
    onBack: () -> Unit
) {
    Box(
        Modifier
            .fillMaxSize(), contentAlignment = Alignment.Center
    ) {
        Image(
            painterResource(R.drawable.bg_google_login),
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            contentDescription = "bg"
        )
        Image(
            painterResource(R.drawable.ic_top_bar_back),
            modifier = Modifier
                .align(alignment = Alignment.TopStart)
                .statusBarsPadding()
                .padding(start = 16.dp, top = 7.dp)
                .clip(CircleShape)
                .clickable { onBack() }
                .size(30.dp),
            contentDescription = "backIcon"
        )
        Column(
            modifier = Modifier
                .align(alignment = Alignment.BottomCenter)
                .statusBarsPadding()
                .fillMaxWidth()
                .padding(horizontal = 30.dp)
                .navigationBarsPadding()
        ) {
            Title()
            DTVerticalSpacer(6.dp)
            CreateAccountDesc()
            DTVerticalSpacer(34.dp)
            GoogleLoginButton(onGoogleLoginSuccess = { viewModel.googleAuthLogin(it) })
            DTVerticalSpacer(20.dp)
            OtherOption(onLogin = onLogin)
            DTVerticalSpacer(16.dp)
            UserPolicyAnnotationString(onTerms, onPrivacy)
            DTVerticalSpacer(40.dp)
            Copyright()
        }
        val isLoading by viewModel.googleLoginState.collectAsState()
        DTLoading(loading = isLoading) { }
    }
}

@Composable
private fun CreateAccountDesc() {
    Text(
        text = stringResource(R.string.create_account_or_log_in_using_options_below),
        style = TextStyle(fontSize = 14.sp, lineHeight = 18.sp, color = White50)
    )
}

@Composable
private fun Title() {
    Text(
        text = rememberTitle(),
        style = TextStyle(
            fontSize = 35.sp,
            lineHeight = 45.sp,
            color = White,
            fontWeight = FontWeight.W800
        )
    )
}

@Composable
private fun OtherOption(onLogin: () -> Unit) {
    DTButton(
        R.string.other_options,
        modifier = Modifier
            .fillMaxWidth()
            .height(50.dp),
        textStyle = DTTextFieldTextStyle.copy(fontSize = 16.sp, color = White),
        containerColor = White15
    ) {
        onLogin()
    }
}

@Composable
private fun Copyright() {
    Text(
        text = stringResource(R.string.all_right),
        style = TextStyle(
            fontSize = 12.sp,
            textAlign = TextAlign.Center,
            lineHeight = 16.sp,
            color = colorResource(R.color.color_66D7D7D7)
        ), modifier = Modifier.fillMaxWidth()
    )
}

@Composable
private fun UserPolicyAnnotationString(
    onTerms: () -> Unit,
    onPrivacy: () -> Unit
) {
    val mainStr = rememberPolicy()
    ClickableText(
        text = mainStr,
        style = TextStyle(
            fontSize = 12.sp,
            lineHeight = 16.sp,
            textAlign = TextAlign.Center,
            color = colorResource(R.color.color_A4A4A4)
        ), modifier = Modifier.fillMaxWidth()
    ) { offset ->
        mainStr.getStringAnnotations(TAG_TERMS, start = offset, end = offset).firstOrNull()
            ?.let {
                onTerms()
            }
        mainStr.getStringAnnotations(TAG_PRIVACY, start = offset, end = offset)
            .firstOrNull()
            ?.let {
                onPrivacy()
            }
    }
}

@Composable
private fun rememberTitle(): AnnotatedString {
    val mainStr = stringResource(R.string.your_best_soulmate)
    val ai = stringResource(R.string.ai)
    val color = colorResource(R.color.color_FD679B)
    return remember(mainStr, ai, color) {
        mainStr.toAnnotatedStringParameters(
            ChildDesign(
                childString = ai,
                annotatedTag = "",
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.W400
                )
            )
        )
    }
}

@Composable
private fun rememberPolicy(): AnnotatedString {
    val mainStr =
        stringResource(R.string.by_signing_up_you_agree_to_our_terms_of_service_and_privacy_policy)
    val terms = stringResource(R.string.terms_of_service)
    val privacy = stringResource(R.string.policy)
    val color = colorResource(R.color.color_FD679B)
    return remember(mainStr, terms, privacy) {
        mainStr.toAnnotatedStringParameters(
            ChildDesign(
                childString = terms,
                annotatedTag = TAG_TERMS,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.W400
                )
            ), ChildDesign(
                childString = privacy,
                annotatedTag = TAG_PRIVACY,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.W400
                )
            )
        )
    }
}

@Composable
fun GoogleLoginButton(
    modifier: Modifier = Modifier,
    onGoogleLoginSuccess: (data: GoogleLoginData) -> Unit,
) {
    val context = LocalContext.current
    val googleLauncher = rememberLauncherGoogleLoginWithResult {
        when (it) {
            GoogleLoginEvent.GoogleServiceNotAvailable -> showToast(context.getString(R.string.google_service_not_available))
            is GoogleLoginEvent.RequestError -> {
                showToast(it.codeString)
            }

            GoogleLoginEvent.RequestErrorCancelToManyTimes -> showToast(context.getString(R.string.google_call_fast))
            GoogleLoginEvent.RequestNetWorkError -> showToast(context.getString(R.string.network_error))
            GoogleLoginEvent.RequestStart -> Unit
            GoogleLoginEvent.ResultCancel -> Unit
            is GoogleLoginEvent.ResultError -> showToast(context.getString(R.string.network_error))
            is GoogleLoginEvent.ResultSuccess -> onGoogleLoginSuccess(it.data)
        }
    }
    Row(
        modifier = modifier
            .clip(RoundedCornerShape(100.dp))
            .clickable {
                googleLauncher(
                    GOOGLE_LOGIN_TOKEN,
                    true,
                    GOOGLE_LOGIN_TIMEOUT
                )
            }
            .background(White)
            .fillMaxWidth()
            .height(48.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Image(
            painterResource(R.drawable.ic_google),
            modifier = Modifier.size(32.dp),
            contentDescription = "icon"
        )
        Spacer(modifier = Modifier.size(6.dp))
        Text(
            stringResource(R.string.continue_with_google),
            style = DTTextFieldTextStyle.copy(
                fontSize = 16.sp,
                fontWeight = FontWeight.W500,
                color = colorResource(R.color.color_121212)
            ),
        )
    }
}

@Composable
private fun HandleGoogleLoginEvent(
    event: SharedFlow<SVGoogleLoginEvent>,
    onBindEmail: (info: BindAccountBean) -> Unit,
    onLoginSuccess: () -> Unit
) {
    val context = LocalContext.current
    LaunchedEffect(event) {
        event.collect {
            when (it) {
                SVGoogleLoginEvent.CommonError -> {
                    showToast(context.getString(R.string.not_network))
                }

                is SVGoogleLoginEvent.LoginSuccess -> {
                    onLoginSuccess()
                }

                is SVGoogleLoginEvent.BindEmail -> {
                    onBindEmail(it.bean)
                }
            }
        }
    }

}