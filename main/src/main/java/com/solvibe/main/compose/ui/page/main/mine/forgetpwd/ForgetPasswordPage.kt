package com.solvibe.main.compose.ui.page.main.mine.forgetpwd

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.main.R
import com.solvibe.main.activity.WebActivity
import com.solvibe.main.compose.theme.White12
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTPage
import com.solvibe.main.compose.ui.components.DTTextField
import com.solvibe.main.compose.ui.components.DTTextFieldIcon
import com.solvibe.main.compose.ui.components.DTTextFieldTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.components.VerifyCodeButton
import com.solvibe.main.compose.ui.page.main.mine.login.UserPolicy
import com.solvibe.main.config.Constant

@Composable
fun ForgetPasswordPage(
    onBack: () -> Unit,
    viewModel: ForgetPasswordViewModel = viewModel()
) {
    val context = LocalContext.current
    DTPage(
        background = R.drawable.splash_bg,
        loading = viewModel.loading,
        onDismissLoading = { viewModel.showLoading(false) }
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .statusBarsPadding()
                .navigationBarsPadding()
                .animateContentSize()
        ) {
            DTVerticalSpacer(16.dp)
            BackCell(onBack)
            DTVerticalSpacer(70.dp)
            Title()
            DTVerticalSpacer(62.dp)
            EmailInput(viewModel.email, viewModel.emailTip.orEmpty(), viewModel::onEmailChanged)
            DTVerticalSpacer(7.dp)
            SMSInput(
                viewModel.code,
                viewModel.emailTip == "",
                viewModel.codeTip.orEmpty(),
                viewModel.lastGetCodeTime,
                viewModel::onCodeChanged,
                viewModel::getCode,
            )
            DTVerticalSpacer(7.dp)
            PasswordInput(
                value = viewModel.password,
                placeholder = stringResource(R.string.new_password),
                tipsStr = viewModel.passwordTip.orEmpty(),
                onValueChange = viewModel::onPasswordChanged
            )
            DTVerticalSpacer(7.dp)
            PasswordInput(
                value = viewModel.confirmPassword,
                placeholder = stringResource(R.string.confirm_password),
                tipsStr = viewModel.confirmPasswordTip.orEmpty(),
                onValueChange = viewModel::onConfirmPasswordChanged
            )
            DTVerticalSpacer(38.dp)
            SubmitBtn(enable = viewModel.canSubmit()) {
                viewModel.submit {
                    onBack()
                }
            }
            Spacer(modifier = Modifier.weight(1f))
            UserPolicy(
                Modifier.padding(horizontal = 20.dp),
                onPolicy = {
                    WebActivity.startBrowser(context, Constant.privacy + Constant.webParams)
                },
                onTerms = {
                    WebActivity.startBrowser(context, (Constant.terms) + Constant.webParams)
                },
                onAgreement = {
                    WebActivity.startBrowser(context, (Constant.eula) + Constant.webParams)
                },
            )
            DTVerticalSpacer(12.dp)
        }
    }
}

@Composable
fun SMSInput(
    value: String,
    enable: Boolean,
    codeTip: String,
    lastGetCodeTime: Long,
    onValueChange: (String) -> Unit,
    onGetCode: () -> Unit
) {
    Box(Modifier.padding(horizontal = 30.dp)) {
        DTTextField(
            value = value,
            onValueChange = onValueChange,
            textFieldModifier = Modifier
                .fillMaxWidth()
                .height(48.dp)
                .background(White12, RoundedCornerShape(50)),
            placeholder = stringResource(R.string.verification_code),
            textStyle = DTTextFieldTextStyle.copy(
                fontSize = 15.sp,
                lineHeight = 22.sp
            ),
            tips = codeTip,
            tipsModifier = Modifier.padding(start = 16.dp),
            keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
            contentPadding = PaddingValues(start = 16.dp, end = 90.dp)
        )
        VerifyCodeButton(
            lastGetCodeTime = lastGetCodeTime,
            modifier = Modifier
                .padding(top = 8.dp, end = 10.dp)
                .size(64.dp, 32.dp)
                .align(Alignment.TopEnd),
            buttonText = stringResource(R.string.get),
            enable = enable,
            containerColor = colorResource(R.color.color_EA557F),
            disabledContainerColor = colorResource(R.color.color_EA557F),
            disabledContentColor = MaterialTheme.colorScheme.onPrimary,
            onClick = onGetCode
        )
    }
}

@Composable
private fun Title() {
    Text(
        text = stringResource(R.string.reset_psw),
        Modifier.padding(start = 30.dp),
        style = MaterialTheme.typography.headlineLarge.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            fontSize = 24.sp,
            lineHeight = 32.sp,
        )
    )
}
@Composable
private fun BackCell(onBack: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 30.dp)
    ) {
        Image(
            painter = painterResource(R.drawable.ic_sign_in_up_close),
            contentDescription = "backIcon",
            modifier = Modifier
                .clickable {
                    onBack()
                }
                .clip(CircleShape)
                .size(24.dp),
            contentScale = ContentScale.FillBounds
        )
    }
}

@Composable
fun EmailInput(
    value: String,
    emailTip: String,
    onValueChange: (String) -> Unit,
) {
    DTTextField(
        value = value,
        onValueChange = onValueChange,
        textFieldModifier = Modifier
            .padding(horizontal = 30.dp)
            .fillMaxWidth()
            .height(48.dp)
            .background(White12, RoundedCornerShape(50)),
        textStyle = DTTextFieldTextStyle.copy(
            fontSize = 15.sp,
            lineHeight = 22.sp
        ),
        placeholder = stringResource(R.string.email),
        tips = emailTip,
        tipsModifier = Modifier.padding(horizontal = 46.dp),
        keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Email),
    )
}

@Composable
fun PasswordInput(
    value: String,
    placeholder: String,
    tipsStr: String,
    onValueChange: (String) -> Unit,
) {
    var isPasswordVisible by remember { mutableStateOf(false) }

    DTTextField(
        value = value,
        onValueChange = onValueChange,
        textFieldModifier = Modifier
            .padding(horizontal = 30.dp)
            .fillMaxWidth()
            .height(48.dp)
            .background(White12, RoundedCornerShape(50)),
        textStyle = DTTextFieldTextStyle.copy(
            fontSize = 15.sp,
            lineHeight = 22.sp
        ),
        placeholder = placeholder,
        tips = tipsStr,
        tipsModifier = Modifier.padding(horizontal = 46.dp),
        rightIcon = DTTextFieldIcon(
            painterResource(if (isPasswordVisible) R.drawable.ic_login_pwd_show else R.drawable.ic_login_pwd_hide),
            onClick = { isPasswordVisible = !isPasswordVisible },
            size = 36.dp,
            padding = 10.dp,
        ),
        keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Password),
        visualTransformation = if (isPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
    )
}

@Composable
fun SubmitBtn(enable: Boolean, onClick: () -> Unit) {
    DTButton(
        stringResource(R.string.login),
        brush = Brush.horizontalGradient(
            if (enable) listOf(
                Color(0xFFBE4EFF),
                Color(0xFFF343A1),
                Color(0xFFFF5476),
            ) else listOf(
                Color(0xFF4B4B51),
                Color(0xFF4B4B51),
            )
        ),
        Modifier
            .padding(horizontal = 30.dp)
            .fillMaxWidth()
            .height(46.dp),
        enable = enable,
        onClick = onClick
    )
}