package com.solvibe.main.compose.ui.page.live2d

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.solvibe.character.net.ws.WsData
import com.solvibe.main.R
import com.solvibe.main.compose.theme.Black80
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer

@Composable
fun IntimacyProgress(intimacyData: WsData.IntimacyData?) {
    AnimatedContent(
        intimacyData,
        Modifier
            .padding(start = 16.dp, top = 6.dp, end = 16.dp)
            .fillMaxWidth(),
        transitionSpec = {
            fadeIn(tween(300))
                .togetherWith(fadeOut(tween(300)))
        }
    ) { data ->
        if (data != null) {
            Box(
                Modifier
                    .fillMaxWidth()
                    .height(18.dp)
                    .background(Black80, RoundedCornerShape(50))
                    .padding(2.dp)
            ) {
                Box(
                    Modifier
                        .fillMaxWidth(data.finalScore / 100f)
                        .fillMaxHeight()
                        .background(
                            brush = Brush.horizontalGradient(
                                listOf(Color(0xFFB93FFF), Color(0xFFFF4066))
                            ),
                            RoundedCornerShape(50)
                        ),
                )
                Row(Modifier.align(Alignment.Center)) {
                    Icon(
                        painterResource(R.drawable.ic_intimacy),
                        null,
                        Modifier.size(12.dp),
                        tint = Color.Unspecified
                    )
                    DTHorizontalSpacer(3.dp)
                    Text(
                        "${data.finalScore}/100",
                        style = MaterialTheme.typography.labelMedium.copy(
                            color = Color.White,
                            lineHeight = 12.sp
                        )
                    )
                }
                Text(
                    "${if (data.score > 0) "+" else ""}${data.score}",
                    Modifier
                        .align(Alignment.CenterEnd)
                        .padding(end = 110.dp),
                    style = MaterialTheme.typography.labelMedium.copy(
                        color = Color(0xFF1CFFA4),
                        lineHeight = 12.sp
                    )
                )
            }
        } else {
            Spacer(
                Modifier
                    .fillMaxWidth()
                    .height(18.dp)
            )
        }
    }
}