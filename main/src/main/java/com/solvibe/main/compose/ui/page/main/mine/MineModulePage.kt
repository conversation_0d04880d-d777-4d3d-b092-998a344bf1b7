package com.solvibe.main.compose.ui.page.main.mine

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.imyfone.membership.api.bean.GuestInfoBean
import com.imyfone.membership.api.bean.MemberBean
import com.solvibe.Membership
import com.solvibe.main.R
import com.solvibe.main.activity.WebActivity
import com.solvibe.main.bean.UserManager
import com.solvibe.main.compose.navigation.MainRoute
import com.solvibe.main.compose.theme.White
import com.solvibe.main.compose.theme.White5
import com.solvibe.main.compose.theme.White50
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTPage
import com.solvibe.main.compose.ui.components.DTTextFieldTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.modifier.click
import com.solvibe.main.compose.utils.ChildDesign
import com.solvibe.main.compose.utils.toAnnotatedStringParameters
import com.solvibe.main.config.Constant

private val TAG_LOGIN = "TAG_LOGIN"
private val TAG_SIGNUP = "TAG_SIGNUP"

@Composable
fun MineRoute(
    viewModel: MineViewModel = viewModel(), onEnterUserInfo: () -> Unit,
    onLogin: () -> Unit,
    onSignUp: () -> Unit,
    onEnterProduct: () -> Unit,
    onBack: () -> Unit,
    onNavigate: (MainRoute) -> Unit
) {
    val context = LocalContext.current
    val memberBean by viewModel.memberFlow.collectAsState()
    val guestInfo by viewModel.guestFlow.collectAsState()
    val user by viewModel.userFlow.collectAsState()
    MineScreen(
        viewModel = viewModel,
        member = memberBean,
        onEnterUserInfo = onEnterUserInfo,
        onLogin = onLogin,
        onSignUp = onSignUp,
        onEnterProduct = onEnterProduct,
        onBuyProduct = {},
        onNavigate = onNavigate,
        onTerms = { WebActivity.startBrowser(context, Constant.terms + Constant.webParams) },
        onPrivacy = { WebActivity.startBrowser(context, Constant.privacy + Constant.webParams) },
        onBack = onBack,
        guestInfo = guestInfo
    )
    if (user != null) {
        LaunchedEffect(user) {
            viewModel.refreshUserInfo()
        }
    } else {
        LaunchedEffect(guestInfo) {
            viewModel.refreshGuestInfo()
        }
    }
}

@Composable
private fun MineScreen(
    viewModel: MineViewModel = viewModel(),
    member: MemberBean?,
    guestInfo: GuestInfoBean?,
    onBack: () -> Unit,
    onEnterUserInfo: () -> Unit,
    onLogin: () -> Unit,
    onSignUp: () -> Unit,
    onEnterProduct: () -> Unit,
    onBuyProduct: () -> Unit,
    onNavigate: (MainRoute) -> Unit,
    onTerms: () -> Unit,
    onPrivacy: () -> Unit
) {
    DTPage(background = R.drawable.bg_me_sv) {
        Column(
            Modifier
                .padding(horizontal = 16.dp)
                .statusBarsPadding()
                .navigationBarsPadding()
                .fillMaxSize()
        ) {
            MeTopBar(
                Modifier,
                member = member,
                onEnterUserInfo = onEnterUserInfo,
                onLogin = onLogin,
                onSignUp = onSignUp,
                onBack = onBack,
                guestInfo = guestInfo
            )
            DTVerticalSpacer(23.dp)
            VipState(
                viewModel = viewModel,
                onEnterProduct = onEnterProduct,
                onBuyProduct = onBuyProduct
            )
            DTVerticalSpacer(24.dp)
            MineItems(
                member = member, onNavigate,
                onTerms = onTerms,
                onPrivacy = onPrivacy
            )
        }
    }
}

@Composable
private fun MeTopBar(
    modifier: Modifier = Modifier,
    member: MemberBean?,
    guestInfo: GuestInfoBean?,
    onBack: () -> Unit,
    onEnterUserInfo: () -> Unit,
    onLogin: () -> Unit,
    onSignUp: () -> Unit
) {

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        TopBarSetting(onBack, onEnterUserInfo)
        DTVerticalSpacer(29.dp)
        Row(
            modifier = Modifier
                .fillMaxWidth(), verticalAlignment = Alignment.CenterVertically
        ) {
            UserAvatar(member, onEnterUserInfo)
            DTHorizontalSpacer(11.dp)
            AccountInfo(
                member = member,
                guestInfo = guestInfo,
                onEnterUserInfo = onEnterUserInfo,
                onLogin = onLogin,
                onSignUp = onSignUp
            )
        }
    }
}

@Composable
private fun AccountInfo(
    member: MemberBean?,
    guestInfo: GuestInfoBean?,
    onEnterUserInfo: () -> Unit,
    onLogin: () -> Unit,
    onSignUp: () -> Unit
) {
    val enterUserInfoModifier =
        Modifier.clickable(enabled = member != null, onClick = onEnterUserInfo)
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .then(enterUserInfoModifier),
        verticalArrangement = Arrangement.Center
    ) {
        if (member != null) {
            Spacer(modifier = Modifier.height(7.33.dp))
            Text(
                text = member.firstName,
                style = DTTextFieldTextStyle.copy(
                    fontSize = 16.sp,
                    color = White,
                    fontWeight = FontWeight.W700,
                    lineHeight = 22.sp
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis, modifier = Modifier.fillMaxWidth()
            )
            Spacer(Modifier.size(2.dp))
            Text(
                text = member.email,
                style = DTTextFieldTextStyle.copy(
                    fontSize = 12.sp,
                    color = White50,
                    lineHeight = 18.sp,
                ),
                maxLines = 1, overflow = TextOverflow.Ellipsis, modifier = Modifier.fillMaxWidth()
            )
        } else {
            Text(
                text = guestInfo?.tourists_code ?: "Guest",
                style = DTTextFieldTextStyle.copy(
                    fontSize = 12.sp,
                    lineHeight = 18.sp,
                    color = White50
                ),
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
                modifier = Modifier.fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(10.dp))
            val annotatedString = loginAndSignUpAnnotationString(White)
            ClickableText(
                text = annotatedString,
                onClick = {
                    annotatedString
                        .getStringAnnotations(tag = TAG_LOGIN, start = it, end = it)
                        .firstOrNull()?.let { _ -> onLogin() }
                    annotatedString
                        .getStringAnnotations(tag = TAG_SIGNUP, start = it, end = it)
                        .firstOrNull()?.let { _ -> onSignUp() }
                },
                style = DTTextFieldTextStyle.copy(
                    fontWeight = FontWeight.W700,
                    fontSize = 16.sp,
                    lineHeight = 24.sp,
                    color = colorResource(R.color.color_747474)
                )
            )

        }
    }
}

@Composable
private fun TopBarSetting(onBack: () -> Unit, onEnterUserInfo: () -> Unit) {
    Row(
        Modifier
            .fillMaxWidth()
            .padding(start = 0.dp, top = 18.dp, end = 0.dp, bottom = 0.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f),
            horizontalAlignment = Alignment.Start,
            verticalArrangement = Arrangement.Center
        ) {
            Image(
                painter = painterResource(R.drawable.ic_close_sv),
                modifier = Modifier
                    .size(24.dp, 24.dp)
                    .clip(CircleShape)
                    .clickable(onClick = onBack),
                contentDescription = "backIcon",
                contentScale = ContentScale.FillBounds
            )
        }
        Column(
            modifier = Modifier.weight(1f),
            horizontalAlignment = Alignment.End,
            verticalArrangement = Arrangement.Center
        ) {
            Image(
                painter = painterResource(R.drawable.ic_user_setting),
                modifier = Modifier
                    .size(20.dp, 22.dp)
                    .clip(CircleShape)
                    .clickable(enabled = Membership.isLogin(), onClick = onEnterUserInfo),
                contentDescription = "setting",
                contentScale = ContentScale.FillBounds
            )
        }
    }
}

@Composable
private fun VipState(
    viewModel: MineViewModel = viewModel(),
    onEnterProduct: () -> Unit,
    onBuyProduct: () -> Unit
) {
    val memberVipState by viewModel.memberVipState.collectAsState()
    val guestVipState by viewModel.guestVipState.collectAsState()
    val isVip by Membership.vipStateFlow.collectAsState()
    val isLogin by Membership.loginStateFlow.collectAsState()
    Box {
        //背景
        Image(
            painterResource(
                if (isVip) R.drawable.bg_vip_state else if (isLogin) R.drawable.bg_common_state_user else R.drawable.bg_common_state_guest
            ),
            contentDescription = "bg",
            contentScale = ContentScale.FillBounds,
            modifier = Modifier
                .click(onEnterProduct)
                .fillMaxWidth()
                .aspectRatio(343 / 112f)
        )
        //权益信息
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(343 / 112f),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (isVip) {
                Text(
                    text = if (isLogin) memberVipState?.licenseName
                        ?: "" else guestVipState?.licenseName ?: "",
                    style = DTTextFieldTextStyle.copy(
                        fontSize = 15.sp,
                        lineHeight = 20.sp,
                        color = White,
                        fontWeight = FontWeight.W700,
                        textAlign = TextAlign.Center
                    ),
                    modifier = Modifier.fillMaxWidth(),
                )
                DTVerticalSpacer(3.dp)
                val expires = stringResource(R.string.expires_title)
                Text(
                    text = "$expires${if (isLogin) memberVipState?.vipFailureTimeText ?: "" else guestVipState?.vipFailureTimeText ?: ""}",
                    style = DTTextFieldTextStyle.copy(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W500,
                        color = White
                    ),
                    minLines = 1
                )
            } else {
                Text(
                    text = choosePlanAnnotationString(),
                    style = DTTextFieldTextStyle.copy(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W600,
                        color = White,
                        lineHeight = 20.sp, textAlign = TextAlign.Center
                    ),
                    modifier = Modifier.fillMaxWidth(),
                )
            }
            DTVerticalSpacer(if (isVip) 13.dp else 8.dp)
            //按钮
            DTButton(
                text = stringResource(if (isVip) R.string.upgrade else R.string.unlock_now_buy),
                textStyle = DTTextFieldTextStyle.copy(
                    color = colorResource(R.color.color_13111B),
                    fontSize = 12.sp,
                    lineHeight = 16.sp,
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.W500
                ),
                shape = RoundedCornerShape(100.dp),
                background = White,
                modifier = Modifier
                    .size(131.dp, 32.dp)
            ) {
                onEnterProduct()
            }
        }
    }
}

@Composable
private fun choosePlanAnnotationString(): AnnotatedString {
    val choosPlan = stringResource(R.string.choose_your_plan_boost_your_pleasure)
    val plan = stringResource(R.string.plan)
    val color = Color(0xFFFAD607)
    return remember(color, choosPlan, plan) {
        choosPlan.toAnnotatedStringParameters(
            ChildDesign(
                childString = plan,
                annotatedTag = "plan",
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.W600
                )
            )
        )
    }
}

@Composable
private fun ItemMine(@DrawableRes img: Int, @StringRes title: Int, onClick: () -> Unit) {
    Row(
        Modifier
            .fillMaxWidth()
            .click(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painterResource(img),
            contentDescription = "icon",
            modifier = Modifier.size(24.dp),
            tint = White
        )
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            text = stringResource(title),
            fontSize = 14.sp,
            lineHeight = 24.sp,
            modifier = Modifier.weight(1f),
            color = White
        )
        Icon(
            painterResource(R.drawable.ic_right_arrow1),
            modifier = Modifier.size(16.dp),
            contentDescription = "more", tint = colorResource(R.color.color_828589)
        )
    }
}

@Composable
fun ItemLanguage(language: String, onClick: () -> Unit) {
    Row(
        Modifier
            .fillMaxWidth()
            .click(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painterResource(R.drawable.ic_lang),
            contentDescription = "icon",
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            text = stringResource(R.string.language),
            fontSize = 14.sp,
            lineHeight = 24.sp,
            modifier = Modifier.weight(1f),
            color = White
        )
        Text(
            text = language,
            fontSize = 14.sp,
            lineHeight = 24.sp,
            color = colorResource(R.color.color_6c6c6c)
        )
        Icon(
            painterResource(R.drawable.ic_right_arrow1),
            modifier = Modifier.size(16.dp),
            contentDescription = "more",
            tint = colorResource(R.color.color_828589)
        )
    }
}

@Composable
private fun MineItems(
    member: MemberBean?,
    onItemClick: (MainRoute) -> Unit,
    onTerms: () -> Unit,
    onPrivacy: () -> Unit
) {
    val state = rememberScrollState()
    Column(
        modifier = Modifier
            .background(White5, shape = RoundedCornerShape(12.dp))
            .fillMaxWidth()
            .padding(top = 16.dp, bottom = 20.dp, start = 16.dp, end = 16.dp)
            .verticalScroll(state)
    ) {
        val language by UserManager.languageFlow.collectAsStateWithLifecycle()
        ItemLanguage(language.title) {
            onItemClick(MainRoute.Language)
        }
        Spacer(
            Modifier
                .fillMaxWidth()
                .height(16.dp)
        )
        HorizontalDivider(thickness = 1.dp, color = White5)
        Spacer(
            Modifier
                .fillMaxWidth()
                .height(16.dp)
        )
        if (member != null) {
            ItemMine(R.drawable.ic_order, R.string.my_order) {
                onItemClick(MainRoute.MyOrder)
            }
            Spacer(
                Modifier
                    .fillMaxWidth()
                    .height(16.dp)
            )
            HorizontalDivider(thickness = 1.dp, color = White5)
            Spacer(
                Modifier
                    .fillMaxWidth()
                    .height(16.dp)
            )
        }
        ItemMine(R.drawable.ic_about_me, R.string.about_me) {
            onItemClick(MainRoute.AboutMe)
        }
        Spacer(
            Modifier
                .fillMaxWidth()
                .height(16.dp)
        )
        HorizontalDivider(thickness = 1.dp, color = White5)
        Spacer(
            Modifier
                .fillMaxWidth()
                .height(16.dp)
        )
        ItemMine(R.drawable.ic_feedback, R.string.feedback) {
            onItemClick(MainRoute.Feedback)
        }
        Spacer(
            Modifier
                .fillMaxWidth()
                .height(16.dp)
        )
        HorizontalDivider(thickness = 1.dp, color = White5)
        Spacer(
            Modifier
                .fillMaxWidth()
                .height(16.dp)
        )
        ItemMine(R.drawable.ic_terms, R.string.terms_of_service) { onTerms() }
        Spacer(
            Modifier
                .fillMaxWidth()
                .height(16.dp)
        )
        HorizontalDivider(thickness = 1.dp, color = White5)
        Spacer(
            Modifier
                .fillMaxWidth()
                .height(16.dp)
        )
        ItemMine(R.drawable.ic_privacy, R.string.policy) { onPrivacy() }
    }
}

@Composable
private fun loginAndSignUpAnnotationString(annotatedStrColor: Color): AnnotatedString {
    val mainString = stringResource(id = R.string.please_login)
    val login = stringResource(id = R.string.log_in)
    val signup = stringResource(id = R.string.sign_up)
    return remember(mainString, login, signup, annotatedStrColor) {
        mainString.toAnnotatedStringParameters(
            ChildDesign(
                childString = login,
                annotatedTag = TAG_LOGIN,
                spanStyle = SpanStyle(
                    color = annotatedStrColor,
                    fontWeight = FontWeight.W600
                )
            ),
            ChildDesign(
                childString = signup,
                annotatedTag = TAG_SIGNUP,
                spanStyle = SpanStyle(
                    color = annotatedStrColor,
                    fontWeight = FontWeight.W600,
                )
            )
        )
    }
}

/**
 * 用户头像
 */
@Composable
private fun UserAvatar(member: MemberBean?, onEnterUserInfo: () -> Unit) {
    val resource = when {
        member == null -> {
            R.drawable.ic_avatar_sv
        }

        member.vipTypeOfProduct == 2 -> {
            R.drawable.ic_avatar_vip_sv
        }

        else -> {
            R.drawable.ic_avatar_common_sv
        }
    }
    Image(
        painterResource(resource),
        contentDescription = "avatar",
        modifier = Modifier
            .clickable(enabled = member != null, onClick = onEnterUserInfo)
            .size(60.dp, 60.dp)
    )

}