package com.solvibe.main.compose.utils

import androidx.compose.ui.text.AnnotatedString.Builder
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.LinkInteractionListener
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLinkStyles
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withLink

inline fun Builder.withUrl(
    text: String,
    url: String,
    style: SpanStyle = SpanStyle(textDecoration = TextDecoration.Underline),
    crossinline onLinkClick: (url: String) -> Unit,
) {
    withLink(clickableLink(url, style, onLinkClick)) {
        append(text)
    }
}

inline fun clickableLink(
    url: String,
    style: SpanStyle,
    crossinline onLinkClick: (url: String) -> Unit
): LinkAnnotation {
    return LinkAnnotation.Url(
        url = url,
        styles = TextLinkStyles(style),
        object : LinkInteractionListener {
            override fun onClick(link: LinkAnnotation) {
                onLinkClick((link as LinkAnnotation.Url).url)
            }
        }
    )
}

inline fun Builder.withClickable(
    text: String,
    tag: String,
    style: SpanStyle = SpanStyle(),
    crossinline onLinkClick: (url: String) -> Unit,
) {
    withLink(clickableSpan(tag, style, onLinkClick)) {
        append(text)
    }
}

inline fun clickableSpan(
    tag: String,
    style: SpanStyle,
    crossinline onLinkClick: (tag: String) -> Unit
): LinkAnnotation {
    return LinkAnnotation.Clickable(
        tag = tag,
        styles = TextLinkStyles(style),
        object : LinkInteractionListener {
            override fun onClick(link: LinkAnnotation) {
                onLinkClick((link as LinkAnnotation.Clickable).tag)
            }
        }
    )
}