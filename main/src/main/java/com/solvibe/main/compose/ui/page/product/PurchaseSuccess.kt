package com.solvibe.main.compose.ui.page.product

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.gson.Gson
import com.imyfone.membership.api.bean.ConfirmResultBean
import com.solvibe.Membership
import com.solvibe.main.R
import com.solvibe.main.compose.theme.Black
import com.solvibe.main.compose.theme.SocialBlue
import com.solvibe.main.compose.theme.White
import com.solvibe.main.compose.ui.components.DTTextFieldTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.utils.ChildDesign
import com.solvibe.main.compose.utils.toAnnotatedStringParameters

/**
 *creater:linjinhao on 2025/5/20 18:41
 */

private val TAG_EMAIL = "TAG_EMAIL"

@Composable
fun PurchaseSuccessRoute(bean: String, onBack: () -> Unit) {
    val gson = Gson()
    val confirmResultBean = gson.fromJson(bean, ConfirmResultBean::class.java)
    PurchaseSuccessScreen(bean = confirmResultBean, onBack)
}

@Composable
fun PurchaseSuccessScreen(bean: ConfirmResultBean, onBack: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(White)
    ) {
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            ) {
                Icon(
                    painterResource(R.drawable.ic_buy_close),
                    contentDescription = "closeButton",
                    modifier = Modifier
                        .padding(start = 16.dp)
                        .clickable {
                            onBack()
                        }
                        .size(24.dp),
                    tint = Black,
                )
            }
            DTVerticalSpacer(30.dp)
            Image(
                painterResource(R.drawable.ic_success),
                contentDescription = "icon",
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .size(36.dp),
                alignment = Alignment.Center
            )
            DTVerticalSpacer(20.dp)
            Text(
                stringResource(R.string.payment_successful),
                style = DTTextFieldTextStyle.copy(
                    color = Black,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.W800,
                    textAlign = TextAlign.Center
                ),
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
            )
            DTVerticalSpacer(20.dp)
            Column(
                modifier = Modifier
                    .padding(horizontal = 12.dp)
                    .background(
                        colorResource(R.color.colorViewBackground),
                        shape = RoundedCornerShape(16.dp)
                    )
                    .padding(vertical = 20.dp, horizontal = 12.dp)
                    .fillMaxWidth()
            ) {
                ItemDetail(stringResource(R.string.product_name), bean.skuName ?: "")
                Spacer(modifier = Modifier.height(16.dp))
                ItemDetail(stringResource(R.string.order_number), bean.orderNo ?: "")
                Spacer(modifier = Modifier.height(16.dp))
                ItemDetail(stringResource(R.string.total_text), "${bean.currencyCode}${bean.price}")
                Spacer(modifier = Modifier.height(16.dp))
                ItemDetail(
                    "Auto-renewal",
                    if (bean.isSubscribe == 1) stringResource(R.string.active) else stringResource(R.string.disabled)
                )
            }
            Spacer(modifier = Modifier.height(24.dp))
            if (Membership.isLogin()) {
                Text(
                    text = sendEmailAnnotationString(Membership.userStateFlow.value?.email ?: ""),
                    style = TextStyle(
                        fontSize = 14.sp,
                        color = colorResource(R.color.colorTextAccent)
                    ), modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .fillMaxWidth()
                )
            }
        }
    }
}

@Composable
fun ItemDetail(title: String, context: String) {
    Row(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = title,
            style = TextStyle(fontSize = 14.sp, color = colorResource(R.color.colorTextAccent))
        )
        Text(
            text = context,
            style = TextStyle(
                fontSize = 14.sp,
                color = colorResource(R.color.colorTextAccent),
                textAlign = TextAlign.End
            ),
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
fun sendEmailAnnotationString(email: String): AnnotatedString {
    val mainStr = stringResource(R.string.email_has_send)
    return remember(email) {
        mainStr.toAnnotatedStringParameters(
            ChildDesign(
                childString = email,
                annotatedTag = TAG_EMAIL,
                spanStyle = SpanStyle(
                    color = SocialBlue,
                    fontWeight = FontWeight.W600
                )
            )
        )
    }
}
