package com.solvibe.main.compose.ui.page.main.mine.login

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.solvibe.AccountStateEvent
import com.solvibe.Membership
import com.solvibe.character.net.util.EncryptedUtil
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 *creater:linjinhao on 2025/5/15 18:48
 */
class SvLoginViewModel : ViewModel() {
    private val account = Membership.membershipClient.account

    private val _loadingState = MutableStateFlow(LoginUIState())
    val loadingState = _loadingState.asStateFlow()

    private val _loginEvent = MutableSharedFlow<LoginEvent>()
    val loginEvent = _loginEvent.asSharedFlow()

    fun login(email: String, pwd: String) {
        viewModelScope.launch {
            _loadingState.update { it.copy(loading = true) }
            val pwdStr = EncryptedUtil.md5(pwd)
            val touristsTokenIsBind = Membership.touristsTokenDataStateFlow.value?.isBound == 1
            val response = if (touristsTokenIsBind) {
                account.login(email = email, password = pwdStr, "")
            } else {
                account.login(email = email, password = pwdStr)
            }
            _loadingState.update { it.copy(loading = false) }
            if (response.isSuccess) {
                Membership.sendAccountStateEvent(AccountStateEvent.LoggedIn)
                _loginEvent.emit(LoginEvent.LoginSuccess)
            } else if (response.code == 410) {
                _loginEvent.emit(LoginEvent.AccountNotExist)
            } else if (response.code == 409) {
                _loginEvent.emit(LoginEvent.PasswordInvalid)
            } else {
                _loginEvent.emit(LoginEvent.CommonError)
            }
        }
    }
}

data class LoginUIState(
    val loading: Boolean = false
)

sealed interface LoginEvent {
    data object LoginSuccess : LoginEvent
    data object AccountNotExist : LoginEvent
    data object PasswordInvalid : LoginEvent
    data object CommonError : LoginEvent
}
