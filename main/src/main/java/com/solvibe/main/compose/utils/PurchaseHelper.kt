package com.solvibe.main.compose.utils

/**
 *creater:<PERSON><PERSON><PERSON><PERSON> on 2025/5/20 17:32
 */
/*internal fun Exception.toPurchaseTips(context: Context): String {
    return when (this) {
        is ClientConnectException -> {
            context.getString(R.string.membership_ui_google_connect_exception)
        }

        DuplicatePurchaseException -> {
            context.getString(R.string.membership_ui_duplicate_purchase)
        }

        ProductAbsentException -> {
            context.getString(R.string.membership_ui_product_not_exist)
        }

        GooglePurchaseException -> {
            context.getString(R.string.membership_ui_purchase_order_exception)
        }

        FeatureNotSupportedException, GooglePurchaseDisabledException -> {
            context.getString(R.string.membership_ui_google_version_low)
        }

        is PurchaseFailException -> {
            context.getString(R.string.membership_ui_purchase_fail)
        }

        is PayException -> {
            context.getString(R.string.membership_ui_common_pay_fail, "${this.code}")
        }

        else -> {
            context.getString(R.string.membership_ui_common_pay_fail, "-1")
        }
    }
}*/
