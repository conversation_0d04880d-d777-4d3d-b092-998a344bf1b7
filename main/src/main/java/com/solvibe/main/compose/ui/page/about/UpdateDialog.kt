package com.solvibe.main.compose.ui.page.about

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.solvibe.main.R
import com.solvibe.main.bean.VersionBean
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTButtonTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer

@Composable
fun UpdateDialog(
    versionBean: VersionBean,
    onDismiss: () -> Unit,
    onUpdate: () -> Unit
) {
    Dialog(
        onDismissRequest = {},
        DialogProperties(usePlatformDefaultWidth = false)
    ) {
        ConstraintLayout(
            Modifier
                .width(300.dp)
                .height(444.dp)
        ) {
            val (bgImageRef, bgRef) = createRefs()
            Image(
                painterResource(R.drawable.bg_update_dialog),
                null,
                Modifier
                    .fillMaxWidth()
                    .height(252.dp)
                    .constrainAs(bgImageRef) {
                        top.linkTo(parent.top)
                    }
            )
            Box(
                Modifier
                    .constrainAs(bgRef) {
                        top.linkTo(bgImageRef.bottom)
                        bottom.linkTo(parent.bottom)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        width = Dimension.fillToConstraints
                        height = Dimension.fillToConstraints
                    }
                    .background(
                        MaterialTheme.colorScheme.onPrimary,
                        RoundedCornerShape(bottomStart = 16.dp, bottomEnd = 16.dp)
                    ),
            )

            Column(Modifier.fillMaxSize()) {
                DTVerticalSpacer(108.dp)
                Text(
                    versionBean.ver,
                    Modifier.padding(start = 20.dp),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = Color(0xFF1E173D),
                        lineHeight = 20.sp
                    )
                )
                DTVerticalSpacer(10.dp)
                Text(
                    stringResource(R.string.update_available),
                    Modifier.padding(start = 20.dp),
                    style = MaterialTheme.typography.headlineLarge.copy(
                        color = Color(0xFF1E173D),
                        fontSize = 16.sp
                    )
                )
                DTVerticalSpacer(36.dp)
                Text(
                    versionBean.info,
                    Modifier
                        .padding(horizontal = 20.dp)
                        .fillMaxWidth()
                        .weight(1f)
                        .verticalScroll(rememberScrollState()),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = Color(0xFF1E173D),
                        lineHeight = 20.sp
                    )
                )
                DTVerticalSpacer(30.dp)
                DTButton(
                    R.string.update,
                    Modifier
                        .padding(horizontal = 40.dp)
                        .fillMaxWidth()
                        .height(46.dp),
                    containerColor = Color(0xFFEA557F),
                    onClick = onUpdate
                )
                if (!versionBean.isForce_update) {
                    DTVerticalSpacer(12.dp)
                    DTButton(
                        stringResource(R.string.cancel),
                        Modifier
                            .padding(horizontal = 40.dp)
                            .fillMaxWidth()
                            .height(46.dp),
                        border = BorderStroke(1.5.dp, Color(0xFFEA557F)),
                        textStyle = DTButtonTextStyle.copy(color = Color(0xFFEA557F)),
                        onClick = onDismiss
                    )
                }
                DTVerticalSpacer(32.dp)
            }
        }
    }
}