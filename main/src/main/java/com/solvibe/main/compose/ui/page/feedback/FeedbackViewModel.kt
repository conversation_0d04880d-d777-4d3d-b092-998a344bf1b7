package com.solvibe.main.compose.ui.page.feedback

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.solvibe.Membership
import com.solvibe.main.compose.utils.ILoadingState
import com.solvibe.main.compose.utils.loadingState
import com.solvibe.main.ext.showToast
import com.solvibe.main.repo.AppRepo
import com.solvibe.main.utils.NetWorkManager
import kotlinx.coroutines.launch

class FeedbackViewModel : ViewModel(),
    ILoadingState by loadingState() {
    private val appRepo = AppRepo()

    var showBackDialog by mutableStateOf(false)
        private set

    var feedback by mutableStateOf("")
        private set
    var email by mutableStateOf("")
        private set

    init {
        viewModelScope.launch {
            email = Membership.getEmail().orEmpty()
        }
    }

    fun showBackDialog(show: <PERSON>ole<PERSON>) {
        showBackDialog = show
    }

    fun onInput(content: String) {
        feedback = content
    }

    fun onInputEmail(content: String) {
        email = content
    }

    fun submit(onResp: (Int?) -> Unit) {
        if (!NetWorkManager.isNetworkConnectedSystem()) {
            showToast("Failed to send, please try again")
            return
        }
        viewModelScope.launch {
            showLoading(true)
            val resp = appRepo.submitFeedback(email, "Feedback", feedback)
            onResp(resp?.code)
            showLoading(false)
        }
    }
}