package com.solvibe.main.compose.ui.page.product

import com.imyfone.membership.api.bean.SKUBean
import com.imyfone.membership.repository.ICartRepository
import com.solvibe.Membership
import com.solvibe.main.bean.ProductBean
import com.solvibe.main.config.Constant


/**
 * 查询google价格
 */
suspend fun queryGooglePrice(data: List<SKUBean>, iCart: ICartRepository): List<ProductBean> {
    val productDetails = try {
        iCart.googlePurchase.querySubsProducts(*data.map { it.skuID }
            .toTypedArray())
    } catch (e: Exception) {
        emptyList()
    }

    return data.map { sku ->
        val product = productDetails.firstOrNull {
            it.productId == sku.skuID
        }
        //真实价格
        val actualSubscriptionOfferDetail =
            product?.subscriptionOfferDetails?.firstOrNull {
                Regex(Constant.PURCHASE_RENEWAL_PLAN_ID_REGEX).matches(it.basePlanId)
            }
        val priceCode =
            actualSubscriptionOfferDetail?.pricingPhases?.pricingPhaseList?.firstOrNull()?.priceCurrencyCode
                ?: "USD"
        val localPrecent =
            try {
                if (sku.buyList.firstOrNull()?.virtualPrice != null && sku.buyList.firstOrNull()?.actualPrice != null) {
                    sku.buyList.firstOrNull()?.actualPrice?.toFloatOrNull()
                        ?.div(
                            sku.buyList.firstOrNull()?.virtualPrice?.toFloatOrNull()
                                ?: 0f
                        )?.times(100) ?: 0f
                } else {
                    0f
                }
            } catch (e: Exception) {
                0f
            }

        val actualPrice =
            actualSubscriptionOfferDetail?.pricingPhases?.pricingPhaseList?.firstOrNull()
        //虚拟价格
        val virtualSubscriptionOfferDetail =
            product?.subscriptionOfferDetails?.firstOrNull {
                Regex(Constant.PURCHASE_VIRTUAL_PLAN_ID_REGEX).matches(it.basePlanId)
            }
        val virtualPrice =
            virtualSubscriptionOfferDetail?.pricingPhases?.pricingPhaseList?.firstOrNull()
        val discountPercent = try {
            virtualPrice?.formattedPrice?.toFloatOrNull()
                ?.div(actualPrice?.formattedPrice?.toFloatOrNull() ?: 0f)
                ?.times(100) ?: 0f
        } catch (e: Exception) {
            0f
        }
        ProductBean(
            isDiscount = false,
            licenseName = sku.name,
            currencyCode = priceCode,
            localPercent = localPrecent,
            discountPercent = discountPercent,
            licenseId = sku.licenseID ?: "",
            virtualPrice = virtualPrice?.formattedPrice
                ?: sku.buyList.firstOrNull()?.virtualPrice ?: "",
            actualPrice = actualPrice?.formattedPrice ?: sku.buyList.firstOrNull()?.actualPrice
            ?: "",
            actualPriceAmount = actualPrice?.priceAmountMicros?.div(1000_000f)
                ?: (sku.buyList.firstOrNull()?.actualPrice?.toFloatOrNull() ?: 0f),
            virtualPriceAmount = virtualPrice?.priceAmountMicros?.div(1000_000f)
                ?: (sku.buyList.firstOrNull()?.virtualPrice?.toFloatOrNull() ?: 0f),
            offerToken = actualSubscriptionOfferDetail?.offerToken ?: "",
            skuBean = sku,
        )
    }
}

/**
 * 刷新会员状态
 */
suspend fun refreshVipState() {
    if (Membership.isLogin()) {
        Membership.permissionRepo.refreshMemberVipState()
    } else {
        Membership.permissionRepo.refreshGuestVipState()
    }
}