package com.solvibe.main.compose.ui.page.product

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.imyfone.membership.api.bean.SKUBean
import com.imyfone.membership.api.bean.UserBean
import com.imyfone.membership.exception.NeedLoginException
import com.imyfone.membership.exception.UserCancelException
import com.imyfone.membership.repository.ConfirmGooglePurchaseResult
import com.solvibe.Membership
import com.solvibe.main.BuildConfig
import com.solvibe.main.R
import com.solvibe.main.bean.ProductBean
import com.solvibe.main.config.ChannelType
import com.solvibe.main.config.Constant
import com.solvibe.utils.ext.logd
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

/**
 *creater:linjinhao on 2025/5/19 16:55
 */
class NewProductViewModel : ViewModel() {
    private val account = Membership.membershipClient.account
    private val guestRepo = Membership.membershipClient.guest
    private val iCart = Membership.membershipClient.iCart
    private val _state = MutableStateFlow(ProductUIState())
    val state = _state.asStateFlow()

    var userBean: UserBean? = null
    var selectIndex by mutableIntStateOf(0)

    val skus = mutableStateListOf<ProductBean>()

    private val _event = MutableSharedFlow<ProductEvent>()
    val event = _event.asSharedFlow()

    init {
        initGuest()
        getProduct()
        collectUser()
    }


    fun setSku(list: List<ProductBean>) {
        skus.clear()
        skus.addAll(list)
    }



    @Composable
    fun getBuyBtnTxt(): String {
        if (skus.isEmpty()) {
            return stringResource(R.string.new_buy_payage_btn_continue)
        }
        val productBean = skus[selectIndex]
        val str = when {
            productBean.isMonthSubscribe() -> stringResource(R.string.buy_now)
            productBean.isYearSubscribe() -> "Continue with ${(100 - productBean.localPercent).roundToInt()}% Off"
            else -> stringResource(R.string.buy_now)
        }
        return str
    }

    fun getSkuUnitStr(productBean: ProductBean): String {
        return when {
            productBean.isDaySubscribe() -> {
                "/Day"
            }

            productBean.isWeekSubscribe() -> {
                "/Day"
            }

            productBean.isMonthSubscribe() -> {
                "/Day"
            }

            productBean.isQuarterSubscribe() -> {
                "/Month"
            }

            productBean.isYearSubscribe() -> {
                "/Day"
            }

            productBean.isOneTimeSubscribe() -> {
                "/Day"
            }

            else -> {
                "Day"
            }
        }
    }


    fun purchase(skuBean: SKUBean) {
        viewModelScope.launch {
            // 1. 拉取当前支付方式
            _state.update { it.copy(purchaseLoading = true) }
            // 2. 开始购买
            if (BuildConfig.channel == ChannelType.CHANNEL_GOOGLE) {
                purchaseGoogle(skuBean)
                _state.update { it.copy(purchaseLoading = false) }
            } else {
                purchaseICart(skuBean)
                _state.update { it.copy(purchaseLoading = false) }
            }
        }
    }

    private fun collectUser() {
        viewModelScope.launch {
            account.userFlow.collectLatest { user ->
                userBean = user
            }
        }
    }

    private fun getProduct() {
        viewModelScope.launch {
            _state.update { it.copy(skuState = SkuUIState.Loading) }
            val response = account.getSKUList()
            val data = response.data
            if (response.isSuccess) {
                val skus = if (data.isNullOrEmpty()) {
                    emptyList()
                } else {
                    queryGooglePrice(data = data, iCart)
                }
                setSku(skus)
                _state.update {
                    it.copy(skuState = SkuUIState.Success(data ?: emptyList()))
                }
            } else {
                _state.update { it.copy(skuState = SkuUIState.Fail) }
            }
        }
    }

    private fun initGuest() {
        viewModelScope.launch {
            guestRepo.loginWithRegister(Constant.fromSite)
        }
    }

    private suspend fun purchaseGoogle(skuBean: SKUBean) {
        try {
            logd("purchaseGoogle skuid:${skuBean.skuID} ")
            val googlePurchaseBean = iCart.googlePurchase(
                skuBean = skuBean,
                source = Constant.fromSite,
                selectTokenBlock = { detail ->
                    detail?.firstOrNull {
                        Regex(Constant.PURCHASE_RENEWAL_PLAN_ID_REGEX).matches(it.basePlanId)
                    }?.offerToken ?: ""
                }
            )
            var confirming = true
            while (confirming) {
                val result = iCart.confirmGooglePurchase(googlePurchaseBean)
                when (result) {
                    ConfirmGooglePurchaseResult.Pending -> {
                    }

                    is ConfirmGooglePurchaseResult.Success -> {
                        confirming = false
                        refreshVipState()
                        _event.emit(ProductEvent.GooglePurchaseSuccess(result.confirmResultBean))
                    }

                    ConfirmGooglePurchaseResult.Fail -> {
                        confirming = false
                        _event.emit(ProductEvent.GoogleConfirmOrderFail)
                    }
                }
                delay(1_000)
            }
        } catch (cancel: UserCancelException) {
            _event.emit(ProductEvent.CancelGooglePurchase)
        } catch (e: NeedLoginException) {
            _event.emit(ProductEvent.NeedLogin)
        } catch (e: Exception) {
            logd("purchaseGoogle error:${e.message},${e.printStackTrace()}")
            _event.emit(ProductEvent.GooglePurchaseFail(e))
        }
    }

    private suspend fun purchaseICart(skuBean: SKUBean) {
        try {
            if (userBean == null) {
                _event.emit(ProductEvent.NeedLogin)
                return
            }
            val email = iCart.iCartPurchase(skuBean)
            refreshVipState()
            if (email.isNotEmpty() && userBean == null) {
                _event.emit(ProductEvent.LoginTip(email))
            }
            if (email.isNotEmpty() && userBean != null) {
                _event.emit(ProductEvent.ICartPurchaseFinish)
            }
        } catch (e: NeedLoginException) {
            _event.emit(ProductEvent.NeedLogin)
        }
    }


}

