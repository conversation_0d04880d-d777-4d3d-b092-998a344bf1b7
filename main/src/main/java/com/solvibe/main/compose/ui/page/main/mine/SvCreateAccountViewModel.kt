package com.solvibe.main.compose.ui.page.main.mine

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.solvibe.AccountStateEvent
import com.solvibe.Membership
import com.solvibe.character.net.util.EncryptedUtil
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 *creater:linjin<PERSON> on 2025/5/26 11:20
 */
class SvCreateAccountViewModel : ViewModel() {
    private val account = Membership.membershipClient.account

    private val _state = MutableStateFlow(false)
    val state = _state.asStateFlow()
    private val _createAccountEvent = MutableSharedFlow<CreateAccountEvent>()
    val createAccountEvent = _createAccountEvent.asSharedFlow()

    fun createAccount(email: String, password: String, state: String) {
        viewModelScope.launch {
            _state.emit(true)
            val pwd = EncryptedUtil.md5(password)
            val response = account.authBindEmail(email, pwd, state)
            _state.emit(false)
            if (response.isSuccess) {
                Membership.sendAccountStateEvent(AccountStateEvent.LoggedIn)
                _createAccountEvent.emit(CreateAccountEvent.Success)
            } else {
                when (response.code) {
                    209, 409 -> {
                        //密码错误
                        _createAccountEvent.emit(CreateAccountEvent.PasswordError)
                    }

                    415 -> {
                        //注册密码格式错误
                        _createAccountEvent.emit(CreateAccountEvent.PasswordFormatError)
                    }

                    414 -> {
                        //注册密码长度必须在6-16字符
                        _createAccountEvent.emit(CreateAccountEvent.PasswordLengthError)
                    }

                    462 -> {
                        //会员账号已綁定其他 Google 帳戶
                        _createAccountEvent.emit(CreateAccountEvent.HasBindGoogle)
                    }

                    464 -> {
                        //会员账号已綁定其他 Facebook 帳戶
                        _createAccountEvent.emit(CreateAccountEvent.HasBindFaceBook)
                    }

                    465 -> {
                        //当前 Google 帳戶已綁定其他会员账号
                        _createAccountEvent.emit(CreateAccountEvent.GoogleHasBind)
                    }

                    467 -> {
                        //当前 Facebook 帳戶已綁定其他会员账号
                        _createAccountEvent.emit(CreateAccountEvent.FaceBookHasBind)
                    }

                    461 -> {
                        //会员账号绑定失败
                        _createAccountEvent.emit(CreateAccountEvent.BindFail)
                    }

                    else -> { //普通错误
                        _createAccountEvent.emit(CreateAccountEvent.CommonError)
                    }
                }
            }
        }
    }
}

sealed interface CreateAccountEvent {
    data object Success : CreateAccountEvent
    data object PasswordError : CreateAccountEvent
    data object PasswordFormatError : CreateAccountEvent
    data object PasswordLengthError : CreateAccountEvent
    data object HasBindGoogle : CreateAccountEvent
    data object HasBindFaceBook : CreateAccountEvent
    data object GoogleHasBind : CreateAccountEvent
    data object FaceBookHasBind : CreateAccountEvent
    data object BindFail : CreateAccountEvent
    data object CommonError : CreateAccountEvent
}






