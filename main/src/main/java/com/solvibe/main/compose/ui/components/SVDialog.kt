package com.solvibe.main.compose.ui.components

import androidx.annotation.StringRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.solvibe.main.compose.theme.White
import com.solvibe.main.compose.theme.White70

// 通用的提示+双按钮弹窗
@Composable
fun SVDialog(
    @StringRes title: Int,
    @StringRes cancelText: Int,
    @StringRes confirmText: Int,
    onConfirm: () -> Unit,
    onCancel: () -> Unit,
) {
    Dialog(
        onDismissRequest = onCancel,
        DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Column(
            Modifier
                .padding(horizontal = 34.dp)
                .fillMaxWidth()
                .background(Color(0xFF2F2A3B), RoundedCornerShape(16.dp))
        ) {
            DTVerticalSpacer(32.dp)
            Text(
                stringResource(title),
                Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
                style = MaterialTheme.typography.headlineMedium.copy(
                    color = White,
                    lineHeight = 20.sp,
                    textAlign = TextAlign.Center
                )
            )
            DTVerticalSpacer(32.dp)
            Row(
                Modifier
                    .padding(horizontal = 30.dp)
                    .fillMaxWidth()
            ) {
                DTButton(
                    stringResource(cancelText),
                    Modifier
                        .weight(1f)
                        .height(40.dp),
                    border = BorderStroke(1.dp, Color(0x99999999)),
                    textStyle = DTButtonTextStyle.copy(color = White70),
                    onClick = onCancel
                )
                DTHorizontalSpacer(16.dp)
                val brush = Brush.horizontalGradient(
                    listOf<Color>(
                        Color(0xFFBE4EFF),
                        Color(0xFFF343A1),
                        Color(0xFFFF5476)
                    )
                )
                DTButton(
                    stringResource(confirmText),
                    brush = brush,
                    modifier = Modifier
                        .weight(1f)
                        .height(40.dp),
                    textStyle = DTButtonTextStyle.copy(color = White),
                    onClick = onConfirm
                )
            }
            DTVerticalSpacer(24.dp)
        }
    }
}