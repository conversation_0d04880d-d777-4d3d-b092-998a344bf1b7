package com.solvibe.main.compose.utils

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue

interface ILoadingState {
    val loading: Boolean
    fun showLoading(loading: <PERSON>olean)
}

class LoadingState : ILoadingState {
    private var _loading by mutableStateOf(false)
    override val loading: <PERSON>olean
        get() = _loading

    override fun showLoading(loading: <PERSON>olean) {
        _loading = loading
    }
}

fun loadingState() = LoadingState()