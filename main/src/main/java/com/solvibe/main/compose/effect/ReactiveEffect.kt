package com.solvibe.main.compose.effect

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleResumeEffect

@Composable
fun ReactiveEffect(
    key: Any? = Unit,
    block: () -> Unit,
) {
    var first by remember { mutableStateOf(true) }

    LifecycleResumeEffect(key) {
        if (first) {
            first = false
        } else {
            block()
        }

        onPauseOrDispose {
            if (this.lifecycle.currentState == Lifecycle.State.RESUMED) {
                return@onPauseOrDispose
            }
            first = true
        }
    }
}

@Composable
fun ResumeEffect(
    key: Any? = Unit,
    onResume: () -> Unit,
    onPause: () -> Unit = {},
) {
    LifecycleResumeEffect(key) {
        onResume()

        onPauseOrDispose {
            if (this.lifecycle.currentState == Lifecycle.State.RESUMED) {
                return@onPauseOrDispose
            }
            onPause()
        }
    }
}