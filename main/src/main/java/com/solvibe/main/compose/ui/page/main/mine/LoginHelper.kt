package com.solvibe.main.compose.ui.page.main.mine

import android.text.TextUtils
import com.solvibe.main.config.EMAIL_REGEX
import java.util.regex.Pattern

/**
 *creater:<PERSON><PERSON><PERSON><PERSON> on 2025/5/21 20:22
 */

fun checkEmail(email: String): <PERSON><PERSON><PERSON> {
    return when {
        email.isEmpty() -> {
            false
        }

        !isEmailFormat(email) -> {
            false
        }

        else -> {
            true
        }
    }
}

fun isEmailFormat(email: String): <PERSON><PERSON><PERSON> {
    return if (TextUtils.isEmpty(email)) false
    else {
        EMAIL_REGEX.matches(email)
    }
}


fun checkAccountPassword(pwd: String): <PERSON><PERSON><PERSON> {
    return when {
        pwd.isEmpty() -> {
            false
        }

        !isPwdFormat(pwd) -> {
            false
        }

        !isPwdContanSpecial(pwd) -> {
            false
        }

        else -> {
            true
        }
    }
}


private fun isPwdFormat(pwd: CharSequence?): Boolean {
    if (pwd?.length in 6 until 17) {
        return pwd?.contains(" ", true) != true
    }
    return false
}

fun isPwdContanSpecial(pwd: CharSequence?): Bo<PERSON>an {
    val pattern = Pattern.compile("[a-zA-Z0-9\\[\\]!\"#$%&'()*+,-./:;<=>?@\\\\^_`{|}~]+$")
    return pattern.matcher(pwd).matches()
}