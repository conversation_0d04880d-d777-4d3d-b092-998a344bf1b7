package com.solvibe.main.compose.ui.page.main.mine.userInfo

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.imyfone.membership.repository.VerificationCode
import com.solvibe.Membership
import com.solvibe.main.bean.UserManager
import com.solvibe.main.compose.utils.ILoadingState
import com.solvibe.main.compose.utils.loadingState
import com.solvibe.main.ext.stateInViewModel
import com.solvibe.utils.ext.logd
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

/**
 *creater:lin<PERSON><PERSON> on 2025/5/27 11:40
 */
class SvUserInfoViewModel : ViewModel(), ILoadingState by loadingState() {
    private val client = Membership.membershipClient
    private val account = Membership.membershipClient.account
    val member = account.memberFlow.stateInViewModel()
    private val _userInformationEvent = MutableSharedFlow<UserInformationEvent>()
    val userInformationEvent = _userInformationEvent.asSharedFlow()

    private val _logoutEvent = MutableSharedFlow<LogoutEvent>()
    val logoutEvent = _logoutEvent.asSharedFlow()

    val user = account.userFlow.stateInViewModel()
    val setSendVerificationCodeTime = UserManager.sendVerificationCodeTime
    fun logout() {
        viewModelScope.launch {
            client.clearData()
            logd("UserInfoViewModel", "logout")
        }
    }

    fun cancelAccount(code: String) {
        viewModelScope.launch {
            showLoading(true)
            val response = account.cancelAccount(code = code)
            showLoading(false)
            if (response.isSuccess) {
                _userInformationEvent.emit(UserInformationEvent.CancelAccountSuccess)
            } else if (response.code == 421) {
                _userInformationEvent.emit(UserInformationEvent.CodeInputInvalid)
            } else {
                _userInformationEvent.emit(UserInformationEvent.CancelAccountFail)
            }

        }
    }

    fun getCode(sendEmail: String, email: String) {
        viewModelScope.launch {
            showLoading(true)
            val response = account.sendVerificationCode(
                sendEmail = sendEmail,
                email = email,
                code = VerificationCode.CANCEL_ACCOUNT
            )
            showLoading(false)
            if (response.isSuccess) {
                UserManager.setSendVerificationCodeTime(System.currentTimeMillis())
                val noticeEmail = response.data?.noticeEmail ?: ""
                _userInformationEvent.emit(UserInformationEvent.GetCodeSuccess(email = noticeEmail))
            } else if (response.code == 400) {
                _userInformationEvent.emit(UserInformationEvent.GetCodeToMany(response.msg ?: ""))
            } else {
                _userInformationEvent.emit(UserInformationEvent.GetCodeError)
            }
        }
    }

    fun checkPermissionStatus() {
        viewModelScope.launch {
            showLoading(true)
            val response = account.checkPermissionStatus()
            showLoading(false)
            val data = response.data
            if (response.isSuccess && data != null) {
                when {
                    data.subscribeStatus -> { //有订阅权益
                        _userInformationEvent.emit(UserInformationEvent.AccountExitsSubscriptPermission)
                    }

                    data.permissionStatus -> { //有权益
                        _userInformationEvent.emit(UserInformationEvent.AccountExitsPermission)
                    }

                    else -> {
                        _userInformationEvent.emit(UserInformationEvent.AccountExitNoPermission)
                    }
                }
            } else {
                _userInformationEvent.emit(UserInformationEvent.CommonError)
            }
        }
    }
}

sealed interface LogoutEvent {
    data object Logout : LogoutEvent
}

sealed interface UserInformationEvent {
    data object AccountExitsPermission : UserInformationEvent  //有非订阅的权益
    data object AccountExitsSubscriptPermission : UserInformationEvent //有订阅的权益
    data object AccountExitNoPermission : UserInformationEvent
    data object CommonError : UserInformationEvent

    /**
     * 验证码输入无效
     */
    object CodeInputInvalid : UserInformationEvent

    /**
     * 取消账号成功
     */
    object CancelAccountSuccess : UserInformationEvent

    /**
     * 取消账号失败
     */
    object CancelAccountFail : UserInformationEvent

    /**
     * 获取验证码成功
     */
    class GetCodeSuccess(val email: String) : UserInformationEvent

    /**
     * 获取验证码过于频繁
     */
    class GetCodeToMany(val msg: String) : UserInformationEvent

    /**
     * 获取验证码失败
     */
    object GetCodeError : UserInformationEvent

}