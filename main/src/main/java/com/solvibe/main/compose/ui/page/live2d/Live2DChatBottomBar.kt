package com.solvibe.main.compose.ui.page.live2d

import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.main.App
import com.solvibe.main.R
import com.solvibe.main.compose.effect.ReactiveEffect
import com.solvibe.main.compose.theme.White10
import com.solvibe.main.compose.theme.White35
import com.solvibe.main.compose.theme.White8
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTTextFieldRowGroup
import com.solvibe.main.compose.ui.components.DTTextFieldTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.modifier.click
import com.solvibe.main.ext.toDp
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun Live2DChatBottomBar(
    viewModel: Live2DViewModel = viewModel()
) {
    val appViewModel = App.viewModel
    val context = LocalContext.current
    val imeHeight = WindowInsets.ime.getBottom(LocalDensity.current).toDp
    val scope = rememberCoroutineScope()

    val imeVisible by remember(imeHeight) {
        derivedStateOf { imeHeight > 0.dp }
    }

    BackHandler(viewModel.showChatMore) {
        viewModel.showChatMore(false)
    }

    ReactiveEffect(imeVisible) {
        scope.launch {
            if (imeVisible) {
                delay(300)
                viewModel.showChatMore(false)
            }
        }
    }

    Column(
        Modifier
            .fillMaxWidth()
            .background(Color(0xFF19171F))
            .padding(vertical = 12.dp),
    ) {
        ToolsRow(
            onClickMysteryPuzzle = {
                viewModel.stopAudioAndShowWaitingMessage()
                appViewModel.mysteryPuzzle()
            },
            onClickTarotFortune = {
                viewModel.showTarotFortuneDialog(true)
            },
            onClickTodayNews = {
                viewModel.stopAudioAndShowWaitingMessage()
                appViewModel.todayNews()
            }
        )
        DTVerticalSpacer(10.dp)
        Input(viewModel.inputText, viewModel::onTextInput, imeVisible) click@{
            if (imeVisible) {
                if (viewModel.inputText.isBlank()) return@click
                viewModel.sendMessage()
            } else {
                viewModel.showChatMore(true)
            }
        }
        ChatMoreAnim(
            viewModel.showChatMore,
            imeHeight,
            onNewChatClick = {
                viewModel.stopAudio()
                appViewModel.newChat()
                viewModel.showChatMore(false)
            },
            onResetClick = {
                viewModel.showResetChatDialog(true)
            },
            onShareClick = {
                val shareIntent = Intent().apply {
                    action = Intent.ACTION_SEND
                    // 设置分享的文本内容
                    putExtra(
                        Intent.EXTRA_TEXT,
                        "快来与AI角色聊天吧！https://Solvibe.com/?id=5uUuUloE"
                    )
                    // 设置数据类型为纯文本
                    type = "text/plain"
                }

                // 启动系统分享选择器
                context.startActivity(Intent.createChooser(shareIntent, "分享到..."))
                viewModel.showChatMore(false)
            }
        )
    }
}

@Composable
fun ToolsRow(
    onClickMysteryPuzzle: () -> Unit,
    onClickTarotFortune: () -> Unit,
    onClickTodayNews: () -> Unit,
) {
    LazyRow(
        Modifier.fillMaxWidth(),
        contentPadding = PaddingValues(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        item {
            ToolItem(R.drawable.ic_mystery_puzzle, R.string.mystery_puzzle, onClickMysteryPuzzle)
        }
        item {
            ToolItem(R.drawable.ic_tarot_fortune, R.string.tarot_fortune, onClickTarotFortune)
        }
        item {
            ToolItem(R.drawable.ic_today_news, R.string.today_news, onClickTodayNews)
        }
    }
}

@Composable
fun ToolItem(@DrawableRes icon: Int, @StringRes title: Int, onClick: () -> Unit) {
    Row(
        Modifier
            .height(32.dp)
            .clip(RoundedCornerShape(8.dp))
            .border(1.dp, White10, RoundedCornerShape(8.dp))
            .clickable(onClick = onClick)
            .padding(horizontal = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(painterResource(icon), null, Modifier.size(16.dp), tint = Color.Unspecified)
        DTHorizontalSpacer(7.dp)
        Text(
            stringResource(title),
            style = MaterialTheme.typography.labelMedium.copy(
                color = Color.White,
            )
        )
    }
}

@Composable
fun Input(
    inputText: String,
    onInputTextChange: (String) -> Unit,
    imeVisible: Boolean,
    onSend: () -> Unit,
) {
    DTTextFieldRowGroup(
        value = inputText,
        onValueChange = onInputTextChange,
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .heightIn(46.dp)
            .background(Color(0x1AE8E8E8), RoundedCornerShape(12.dp)),
        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
        keyboardActions = KeyboardActions(onSend = { onSend() }),
        maxLines = 5,
        contentPadding = PaddingValues(12.dp),
        placeholder = {
            Text(
                stringResource(R.string.chat_page_input_placeholder),
                Modifier.fillMaxWidth(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = DTTextFieldTextStyle.copy(color = White35),
            )
        },
        endContent = {
            DTHorizontalSpacer(2.dp)
            Icon(
                if (imeVisible) painterResource(R.drawable.ic_send_msg_sv) else painterResource(R.drawable.ic_chat_more),
                null,
                Modifier
                    .size(22.dp)
                    .click(onSend),
                tint = Color.Unspecified
            )
        }
    )
}

@Composable
fun ChatMoreAnim(
    showChatMore: Boolean,
    imeHeight: Dp,
    onNewChatClick: () -> Unit,
    onResetClick: () -> Unit,
    onShareClick: () -> Unit,
) {
    AnimatedContent(
        showChatMore,
        Modifier.fillMaxWidth(),
        transitionSpec = {
            fadeIn(animationSpec = tween(300))
                .togetherWith(fadeOut(animationSpec = tween(300)))
        }
    ) { show ->
        if (show) {
            Column(
                Modifier
                    .fillMaxWidth()
                    .heightIn(imeHeight)
                    .navigationBarsPadding()
            ) {
                DTVerticalSpacer(10.dp)
                ChatMore(onNewChatClick, onResetClick, onShareClick)
            }
        } else {
            Spacer(
                Modifier
                    .imePadding()
                    .navigationBarsPadding()
            )
        }
    }
}

@Composable
fun ChatMore(
    onNewChatClick: () -> Unit,
    onResetClick: () -> Unit,
    onShareClick: () -> Unit,
) {
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        ChatMoreItem(R.drawable.ic_chat_more_new_chat, R.string.new_chat, onNewChatClick)
        ChatMoreItem(R.drawable.ic_chat_more_reset, R.string.reset, onResetClick)
        ChatMoreItem(R.drawable.ic_chat_more_share, R.string.share, onShareClick)
    }
}

@Composable
fun ChatMoreItem(@DrawableRes icon: Int, @StringRes title: Int, onClick: () -> Unit) {
    Column(
        Modifier
            .size(109.dp, 80.dp)
            .clip(RoundedCornerShape(12.dp))
            .clickable(onClick = onClick)
            .background(White8),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            painterResource(icon),
            null,
            Modifier.size(22.dp),
            tint = Color.Unspecified
        )
        DTVerticalSpacer(10.dp)
        Text(
            stringResource(title),
            style = MaterialTheme.typography.labelMedium.copy(
                color = Color.White,
            )
        )
    }
}