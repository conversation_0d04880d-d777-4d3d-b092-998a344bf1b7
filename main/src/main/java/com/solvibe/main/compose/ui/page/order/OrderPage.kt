package com.solvibe.main.compose.ui.page.order

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.PullToRefreshDefaults.Indicator
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import com.imyfone.membership.api.bean.OrderBean
import com.solvibe.main.R
import com.solvibe.main.compose.theme.SocialBlue
import com.solvibe.main.compose.theme.White10
import com.solvibe.main.compose.theme.White5
import com.solvibe.main.compose.ui.components.BasicLoadMore
import com.solvibe.main.compose.ui.components.DTPage
import com.solvibe.main.compose.ui.components.SVTopBar
import com.solvibe.main.ext.showToast

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrderPage(
    onBack: () -> Unit,
    viewModel: OrderViewModel = viewModel()
) {
    val orders = viewModel.orderFlow.collectAsLazyPagingItems()
    val isRefresh by remember(orders) {
        derivedStateOf { orders.loadState.refresh is LoadState.Loading }
    }

    val refreshState = rememberPullToRefreshState()
    PullToRefreshBox(
        isRefreshing = isRefresh,
        onRefresh = orders::refresh,
        modifier = Modifier.fillMaxSize(),
        state = refreshState,
        indicator = {
            Indicator(
                modifier = Modifier.align(Alignment.TopCenter),
                isRefreshing = isRefresh,
                state = refreshState,
                color = SocialBlue
            )
        },
    ) {
        DTPage(Modifier.background(Color(0xFF13111B))) {
            val state by viewModel.state.collectAsState()
            Column(
                Modifier
                    .statusBarsPadding()
                    .fillMaxSize()
            ) {
                SVTopBar(R.string.my_order, onBack = onBack)
                val context = LocalContext.current
                when {
                    state.isLoadedSuccess -> {
                        if (orders.itemCount == 0) {
                            EmptyView()
                        } else {
                            Orders(orders, modifier = Modifier.fillMaxSize())
                        }
                    }

                    state.isShowErrorPage -> {
                        showToast(context.getString(R.string.not_network))
                        EmptyView()
                    }
                }
            }
        }
    }
}

@Composable
private fun Orders(orders: LazyPagingItems<OrderBean>, modifier: Modifier = Modifier) {
    LazyColumn(
        modifier,
        contentPadding = PaddingValues(vertical = 12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(orders.itemCount) { index ->
            val order = orders[index]
            order?.let {
                ItemOrder(it)
            }
        }
        orders.apply {
            when {
                loadState.refresh is LoadState.Loading -> {
                    // 首次加载或刷新时显示加载指示器
                }

                loadState.append is LoadState.Loading -> {
                    // 底部加载更多时显示加载指示器
                    item { LoadingIndicator(modifier = Modifier.fillMaxWidth()) }
                }

                loadState.append is LoadState.Error -> {
                    val e = loadState.refresh as LoadState.Error
                    // 首次加载或刷新时显示错误信息
                    item { ErrorMessage(message = e.error.localizedMessage ?: "Unknown error") }
                }
            }
        }
    }

}

@Composable
fun LoadingIndicator(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        BasicLoadMore(Modifier.size(55.dp))
    }
}

@Composable
fun ErrorMessage(message: String, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(text = "Error: $message", color = Color.Red)
    }
}

@Composable
private fun ColumnScope.EmptyView() {
    Box(
        Modifier
            .fillMaxWidth()
            .weight(1f), contentAlignment = Alignment.Center
    ) {
        Image(painterResource(R.drawable.img_empty_orders), null, Modifier.size(208.dp))
    }
}

@Composable
private fun ItemOrder(order: OrderBean) {
    Box {
        ConstraintLayout(
            Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .background(White10)
                .padding(bottom = 10.dp)
        ) {
            val (titleRef, lineRef, orderNumRef, dateRef, amountRef) = createRefs()
            Text(
                order.skuName,
                Modifier.constrainAs(titleRef) {
                    start.linkTo(parent.start, 16.dp)
                    top.linkTo(parent.top, 16.dp)
                    end.linkTo(parent.end, 80.dp)
                    width = Dimension.fillToConstraints
                },
                style = MaterialTheme.typography.headlineLarge.copy(
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontSize = 16.sp
                )
            )

            HorizontalDivider(
                Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .constrainAs(lineRef) {
                        top.linkTo(titleRef.bottom, 10.dp)
                    },
                1.dp,
                White5
            )

            Text(
                order.orderNo,
                Modifier.constrainAs(orderNumRef) {
                    start.linkTo(titleRef.start)
                    top.linkTo(lineRef.bottom, 12.dp)
                },
                style = MaterialTheme.typography.bodySmall.copy(
                    color = Color(0xFF999999),
                    fontSize = 14.sp,
                    lineHeight = 24.sp
                )
            )
            Text(
                stringResource(R.string.order_date) + order.createdAt,
                Modifier.constrainAs(dateRef) {
                    start.linkTo(titleRef.start)
                    top.linkTo(orderNumRef.bottom)
                },
                style = MaterialTheme.typography.bodySmall.copy(
                    color = Color(0xFF999999),
                    fontSize = 14.sp,
                    lineHeight = 24.sp
                )
            )
            Text(
                "${order.amount} ${order.currencyCode}",
                Modifier.constrainAs(amountRef) {
                    end.linkTo(parent.end, 16.dp)
                    centerVerticallyTo(dateRef)
                },
                style = MaterialTheme.typography.labelMedium.copy(
                    color = Color(0xFF999999),
                    lineHeight = 20.sp
                )
            )
        }

        Image(
            if (order.orderStatus == 1) painterResource(R.drawable.bg_order_status_success)
            else painterResource(R.drawable.bg_order_status_error),
            null,
            Modifier
                .padding(top = 15.dp, end = 13.dp)
                .size(76.dp, 22.dp)
                .align(Alignment.TopEnd)
        )
        Text(
            order.orderStatusText,
            Modifier
                .padding(top = 18.dp, end = 25.dp)
                .align(Alignment.TopEnd),
            style = MaterialTheme.typography.headlineMedium.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                fontSize = 10.sp,
                lineHeight = 12.sp
            )
        )
    }
}