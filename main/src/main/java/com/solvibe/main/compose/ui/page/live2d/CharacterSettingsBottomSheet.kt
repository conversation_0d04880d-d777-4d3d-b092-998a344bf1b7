package com.solvibe.main.compose.ui.page.live2d

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.Membership
import com.solvibe.main.App
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White8
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.components.SVSwitch
import com.solvibe.main.compose.ui.dialog.SVBottomSheet
import com.solvibe.main.state.getAutoPlayEnable
import com.solvibe.main.state.getReasoningEnable
import com.solvibe.main.state.getRecordEnable

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CharacterSettingsBottomSheet(
    onDismiss: () -> Unit,
    onReport: () -> Unit,
    onChatRecord: () -> Unit,
    onGotoPay: () -> Unit,
    viewModel: Live2DViewModel = viewModel()
) {
    val currentLLMModel by App.viewModel.settingsState.currentLLMModelFlow.collectAsStateWithLifecycle()
    val currentTone by App.viewModel.settingsState.currentToneFlow.collectAsStateWithLifecycle()
    val characterSettings by App.viewModel.settingsState.characterSettingsFlow.collectAsStateWithLifecycle()

    SVBottomSheet(
        onDismiss = onDismiss,
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        containerColor = Color(0xFF19171F)
    ) {
        DTVerticalSpacer(8.dp)
        BSTitle(R.string.character_settings)
        DTVerticalSpacer(10.dp)
        SettingGroup(
            R.string.generally,
            listOf(
                SettingsItemData.Selectable(
                    R.string.model_settings, true, currentLLMModel?.name.orEmpty(),
                    onClick = {
                        if (!Membership.isVip()) {
                            onDismiss()
                            return@Selectable onGotoPay()
                        }
                        viewModel.showSelectLLMModel(true)
                    }
                )
            )
        )
        DTVerticalSpacer(20.dp)
        SettingGroup(
            R.string.role,
            listOf(
                SettingsItemData.Selectable(
                    R.string.tone,
                    false,
                    if (currentTone == null) "" else stringResource(currentTone!!.name),
                    onClick = {
                        viewModel.showSelectTone(true)
                    }
                ),
                SettingsItemData.Switch(
                    R.string.character_settings_auto_play_tts,
                    isVipSetting = false,
                    characterSettings.getAutoPlayEnable(),
                    onClick = {
                        if (characterSettings.getAutoPlayEnable()) {
                            viewModel.stopAudio()
                        }
                        App.viewModel.settingsState.setAutoPlay(!characterSettings.getAutoPlayEnable())
                    }
                ),
                SettingsItemData.Switch(
                    R.string.character_memory,
                    isVipSetting = true,
                    characterSettings.getRecordEnable(),
                    onClick = {
                        if (!Membership.isVip()) {
                            onDismiss()
                            return@Switch onGotoPay()
                        }
                        App.viewModel.settingsState.setRecord(!characterSettings.getRecordEnable())
                    }
                ),
                SettingsItemData.Switch(
                    R.string.deepthinking,
                    isVipSetting = true,
                    characterSettings.getReasoningEnable(),
                    onClick = {
                        if (!Membership.isVip()) {
                            onDismiss()
                            return@Switch onGotoPay()
                        }
                        App.viewModel.settingsState.setReasoning(!characterSettings.getReasoningEnable())
                    }
                )
            )
        )
        DTVerticalSpacer(20.dp)
        SettingGroup(
            R.string.chat,
            listOf(
                SettingsItemData.Selectable(
                    R.string.history, false, "",
                    onClick = onChatRecord
                ),
                SettingsItemData.Selectable(
                    R.string.report, false, "",
                    onClick = onReport
                ),
            )
        )
        DTVerticalSpacer(20.dp)
    }
}

@Composable
fun SettingGroup(@StringRes title: Int, items: List<SettingsItemData>) {
    Column(Modifier.padding(horizontal = 16.dp)) {
        Text(
            stringResource(title),
            style = MaterialTheme.typography.headlineSmall.copy(
                color = Color(0xFFB6B6B6),
                lineHeight = 18.sp
            )
        )
        DTVerticalSpacer(8.dp)
        Column(
            Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .background(White8)
        ) {
            items.forEachIndexed { index, data ->
                SettingItem(data)
                if (index < items.lastIndex) {
                    HorizontalDivider(
                        Modifier
                            .padding(horizontal = 16.dp)
                            .fillMaxWidth(),
                        0.5.dp,
                        color = Color(0xFF555555)
                    )
                }
            }
        }
    }
}

@Composable
fun SettingItem(data: SettingsItemData) {
    Row(
        Modifier
            .fillMaxWidth()
            .height(42.dp)
            .clickable(onClick = data.onClick)
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            stringResource(data.title),
            maxLines = 1,
            style = MaterialTheme.typography.bodySmall.copy(
                color = Color.White
            )
        )
        DTHorizontalSpacer(10.dp)
        if (data.isVipSetting) {
            VIPLabel()
        }
        Spacer(Modifier.weight(1f))
        when (data) {
            is SettingsItemData.Selectable -> {
                if (data.selectedText.isNotEmpty()) {
                    Text(
                        data.selectedText,
                        maxLines = 1,
                        style = MaterialTheme.typography.bodySmall.copy(
                            color = Color.White
                        )
                    )
                }
                DTHorizontalSpacer(4.dp)
                Icon(
                    painterResource(R.drawable.ic_ringht_arrow_sv),
                    null,
                    Modifier.size(16.dp, 12.dp),
                    tint = Color.Unspecified
                )
            }

            is SettingsItemData.Switch -> {
                SVSwitch(data.isSelected) { data.onClick() }
            }
        }
    }
}

@Composable
fun VIPLabel() {
    Text(
        stringResource(R.string.vip),
        Modifier
            .size(31.dp, 16.dp)
            .background(Color(0xFFF8345B), RoundedCornerShape(50))
            .wrapContentHeight(align = Alignment.CenterVertically),
        style = MaterialTheme.typography.labelMedium.copy(
            color = Color.White,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center
        )
    )
}

sealed interface SettingsItemData {
    val title: Int
    val isVipSetting: Boolean
    val onClick: () -> Unit

    data class Selectable(
        override val title: Int,
        override val isVipSetting: Boolean,
        val selectedText: String,
        override val onClick: () -> Unit
    ) : SettingsItemData

    data class Switch(
        override val title: Int,
        override val isVipSetting: Boolean,
        val isSelected: Boolean,
        override val onClick: () -> Unit
    ) : SettingsItemData
}

@Composable
fun BSTitle(@StringRes title: Int) {
    Text(
        stringResource(title),
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.headlineLarge.copy(
            color = Color.White,
            fontSize = 16.sp,
            lineHeight = 18.sp
        )
    )
}