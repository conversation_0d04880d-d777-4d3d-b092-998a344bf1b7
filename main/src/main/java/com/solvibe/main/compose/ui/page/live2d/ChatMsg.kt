package com.solvibe.main.compose.ui.page.live2d

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.fromHtml
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.main.App
import com.solvibe.main.R
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.components.DashedLine
import com.solvibe.main.compose.ui.components.ThreeDotsLoading
import com.solvibe.main.compose.ui.modifier.click
import com.solvibe.main.state.UIMsg
import com.solvibe.utils.ext.dpi

@Composable
fun ChatMsg(uiMsg: UIMsg, isLatestMsg: Boolean) {
    if (uiMsg.isSelf) {
        MyChatMsg(uiMsg)
    } else {
        OtherChatMsg(uiMsg, isLatestMsg)
    }
}

@Composable
fun MyChatMsg(uiMsg: UIMsg) {
    Box(Modifier.fillMaxWidth(), contentAlignment = Alignment.CenterEnd) {
        Text(
            uiMsg.sourceContent,
            Modifier
                .clip(RoundedCornerShape(16.dp, 2.dp, 16.dp, 16.dp))
                .background(Color(0xFF2B2835))
                .padding(15.dp, 12.dp),
            style = MaterialTheme.typography.bodySmall.copy(
                color = Color.White,
            )
        )
    }
}

@Composable
fun OtherChatMsg(
    uiMsg: UIMsg,
    isLatestMsg: Boolean,
    viewModel: Live2DViewModel = viewModel()
) {
    var showExpandReasoningText by remember { mutableStateOf(false) }
    var expandReasoning by remember { mutableStateOf(true) }
    val appViewModel = App.viewModel
    Column {
        Column(
            Modifier
                .clip(RoundedCornerShape(2.dp, 16.dp, 16.dp, 16.dp))
                .background(Color.White)
                .padding(15.dp, 12.dp),
        ) {
            if (uiMsg.reasoningContent.isNotEmpty()) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        stringResource(R.string.deepthinking),
                        style = MaterialTheme.typography.bodySmall.copy(
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF121212),
                        )
                    )
                    DTHorizontalSpacer(2.dp)
                    Icon(
                        painterResource(R.drawable.ic_deepthinking_arrow),
                        null,
                        Modifier.height(12.dp),
                        tint = Color.Unspecified
                    )
                }
                DTVerticalSpacer(10.dp)
                Text(
                    uiMsg.reasoningContent.trim(),
                    Modifier
                        .animateContentSize()
                        .heightIn(max = if (expandReasoning) Dp.Unspecified else 72.dp)
                        .onSizeChanged {
                            showExpandReasoningText = it.height >= 72.dpi
                        },
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = Color(0xFF999999),
                    )
                )
                DTVerticalSpacer(8.dp)
                AnimatedVisibility(showExpandReasoningText, Modifier.fillMaxWidth()) {
                    Box(Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                        Text(
                            if (expandReasoning) stringResource(R.string.collapse) else stringResource(
                                R.string.expand_all
                            ),
                            Modifier
                                .click { expandReasoning = !expandReasoning },
                            style = MaterialTheme.typography.bodySmall.copy(
                                color = Color(0xFF999999),
                            )
                        )
                    }
                }
            }
            AnimatedVisibility(
                uiMsg.reasoningContent.isNotEmpty() && uiMsg.htmlContent.isNotEmpty(),
                Modifier.fillMaxWidth()
            ) {
                Column {
                    DTVerticalSpacer(17.dp)
                    DashedLine(Color(0xFF555555))
                    DTVerticalSpacer(17.dp)
                }
            }
            if (uiMsg.htmlContent.isNotEmpty()) {
                Text(
                    AnnotatedString.fromHtml(uiMsg.htmlContent),
                    Modifier.animateContentSize(),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = Color(0xFF333333),
                    )
                )
            }
        }
        if (isLatestMsg && !viewModel.showWaitingMessage) {
            DTVerticalSpacer(8.dp)
            ContinueAndRetryBtns(
                onContinueClick = {
                    viewModel.stopAudioAndShowWaitingMessage()
                    appViewModel.continueReply()
                },
                onRetryClick = {
                    viewModel.stopAudioAndShowWaitingMessage()
                    appViewModel.retry()
                },
            )
            if (uiMsg.talkSuggestion.isNotEmpty()) {
                DTVerticalSpacer(8.dp)
                uiMsg.talkSuggestion.forEachIndexed { index, suggestion ->
                    Text(
                        suggestion,
                        Modifier
                            .clickable {
                                viewModel.sendMessage(suggestion)
                            }
                            .clip(RoundedCornerShape(8.dp))
                            .background(Color.White)
                            .padding(8.dp, 7.dp),
                        style = MaterialTheme.typography.labelMedium.copy(
                            color = Color(0xFF121212),
                            lineHeight = 18.sp
                        )
                    )
                    if (index < uiMsg.talkSuggestion.lastIndex) {
                        DTVerticalSpacer(8.dp)
                    }
                }
            }
        }
    }
}

/**
 * 继续和重试图标按钮
 */
@Composable
fun ContinueAndRetryBtns(
    onContinueClick: () -> Unit,
    onRetryClick: () -> Unit,
) {
    Row(
        Modifier
            .width(60.dp)
            .background(Color(0xFF2B2835), RoundedCornerShape(50)),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Icon(
            painterResource(R.drawable.ic_continue_reply),
            null,
            Modifier
                .size(28.dp)
                .clip(CircleShape)
                .clickable(onClick = onContinueClick)
                .padding(6.dp),
            tint = Color.Unspecified
        )
        Icon(
            painterResource(R.drawable.ic_retry_msg),
            null,
            Modifier
                .size(28.dp)
                .clip(CircleShape)
                .clickable(onClick = onRetryClick)
                .padding(6.dp),
            tint = Color.Unspecified
        )
    }
}

@Composable
fun WaitingReply() {
    ThreeDotsLoading(
        Modifier
            .padding(bottom = 16.dp)
            .size(73.dp, 42.dp)
            .clip(RoundedCornerShape(2.dp, 16.dp, 16.dp, 16.dp))
            .background(Color.White),
    )
}