package com.solvibe.main.compose.ui.page.main.language

import java.util.Locale

/**
 * @param id: 语言id，和标准语言的标识符一致
 * @param backendId: 后端语言id
 * @param title: 语言名称
 * @param locale: 语言区域
 */
enum class AppSupportedLanguage(
    val id: String,
    val backendId: String,
    val title: String,
    val locale: Locale,
) {
    ENGLISH("en", "en", "English", Locale.ENGLISH),
    CHINESE_CN("zh_CN", "zh-CN", "简体中文", Locale.SIMPLIFIED_CHINESE),
    CHINESE_TW("zh_TW", "zh-TW", "繁体中文", Locale.TRADITIONAL_CHINESE),
    KOREAN("ko", "ko", "한국어", Locale.KOREAN),
    JAPANESE("ja", "ja", "日本語", Locale.JAPAN);
}