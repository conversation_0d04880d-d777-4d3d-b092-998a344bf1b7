package com.solvibe.main.compose.ui.page.about

import android.app.Application
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.solvibe.main.R
import com.solvibe.main.bean.VersionBean
import com.solvibe.main.compose.utils.AppUtils
import com.solvibe.main.compose.utils.ILoadingState
import com.solvibe.main.compose.utils.loadingState
import com.solvibe.main.ext.showToast
import com.solvibe.main.repo.AppRepo
import kotlinx.coroutines.launch

class AboutViewModel(application: Application) : AndroidViewModel(application),
    ILoadingState by loadingState() {
    private val appRepo = AppRepo()
    var versionBean by mutableStateOf<VersionBean?>(null)
        private set

    fun showUpdateDialog(versionBean: VersionBean?) {
        this.versionBean = versionBean
    }

    fun checkUpdate() {
        viewModelScope.launch {
            showLoading(true)
            val versionBean = appRepo.getVersionInfo()
            val app = getApplication<Application>()
            if (versionBean == null || versionBean.ver.isEmpty()) {
                showLoading(false)
                showToast(app.getString(R.string.new_version))
                return@launch
            }
            if (versionBean.ver > AppUtils.getVersion(app)) {
                // 服务器版本比当前版本大，显示更新弹窗
                showUpdateDialog(versionBean)
            }
            showLoading(false)
        }
    }
}
