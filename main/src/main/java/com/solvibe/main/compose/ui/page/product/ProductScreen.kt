package com.solvibe.main.compose.ui.page.product

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.gson.Gson
import com.imyfone.membership.api.bean.SKUBean
import com.solvibe.Membership
import com.solvibe.main.R
import com.solvibe.main.activity.WebActivity
import com.solvibe.main.bean.ProductBean
import com.solvibe.main.compose.theme.White
import com.solvibe.main.compose.theme.White60
import com.solvibe.main.compose.theme.White70
import com.solvibe.main.compose.theme.White8
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTLoading
import com.solvibe.main.compose.ui.components.DTTextFieldTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.components.SelectableDrawable
import com.solvibe.main.compose.ui.modifier.click
import com.solvibe.main.compose.utils.ChildDesign
import com.solvibe.main.compose.utils.toAnnotatedStringParameters
import com.solvibe.main.config.Constant
import com.solvibe.main.config.ReportEventUtils
import com.solvibe.main.config.UmConstant
import com.solvibe.main.ext.showToast

private val TAG_PRIVACY = "TAG_PRIVACY"
private val TAG_TERMS = "TAG_TERMS"
private val TAG_EULA = "TAG_EULA"

/**
 *creater:linjinhao on 2025/5/19 15:02
 */
@Composable
fun ProductRoute(
    onBack: () -> Unit,
    onGooglePurchaseSuccess: (bean: String) -> Unit,
    onLogin: () -> Unit
) {
    val context = LocalContext.current
    val viewModel: ProductViewModel = viewModel()
    val state by viewModel.state.collectAsState()
    ProductScreen(
        onBack = onBack,
        viewModel = viewModel,
        onPrivacy = {
            WebActivity.startBrowser(
                context,
                Constant.privacy + Constant.webParams
            )
        },
        onTerm = { WebActivity.startBrowser(context, Constant.terms + Constant.webParams) },
        onEula = { WebActivity.startBrowser(context, Constant.eula + Constant.webParams) },
        onCancelSubscript = {
            Membership.toGoogleSubscription(context)
        }
    )
    DTLoading(loading = state.skuState is SkuUIState.Loading || state.purchaseLoading) {}

    LaunchedEffect(viewModel.state) {
        when (state.skuState) {
            SkuUIState.Fail -> {
                showToast(context.getString(R.string.not_network))
            }

            SkuUIState.Init, SkuUIState.Loading -> {

            }

            is SkuUIState.Success -> {
            }
        }
    }
    LaunchedEffect(viewModel.event) {
        viewModel.event.collect {
            when (it) {
                is ProductEvent.GooglePurchaseSuccess -> {
                    val gson = Gson()
                    val jsonString = gson.toJson(it.bean)
                    onGooglePurchaseSuccess(jsonString)
                }

                is ProductEvent.LoginTip -> {
                    showToast(context.getString(R.string.payment_successful))
                }

                ProductEvent.NeedLogin -> onLogin()

                ProductEvent.CancelGooglePurchase -> showToast(context.getString(R.string.pay_cancel))

                is ProductEvent.GooglePurchaseFail -> {
                    showToast(context.getString(R.string.pay_cancel))
                }

                ProductEvent.ICartPurchaseFinish -> {
                    showToast(context.getString(R.string.payment_successful))
                }

                ProductEvent.GoogleConfirmOrderFail -> {
                    showToast(context.getString(R.string.pay_cancel))
                }
            }
        }
    }
}


@Composable
private fun ProductScreen(
    onBack: () -> Unit,
    viewModel: ProductViewModel,
    onPrivacy: () -> Unit,
    onEula: () -> Unit,
    onTerm: () -> Unit,
    onCancelSubscript: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Image(
            painterResource(R.drawable.bg_bug_sv),
            contentDescription = "image",
            modifier = Modifier
                .aspectRatio(375 / 812f)
                .fillMaxSize(),
        )

        ConstraintLayout(
            modifier = Modifier
                .statusBarsPadding()
                .navigationBarsPadding()
                .fillMaxSize()
        ) {
            val (topBar, center, btm) = createRefs()
            Title(modifier = Modifier.constrainAs(topBar) {
                top.linkTo(parent.top)
                start.linkTo(parent.start)
                end.linkTo(parent.end)
            }, onBack = onBack)
            LazyColumn(
                modifier = Modifier
                    .constrainAs(center) {
                        top.linkTo(topBar.bottom)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        bottom.linkTo(btm.top)
                        height = Dimension.fillToConstraints
                    }
                    .fillMaxWidth(),
                contentPadding = PaddingValues(horizontal = 16.dp),
            ) {
                item {
                    DTVerticalSpacer(17.dp)
                    PlanDescText()
                    DTVerticalSpacer(55.dp)
                    TableTitle()
                }
                itemsIndexed(viewModel.benefits, key = { index, item -> index }) { index, item ->
                    TableItem(data = item, index != viewModel.benefits.size - 1)
                }
                item {
                    DTVerticalSpacer(12.dp)
                }
                itemsIndexed(viewModel.skus, key = { _, it -> it.skuBean.skuID }) { index, item ->
                    ItemSku(
                        item,
                        selected = viewModel.selectIndex == index,
                        onSelect = { viewModel.selectIndex = index })
                    Spacer(modifier = Modifier.height(17.dp))
                }
            }
            LayBottom(
                modifier = Modifier.constrainAs(btm) {
                    top.linkTo(center.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom)
                },
                viewModel = viewModel,
                onBuyProduct = {
                    viewModel.purchase(skuBean = it)
                },
                onPrivacy = onPrivacy,
                onEula = onEula,
                onTerm = onTerm,
                onCancelSubscript = onCancelSubscript
            )
        }
    }
}

/**
 * 表的每一项
 */
@Composable
private fun TableItem(data: BenefitCompareBean, needShowLine: Boolean = true) {
    Column(
        Modifier
            .clip(
                if (!needShowLine) RoundedCornerShape(
                    bottomStart = 12.dp,
                    bottomEnd = 12.dp
                ) else RoundedCornerShape(0.dp)
            )
            .fillMaxWidth()
    ) {
        var height: Dp by remember { mutableStateOf(0.dp) }
        val density = LocalDensity.current
        Column {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .background(White8)
                        .weight(2f)
                        .padding(top = 6.dp, bottom = 6.dp)
                        .padding(start = 15.dp)
                        .onGloballyPositioned {
                            height = with(density) {
                                it.size.height.toDp()
                            }

                        }, verticalArrangement = Arrangement.Center
                ) {
                    DTVerticalSpacer(6.dp)
                    Text(
                        text = stringResource(data.benefit),
                        style = DTTextFieldTextStyle.copy(
                            color = White70,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.W400
                        )
                    )
                }
                Column(
                    modifier = Modifier
                        .background(colorResource(R.color.color_1FEA557F))
                        .padding(top = 6.dp, bottom = 6.dp)
                        .weight(1f)
                        .height(height),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Image(
                        painter = painterResource(data.premium),
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier.size(16.dp),
                        contentDescription = "icon"
                    )
                }
                Column(
                    modifier = Modifier
                        .background(White8)
                        .padding(top = 6.dp, bottom = 6.dp)
                        .weight(1f)
                        .height(height),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Image(
                        painter = painterResource(data.free),
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier.size(16.dp),
                        contentDescription = "icon"
                    )
                }
            }
        }
        if (needShowLine) {
            HorizontalDivider(
                modifier = Modifier.fillMaxWidth(),
                thickness = 0.5.dp,
                color = colorResource(R.color.color_333333)
            )
        }
    }
}

/**
 * 表头
 */
@Composable
private fun TableTitle() {
    Column(
        Modifier
            .background(White8, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Column(
                modifier = Modifier
                    .background(White8)
                    .weight(2f)
            ) { }
            Column(
                modifier = Modifier
                    .background(colorResource(R.color.color_1FEA557F))
                    .padding(bottom = 6.dp, top = 6.dp)
                    .weight(1f), verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val brush = Brush.linearGradient(listOf(Color(0xFFFF716A), Color(0xFFFF716A)))
                Text(
                    text = stringResource(id = R.string.premium),
                    style = DTTextFieldTextStyle.copy(
                        brush = brush,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W600
                    )
                )
            }
            Column(
                modifier = Modifier
                    .background(White8)
                    .padding(bottom = 6.dp, top = 6.dp)
                    .weight(1f), verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(id = R.string.free),
                    style = DTTextFieldTextStyle.copy(
                        color = White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W500
                    )
                )
            }
        }
        HorizontalDivider(
            modifier = Modifier.fillMaxWidth(),
            thickness = 0.5.dp,
            color = colorResource(R.color.color_333333)
        )
    }
}

@Composable
private fun Title(modifier: Modifier = Modifier, onBack: () -> Unit) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(start = 20.dp, top = 16.dp, end = 20.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painterResource(R.drawable.ic_close_sv),
            modifier = Modifier
                .size(24.dp)
                .click {
                    onBack()
                },
            contentDescription = "backIcon"
        )
    }
}

@Composable
private fun LayBottom(
    modifier: Modifier = Modifier,
    viewModel: ProductViewModel = viewModel(),
    onBuyProduct: (skuBean: SKUBean) -> Unit,
    onPrivacy: () -> Unit,
    onTerm: () -> Unit,
    onEula: () -> Unit,
    onCancelSubscript: () -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            stringResource(R.string.auto_renewal_text),
            style = TextStyle(fontSize = 12.sp, color = colorResource(R.color.color_B8B8B8))
        )
        DTVerticalSpacer(3.dp)
        DTButton(
            text = stringResource(R.string.unlock_now_buy),
            contentColor = White,
            bg = SelectableDrawable(R.drawable.bg_btn_buy, R.drawable.bg_btn_buy),
            textStyle = DTTextFieldTextStyle.copy(
                fontSize = 15.sp,
                fontWeight = FontWeight.W700,
                lineHeight = 24.sp
            ),
            modifier = Modifier
                .padding(start = 16.dp, end = 16.dp)
                .fillMaxWidth()
                .height(48.dp),
            shape = RoundedCornerShape(22.dp)
        ) {
            if (viewModel.skus.isEmpty()) return@DTButton
            ReportEventUtils.onEvent(
                UmConstant.Purchase_page,
                mapOf(UmConstant.Purchase_page to if (viewModel.skus[viewModel.selectIndex].isMonthSubscribe()) "Buy_1_Month" else "Buy_1_Year")
            )
            onBuyProduct(viewModel.skus[viewModel.selectIndex].skuBean)
        }

        DTVerticalSpacer(12.dp)
        Text(
            text = stringResource(R.string.how_to_cancel_subscript),
            style = DTTextFieldTextStyle.copy(
                fontSize = 12.sp,
                color = colorResource(R.color.color_B8B8B8)
            ),
            modifier = Modifier.click {
                onCancelSubscript()
            }
        )
        DTVerticalSpacer(6.dp)
        TermsAndPolicy(onTerm, onPrivacy)
    }
}

@Composable
private fun PlanDescText() {
    val title = titleTxtAnnotatedString()
    Text(
        title,
        textAlign = TextAlign.Center,
        style = DTTextFieldTextStyle.copy(
            color = White,
            fontSize = 18.sp,
            fontWeight = FontWeight.W800,
            fontStyle = FontStyle.Italic,
            lineHeight = 20.sp
        ), modifier = Modifier
            .fillMaxWidth()
            .padding(start = 12.dp, end = 12.dp)
    )
}

@Composable
private fun ItemSku(
    productBean: ProductBean,
    selected: Boolean,
    onSelect: () -> Unit
) {
    val viewModel: ProductViewModel = viewModel()

    Box(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .padding(top = 11.5.dp)
                .background(
                    if (selected) {
                        colorResource(R.color.color_33DE5892)
                    } else colorResource(R.color.color_2A272F), shape = RoundedCornerShape(14.dp)
                )
                .border(
                    width = 1.2.dp,
                    color = if (selected) colorResource(R.color.color_E74E89) else colorResource(R.color.color_504C58),
                    shape = RoundedCornerShape(14.dp)
                )
                .click {
                    onSelect()
                }
                .fillMaxWidth()
                .padding(vertical = 15.dp)
                .padding(start = 16.dp, end = 15.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Column {
                Image(
                    painter = painterResource(if (selected) R.drawable.ic_sku_checkbox_sel else R.drawable.ic_sku_checkbox_def),
                    modifier = Modifier.size(14.dp),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds
                )
            }

            DTHorizontalSpacer(9.dp)

            Column(modifier = Modifier.weight(1f), horizontalAlignment = Alignment.Start) {
                Text(
                    text = productBean.licenseName,
                    style = DTTextFieldTextStyle.copy(
                        color = White,
                        fontSize = 14.sp,
                        lineHeight = 24.sp,
                        fontWeight = FontWeight.W500
                    )
                )
                DTVerticalSpacer(16.dp)
                Row {
                    Text(
                        text = "${productBean.getAveragePrice()}${
                            viewModel.getSkuUnitStr(
                                productBean
                            )
                        }",
                        style = DTTextFieldTextStyle.copy(
                            color = White60,
                            fontSize = 12.sp,
                            lineHeight = 24.sp,
                            fontWeight = FontWeight.W400
                        )
                    )
                }
            }
            Column() {
                Text(
                    text = "${productBean.currencyCode}${productBean.actualPriceAmount}",
                    style = DTTextFieldTextStyle.copy(
                        color = colorResource(R.color.color_FA5DFF),
                        fontSize = 18.sp,
                        lineHeight = 24.sp,
                        fontWeight = FontWeight.W600
                    )
                )
                Text(
                    text = "${productBean.currencyCode}${productBean.virtualPriceAmount}",
                    style = DTTextFieldTextStyle.copy(
                        color = White60,
                        fontSize = 12.sp,
                        lineHeight = 24.sp,
                        fontWeight = FontWeight.W400,
                        textDecoration = TextDecoration.LineThrough,
                    ),
                )
            }

        }
        if (productBean.localPercent > 0 && selected) {
            Box(
                modifier = Modifier
                    .align(alignment = Alignment.TopEnd)
                    .height(21.dp),
                contentAlignment = Alignment.Center
            ) {
                Image(painterResource(R.drawable.bg_save_text), contentDescription = "")
                Text(
                    text = "Save ${productBean.localPercent.toInt()}%",
                    style = DTTextFieldTextStyle.copy(
                        fontSize = 13.sp,
                        fontWeight = FontWeight.W700,
                        color = White
                    )
                )
            }
        }
    }
}

@Composable
private fun titleTxtAnnotatedString(): AnnotatedString {
    val title = stringResource(R.string.unlock_the_full_experience)
    val unlock = stringResource(R.string.buy_page_unlock_title)
    val color = colorResource(R.color.color_FF5476)
    return remember(color, title, unlock) {
        title.toAnnotatedStringParameters(
            ChildDesign(
                childString = unlock,
                annotatedTag = "unlock",
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.W800
                )
            )
        )
    }
}

@Composable
private fun TermsAndPolicy(onTerm: () -> Unit, onPrivacy: () -> Unit) {
    Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        Text(
            text = stringResource(R.string.terms),
            style = DTTextFieldTextStyle.copy(
                color = Color(0x99D7D7D7),
                fontSize = 12.sp,
                lineHeight = 18.sp, textDecoration = TextDecoration.Underline
            ), modifier = Modifier.clickable(onClick = onTerm)
        )
        DTHorizontalSpacer(37.dp)
        Text(
            text = stringResource(R.string.policy),
            style = DTTextFieldTextStyle.copy(
                color = Color(0x99D7D7D7),
                fontSize = 12.sp,
                lineHeight = 18.sp, textDecoration = TextDecoration.Underline
            ), modifier = Modifier.clickable(onClick = onPrivacy)
        )
    }
}
