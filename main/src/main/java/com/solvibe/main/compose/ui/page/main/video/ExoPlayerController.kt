package com.solvibe.main.compose.ui.page.main.video

import android.content.Context
import android.os.Bundle
import android.os.Parcelable
import android.view.Surface
import androidx.annotation.FloatRange
import androidx.annotation.OptIn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.core.net.toUri
import androidx.core.os.bundleOf
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import com.solvibe.utils.ext.loge
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.parcelize.Parcelize
import kotlin.math.roundToLong

@Composable
fun rememberExoPlayerController(url: String): ExoPlayerController {
    val context = LocalContext.current

    val exoPlayerController = rememberSaveable(saver = ExoPlayerController.saver(context, url)) {
        ExoPlayerController.new(context, url)
    }

    DisposableEffect(Unit) {
        onDispose { exoPlayerController.onDispose() }
    }

    return exoPlayerController
}

@Parcelize
sealed interface ExoPlayState : Parcelable {
    data object Idle : ExoPlayState
    data object Loading : ExoPlayState
    data object Buffering : ExoPlayState
    data object Playing : ExoPlayState
    data object Paused : ExoPlayState
    data object Finished : ExoPlayState
    data class Error(val error: PlaybackException) : ExoPlayState
}

fun ExoPlayState.isIdle() = this is ExoPlayState.Idle
fun ExoPlayState.isLoading() = this is ExoPlayState.Loading
fun ExoPlayState.isBuffering() = this is ExoPlayState.Buffering
fun ExoPlayState.isPlaying() = this is ExoPlayState.Playing
fun ExoPlayState.isPaused() = this is ExoPlayState.Paused
fun ExoPlayState.isFinished() = this is ExoPlayState.Finished
fun ExoPlayState.isError() = this is ExoPlayState.Error

fun ExoPlayState.isNeedPrepare(): Boolean {
    return isIdle() || isFinished() || isError()
}

@Stable
class ExoPlayerController(
    url: String,
    private val player: ExoPlayer,
) {
    var url by mutableStateOf(url)
        private set
    var position by mutableLongStateOf(0)
        private set
    var progress by mutableFloatStateOf(0f)
        private set
    var playState by mutableStateOf<ExoPlayState>(ExoPlayState.Loading)
        private set

    private val scope = CoroutineScope(Dispatchers.Default)

    companion object {
        @OptIn(UnstableApi::class)
        fun newExoPlayer(context: Context): ExoPlayer {
            return ExoPlayer.Builder(context)
                .setMediaSourceFactory(VideoCacheManager.newMediaSourceFactory(context))
                .build()
        }

        fun new(context: Context, url: String): ExoPlayerController {
            val player = newExoPlayer(context)
            return ExoPlayerController(url, player)
        }

        fun saver(context: Context, url: String) = Saver<ExoPlayerController, Bundle>(
            save = { it.saveState(url) },
            restore = { restoreState(context, url, it) }
        )

        private fun restoreState(
            context: Context,
            url: String,
            bundle: Bundle
        ): ExoPlayerController {
            val exoPlayerController = new(context, url)
            val state = bundle.getParcelable<ExoPlayerControllerState>(SAVER_KEY_EXO_PLAYER_STATE)
                ?: return exoPlayerController
            exoPlayerController.restore(state)
            return exoPlayerController
        }
    }

    private val listener = object : Player.Listener {
        override fun onIsLoadingChanged(isLoading: Boolean) {
            super.onIsLoadingChanged(isLoading)
            if (isLoading) {
                playState = ExoPlayState.Loading
            }
        }

        override fun onPlaybackStateChanged(playbackState: Int) {
            super.onPlaybackStateChanged(playbackState)
            when (playbackState) {
                Player.STATE_IDLE -> {
                    playState = ExoPlayState.Idle
                    stopProgressUpdates()
                }

                Player.STATE_BUFFERING -> playState = ExoPlayState.Buffering
                Player.STATE_READY -> {
                    if (player.isPlaying) {
                        startProgressUpdates()
                    } else {
                        playState = ExoPlayState.Paused
                        stopProgressUpdates()
                    }
                }

                Player.STATE_ENDED -> {
                    playState = ExoPlayState.Finished
                    progress = 1f
                    stopProgressUpdates()
                }
            }
        }

        override fun onIsPlayingChanged(isPlaying: Boolean) {
            super.onIsPlayingChanged(isPlaying)
            if (player.playbackState == Player.STATE_READY) {
                if (isPlaying) {
                    startProgressUpdates()
                } else {
                    playState = ExoPlayState.Paused
                    stopProgressUpdates()
                }
            }
        }

        override fun onPlayerError(error: PlaybackException) {
            super.onPlayerError(error)
            loge("播放失败，${error.stackTraceToString()}")
            playState = ExoPlayState.Error(error)
            stopProgressUpdates()
        }
    }

    private var progressUpdateJob: Job? = null

    private fun startProgressUpdates() {
        stopProgressUpdates()
        playState = ExoPlayState.Playing

        progressUpdateJob = scope.launch {
            while (isActive) {
                updatePlayingProgress()
                delay(500) // 每500毫秒更新一次进度
            }
        }
    }

    private fun stopProgressUpdates() {
        progressUpdateJob?.cancel()
        progressUpdateJob = null
    }

    private suspend fun updatePlayingProgress() = withContext(Dispatchers.Main) {
        if (player.isPlaying && player.duration > 0) {
            val progressPercentage = player.currentPosition.toFloat() / player.duration.toFloat()
            progress = progressPercentage.coerceIn(0f, 1f)
        }
    }

    init {
        prepare(url)
        player.addListener(listener)
    }

    private fun saveState(url: String): Bundle {
        return bundleOf(
            SAVER_KEY_EXO_PLAYER_STATE to ExoPlayerControllerState(
                url = url,
                pos = player.currentPosition,
                playState = playState
            )
        )
    }

    private fun restore(state: ExoPlayerControllerState) {
        url = state.url
        position = state.pos
        playState = state.playState

        if (url.isNotEmpty()) {
            player.seekTo(position)
            if (playState.isPlaying()) {
                player.play()
            }
        }
    }

    fun setVideoSurface(surface: Surface?) {
        player.setVideoSurface(surface)
    }

    fun prepare(url: String) {
        this.url = url
        player.prepareUrl(url)
    }

    fun play(url: String? = null) {
        if (!url.isNullOrEmpty()) {
            prepare(url)
        }
        if (playState.isNeedPrepare() && this.url.isNotEmpty()) {
            prepare(this.url)
        }
        player.play()
    }

    fun seekToPercent(@FloatRange(from = 0.0, to = 1.0) percent: Float) {
        progress = percent
        player.seekTo((player.duration * percent).roundToLong())
    }

    fun pause() {
        player.pause()
    }

    fun onDispose() {
        stopProgressUpdates()
        scope.cancel()
        player.pause()
        player.stop()
        player.removeListener(listener)
        player.clearMediaItems()
        player.clearVideoSurface()
        player.release()
    }
}

@Parcelize
data class ExoPlayerControllerState(
    val url: String,
    val pos: Long,
    val playState: ExoPlayState
) : Parcelable

private const val SAVER_KEY_EXO_PLAYER_STATE = "SAVER_KEY_EXO_PLAYER_STATE"

fun ExoPlayer.prepareUrl(url: String) {
    setMediaItem(
        MediaItem.Builder()
            .setMediaId(url)
            .setUri(url.toUri())
            .build()
    )
    prepare()
}

fun ExoPlayer.playUrl(url: String) {
    prepareUrl(url)
    play()
}