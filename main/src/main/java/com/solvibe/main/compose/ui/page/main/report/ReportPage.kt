package com.solvibe.main.compose.ui.page.main.report

import androidx.activity.compose.BackHandler
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White10
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTButtonTextStyle
import com.solvibe.main.compose.ui.components.DTPage
import com.solvibe.main.compose.ui.components.DTTextFieldNoBorder
import com.solvibe.main.compose.ui.components.DTTextFieldTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.components.SVDialog
import com.solvibe.main.compose.ui.components.SVTopBar
import com.solvibe.main.ext.showToast

@Composable
fun ReportPage(
    viewModel: ReportViewModel = viewModel(),
    onBack: () -> Unit
) {
    val context = LocalContext.current

    BackHandler {
        viewModel.showBackDialog(true)
    }

    DTPage(
        Modifier.background(Color(0xFF13111B)),
        loading = viewModel.loading,
        onDismissLoading = {
            viewModel.showLoading(false)
        }
    ) {
        Column(
            Modifier
                .statusBarsPadding()
                .fillMaxSize()
        ) {
            SVTopBar(R.string.report) {
                viewModel.showBackDialog(true)
            }
            DTVerticalSpacer(8.dp)
            // report role
            Title(R.string.report_role)
            DTVerticalSpacer(8.dp)
            RoleName(viewModel.roleName, viewModel::onRoleNameChanged)
            DTVerticalSpacer(22.dp)
            // report content
            Title(R.string.report_content)
            DTVerticalSpacer(8.dp)
            ReportContent(viewModel.reportContent, viewModel::onReportContentChanged)
            DTVerticalSpacer(22.dp)
            // your email
            Title(R.string.your_email)
            DTVerticalSpacer(8.dp)
            Email(viewModel.email, viewModel::onEmailChanged)
            DTVerticalSpacer(40.dp)
            SubmitBtn(enable = viewModel.canSubmit()) {
                viewModel.submit { code ->
                    if (code == null) {
                        return@submit showToast(context.getString(R.string.send_failed))
                    }
                    if (code == 0) {
                        return@submit showToast(context.getString(R.string.login_email_valid))
                    }
                    showToast(context.getString(R.string.report_successful))
                    onBack()
                }
            }
        }
    }

    if (viewModel.showBackDialog) {
        SVDialog(
            title = R.string.discard_changes_and_go_back,
            cancelText = R.string.cancel,
            confirmText = R.string.Delete,
            onConfirm = {
                viewModel.showBackDialog(false)
                onBack()
            },
            onCancel = {
                viewModel.showBackDialog(false)
            }
        )
    }
}

@Composable
fun Title(@StringRes text: Int) {
    val content = buildAnnotatedString {
        withStyle(style = SpanStyle(color = Color(0xFFFA3F3F))) {
            append("* ")
        }
        append(stringResource(text))
    }
    Text(
        content,
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.headlineMedium.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            lineHeight = 32.sp
        )
    )
}

@Composable
fun RoleName(roleName: String, onRoleNameChanged: (String) -> Unit) {
    DTTextFieldNoBorder(
        value = roleName,
        onValueChange = onRoleNameChanged,
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(52.dp)
            .background(White10, RoundedCornerShape(12.dp)),
        placeholder = stringResource(R.string.email),
        textStyle = DTTextFieldTextStyle.copy(
            fontSize = 15.sp,
            lineHeight = 22.sp
        ),
    )
}

@Composable
fun ReportContent(content: String, onReportContentChanged: (String) -> Unit) {
    DTTextFieldNoBorder(
        value = content,
        onValueChange = onReportContentChanged,
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(180.dp)
            .background(White10, RoundedCornerShape(12.dp)),
        placeholder = stringResource(R.string.anything_you_want_to_say),
        textStyle = DTTextFieldTextStyle.copy(
            fontSize = 15.sp,
            lineHeight = 22.sp
        ),
        singleLine = false,
        verticalAlignment = Alignment.Top,
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 15.dp)
    )
}

@Composable
fun Email(email: String, onEmailChanged: (String) -> Unit) {
    DTTextFieldNoBorder(
        value = email,
        onValueChange = onEmailChanged,
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(52.dp)
            .background(White10, RoundedCornerShape(12.dp)),
        placeholder = stringResource(R.string.email),
        textStyle = DTTextFieldTextStyle.copy(
            fontSize = 15.sp,
            lineHeight = 22.sp
        ),
        keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Email)
    )
}

@Composable
fun SubmitBtn(enable: Boolean, onClick: () -> Unit) {
    val brush = remember(enable) {
        Brush.horizontalGradient(
            if (enable) listOf(Color(0xFFBE4EFF), Color(0xFFF343A1), Color(0xFFFF5476))
            else listOf(Color(0xFF4B4B51), Color(0xFF4B4B51))
        )
    }
    DTButton(
        text = stringResource(R.string.submit),
        brush = brush,
        enable = enable,
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(44.dp),
        textStyle = DTButtonTextStyle.copy(
            color = if (enable) MaterialTheme.colorScheme.onPrimary else Color(0xFF8A8C91)
        ),
        onClick = onClick
    )
}