package com.solvibe.main.compose.ui.page.product

import androidx.annotation.DrawableRes
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.imyfone.membership.api.bean.ConfirmResultBean
import com.imyfone.membership.api.bean.SKUBean
import com.imyfone.membership.api.bean.UserBean
import com.imyfone.membership.exception.NeedLoginException
import com.imyfone.membership.exception.UserCancelException
import com.imyfone.membership.repository.ConfirmGooglePurchaseResult
import com.solvibe.Membership
import com.solvibe.main.BuildConfig
import com.solvibe.main.R
import com.solvibe.main.bean.ProductBean
import com.solvibe.main.config.ChannelType
import com.solvibe.main.config.Constant
import com.solvibe.utils.ext.logd
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 *creater:linjinhao on 2025/5/19 16:55
 */
class ProductViewModel : ViewModel() {
    private val account = Membership.membershipClient.account
    private val iCart = Membership.membershipClient.iCart
    private val _state = MutableStateFlow(ProductUIState())
    val state = _state.asStateFlow()
    val benefits = mutableStateListOf<BenefitCompareBean>().apply {
        addAll(getBenefitCompareList())
    }
    var userBean: UserBean? = null
    var selectIndex by mutableIntStateOf(0)
    val flow = snapshotFlow { selectIndex }

    val skus = mutableStateListOf<ProductBean>()

    private val _event = MutableSharedFlow<ProductEvent>()
    val event = _event.asSharedFlow()

    init {
        getProduct()
        collectUser()
    }

    fun setSku(list: List<ProductBean>) {
        skus.clear()
        skus.addAll(list)
    }

    private fun collectUser() {
        viewModelScope.launch {
            account.userFlow.collectLatest { user ->
                userBean = user
            }
        }
    }


    private fun getProduct() {
        viewModelScope.launch {
            _state.update { it.copy(skuState = SkuUIState.Loading) }
            val response = account.getSKUList()
            val data = response.data
            if (response.isSuccess) {
                val skus = if (data.isNullOrEmpty()) {
                    emptyList()
                } else {
                    queryGooglePrice(data = data, iCart)
                }
                setSku(skus)
                _state.update {
                    it.copy(skuState = SkuUIState.Success(data ?: emptyList()))
                }
            } else {
                _state.update { it.copy(skuState = SkuUIState.Fail) }
            }
        }
    }

    fun getSkuUnitStr(productBean: ProductBean): String {
        return when {
            productBean.isDaySubscribe() -> {
                "/Day"
            }

            productBean.isWeekSubscribe() -> {
                "/Day"
            }

            productBean.isMonthSubscribe() -> {
                "/Day"
            }

            productBean.isQuarterSubscribe() -> {
                "/Month"
            }

            productBean.isYearSubscribe() -> {
                "/Day"
            }

            productBean.isOneTimeSubscribe() -> {
                "/Day"
            }

            else -> {
                "Day"
            }
        }
    }


    fun purchase(skuBean: SKUBean) {
        viewModelScope.launch(SupervisorJob() + CoroutineExceptionHandler { _, throwable ->
            logd("purchase error:${throwable.stackTraceToString()}")
        }) {
            // 1. 拉取当前支付方式
            _state.update { it.copy(purchaseLoading = true) }
            // 2. 开始购买
            if (BuildConfig.channel == ChannelType.CHANNEL_GOOGLE) {
                purchaseGoogle(skuBean)
                _state.update { it.copy(purchaseLoading = false) }
            } else {
                purchaseICart(skuBean)
                _state.update { it.copy(purchaseLoading = false) }
            }
        }
    }

    private suspend fun purchaseGoogle(skuBean: SKUBean) {
        try {
            logd("purchaseGoogle skuid:${skuBean.skuID} ")
            val googlePurchaseBean = iCart.googlePurchase(
                skuBean = skuBean,
                source = Constant.fromSite,
                selectTokenBlock = { detail ->
                    detail?.firstOrNull {
                        Regex(Constant.PURCHASE_RENEWAL_PLAN_ID_REGEX).matches(it.basePlanId)
                    }?.offerToken ?: ""
                }
            )
            var confirming = true
            while (confirming) {
                val result = iCart.confirmGooglePurchase(googlePurchaseBean)
                when (result) {
                    ConfirmGooglePurchaseResult.Pending -> {
                    }

                    is ConfirmGooglePurchaseResult.Success -> {
                        confirming = false
                        refreshVipState()
                        _event.emit(ProductEvent.GooglePurchaseSuccess(result.confirmResultBean))
                    }

                    ConfirmGooglePurchaseResult.Fail -> {
                        confirming = false
                        _event.emit(ProductEvent.GoogleConfirmOrderFail)
                    }
                }
                delay(1_000)
            }
        } catch (cancel: UserCancelException) {
            _event.emit(ProductEvent.CancelGooglePurchase)
        } catch (e: NeedLoginException) {
            _event.emit(ProductEvent.NeedLogin)
        } catch (e: Exception) {
            _event.emit(ProductEvent.GooglePurchaseFail(e))
        }
    }

    private suspend fun purchaseICart(skuBean: SKUBean) {
        try {
            if (userBean == null) {
                _event.emit(ProductEvent.NeedLogin)
                return
            }
            val email = iCart.iCartPurchase(skuBean)
            if (email.isNotEmpty() && userBean == null) {
                _event.emit(ProductEvent.LoginTip(email))
            }
            if (email.isNotEmpty() && userBean != null) {
                _event.emit(ProductEvent.ICartPurchaseFinish)
            }
        } catch (e: NeedLoginException) {
            _event.emit(ProductEvent.NeedLogin)
        }
    }


    private fun getBenefitCompareList(): List<BenefitCompareBean> {
        val bean1 = BenefitCompareBean(
            R.string.basic_model,
            R.drawable.ic_circle_right_sv,
            R.drawable.ic_circle_right_sv_def
        )
        val bean2 = BenefitCompareBean(
            R.string.more_intelligent_models,
            R.drawable.ic_circle_right_sv,
            R.drawable.ic_circle_lock_def
        )
        val bean3 = BenefitCompareBean(
            R.string.more_information,
            R.drawable.ic_circle_right_sv,
            R.drawable.ic_circle_lock_def
        )
        val bean4 = BenefitCompareBean(
            R.string.deep_thinking,
            R.drawable.ic_circle_right_sv,
            R.drawable.ic_circle_lock_def
        )
        val bean5 = BenefitCompareBean(
            R.string.new_features_first,
            R.drawable.ic_circle_right_sv,
            R.drawable.ic_circle_lock_def
        )
        val bean6 = BenefitCompareBean(
            R.string.more_character_memories,
            R.drawable.ic_circle_right_sv,
            R.drawable.ic_circle_lock_def
        )
        val bean7 = BenefitCompareBean(
            R.string._7_24_online_service_support,
            R.drawable.ic_circle_right_sv,
            R.drawable.ic_circle_lock_def
        )
        return listOf(bean1, bean2, bean3, bean4, bean5, bean6, bean7)
    }
}

data class ProductUIState(
    val skuState: SkuUIState = SkuUIState.Init,
    val purchaseLoading: Boolean = false
)

sealed class SkuUIState {
    data object Init : SkuUIState()
    data object Loading : SkuUIState()
    data class Success(val skus: List<SKUBean>) : SkuUIState()
    data object Fail : SkuUIState()
}

data class BenefitCompareBean(
    val benefit: Int, @DrawableRes val premium: Int, @DrawableRes val free: Int
)


sealed interface ProductEvent {
    class GooglePurchaseSuccess(val bean: ConfirmResultBean?) : ProductEvent

    class LoginTip(val email: String) : ProductEvent

    data object NeedLogin : ProductEvent

    data object CancelGooglePurchase : ProductEvent

    class GooglePurchaseFail(val e: Exception) : ProductEvent
    data object GoogleConfirmOrderFail : ProductEvent
    data object ICartPurchaseFinish : ProductEvent
}
