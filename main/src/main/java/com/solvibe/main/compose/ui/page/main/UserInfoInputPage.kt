package com.solvibe.main.compose.ui.page.main

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.solvibe.main.R
import com.solvibe.main.bean.UserManager
import com.solvibe.main.compose.theme.White8
import com.solvibe.main.compose.theme.White80
import com.solvibe.main.compose.ui.components.AgeSelector
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTPage
import com.solvibe.main.compose.ui.components.DTTextFieldNoBorder
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.components.SelectableDrawable
import com.solvibe.main.compose.ui.components.SexSelector

@Composable
fun UserInfoInputPage(gotoMain: () -> Unit) {
    var userName by remember { mutableStateOf("") }
    var sexIndex by remember { mutableIntStateOf(-1) }
    var ageIndex by remember { mutableIntStateOf(-1) }

    val canSubmit by remember {
        derivedStateOf {
            userName.isNotBlank() && sexIndex != -1 && ageIndex != -1
        }
    }

    DTPage(background = R.drawable.new_user_info_select_bg) {
        Column(
            Modifier
                .fillMaxWidth()
                .navigationBarsPadding()
                .align(Alignment.BottomCenter),
        ) {
            Text(
                stringResource(R.string.new_user_info_select_page_title),
                Modifier.padding(horizontal = 16.dp),
                style = MaterialTheme.typography.headlineLarge.copy(
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontSize = 24.sp,
                    lineHeight = 24.sp,
                    fontWeight = FontWeight.Normal
                ),
            )
            DTVerticalSpacer(10.dp)
            Text(
                stringResource(R.string.new_user_info_select_page_content),
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = White80,
                    fontSize = 13.sp,
                )
            )
            DTVerticalSpacer(24.dp)
            SecTitle(R.string.user_name)
            DTVerticalSpacer(10.dp)
            DTTextFieldNoBorder(
                value = userName,
                onValueChange = {
                    if (it.length > 40) {
                        return@DTTextFieldNoBorder
                    }
                    userName = it
                },
                Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .height(46.dp)
                    .background(White8, RoundedCornerShape(12.dp)),
            )
            DTVerticalSpacer(20.dp)
            SecTitle(R.string.gender)
            DTVerticalSpacer(10.dp)
            SexSelector(sexIndex) { index ->
                sexIndex = index
            }
            DTVerticalSpacer(20.dp)
            SecTitle(R.string.age)
            DTVerticalSpacer(10.dp)
            AgeSelector(ageIndex) { index ->
                ageIndex = index
            }
            DTVerticalSpacer(36.dp)
            DTButton(
                stringResource(R.string.new_user_info_select_page_submit_btn_text),
                bg = SelectableDrawable(
                    enableDrawable = R.drawable.info_select_page_btn_enable_bg,
                    disableDrawable = R.drawable.btn_disable_bg
                ),
                Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .height(56.dp),
                canSubmit,
                stringResource(R.string.new_user_info_select_page_submit_btn_sub_text),
                shape = RoundedCornerShape(12.dp),
                onClick = {
                    UserManager.setName(userName)
                    UserManager.setGender(sexIndex + 1)
                    UserManager.setAge(ageIndex + 1)
                    UserManager.setIsFirstTimeLaunchAppFalse()
                    gotoMain()
                }
            )
            DTVerticalSpacer(27.dp)
        }
    }
}

@Composable
private fun SecTitle(@StringRes titleId: Int) {
    Text(
        stringResource(titleId),
        Modifier.padding(horizontal = 16.dp),
        style = MaterialTheme.typography.headlineMedium.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            lineHeight = 18.sp,
        ),
    )
}