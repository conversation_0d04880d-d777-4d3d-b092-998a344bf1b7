package com.solvibe.main.compose.ui.components

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp

@Composable
fun DTHorizontalSpacer(size: Dp) {
    Spacer(Modifier.width(size))
}

@Composable
fun DTVerticalSpacer(size: Dp) {
    Spacer(Modifier.height(size))
}