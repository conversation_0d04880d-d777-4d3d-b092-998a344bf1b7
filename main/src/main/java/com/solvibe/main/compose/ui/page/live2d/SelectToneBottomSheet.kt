package com.solvibe.main.compose.ui.page.live2d

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.solvibe.main.App
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White8
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.dialog.SVBottomSheet
import com.solvibe.main.state.Tone

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SelectToneBottomSheet(onDismiss: () -> Unit) {
    val toneList = App.viewModel.settingsState.toneList
    val characterSettings by App.viewModel.settingsState.characterSettingsFlow.collectAsStateWithLifecycle()
    var selectedToneValue by remember { mutableStateOf(characterSettings?.character) }

    SVBottomSheet(
        onDismiss = onDismiss,
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        containerColor = Color(0xFF19171F)
    ) {
        DTVerticalSpacer(8.dp)
        BSTitle(R.string.tone)
        DTVerticalSpacer(10.dp)
        ToneList(selectedToneValue, toneList) {
            selectedToneValue = it.backendValue
        }
        DTVerticalSpacer(20.dp)
        ConfirmBtnGroup(onDismiss) {
            App.viewModel.settingsState.selectTone(selectedToneValue)
            onDismiss()
        }
        DTVerticalSpacer(20.dp)
    }
}

@Composable
fun ToneList(currentToneBackendValue: String?, tones: List<Tone>, onClick: (Tone) -> Unit) {
    Column(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(White8)
    ) {
        tones.forEachIndexed { index, tone ->
            ToneItem(tone, tone.backendValue == currentToneBackendValue) {
                onClick(tone)
            }
            if (index < tones.lastIndex) {
                HorizontalDivider(
                    Modifier
                        .padding(horizontal = 16.dp)
                        .fillMaxWidth(),
                    0.5.dp,
                    color = Color(0xFF555555)
                )
            }
        }
    }
}

@Composable
fun ToneItem(tone: Tone, selected: Boolean, onClick: (tone: Tone) -> Unit) {
    Row(
        Modifier
            .fillMaxWidth()
            .height(42.dp)
            .clickable {
                onClick(tone)
            }
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            stringResource(tone.name),
            Modifier.weight(1f),
            maxLines = 1,
            style = MaterialTheme.typography.bodySmall.copy(
                color = Color.White
            )
        )
        DTHorizontalSpacer(10.dp)
        if (selected) {
            Icon(
                painterResource(R.drawable.ic_language_check),
                null,
                Modifier.size(18.dp),
                tint = Color.Unspecified
            )
        }
    }
}