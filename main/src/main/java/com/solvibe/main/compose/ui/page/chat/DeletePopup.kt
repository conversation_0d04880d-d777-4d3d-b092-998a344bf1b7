package com.solvibe.main.compose.ui.page.chat

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.solvibe.main.R
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTPopup

@Composable
fun DeletePopup(
    modifier: Modifier = Modifier,
    onDismiss: () -> Unit = {},
    onClick: () -> Unit,
) {
    DTPopup(
        modifier,
        alignment = Alignment.TopStart,
        onDismissRequest = onDismiss
    ) {
        Row(
            Modifier
                .height(32.dp)
                .clip(RoundedCornerShape(6.dp))
                .clickable(onClick = onClick)
                .background(Color(0xFF393640))
                .padding(horizontal = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painterResource(R.drawable.ic_msg_del),
                stringResource(R.string.Delete),
                Modifier.size(16.dp),
                tint = Color(0xFFFF5476)
            )
            DTHorizontalSpacer(8.dp)
            Text(
                stringResource(R.string.Delete),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = Color(0xFFFF5476),
                )
            )
        }
    }
}