package com.solvibe.main.compose.ui.page.chat

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import com.solvibe.main.R
import com.solvibe.main.compose.theme.Black
import com.solvibe.main.compose.ui.components.DTPopup
import com.solvibe.main.compose.ui.components.DTVerticalSpacer

sealed interface MsgPopupOptionType {
    data object Copy : MsgPopupOptionType
    data object Reply : MsgPopupOptionType
    data object Delete : MsgPopupOptionType
}

@Composable
fun MsgSelectedPopup(
    modifier: Modifier = Modifier,
    onDismiss: () -> Unit = {},
    onClick: (MsgPopupOptionType) -> Unit,
) {
    DTPopup(
        modifier,
        alignment = Alignment.TopEnd,
        offsetY = 8.dp,
        onDismissRequest = onDismiss
    ) {
        ConstraintLayout {
            val (arrowRef, contentRef) = createRefs()
            Icon(
                painterResource(R.drawable.ic_msg_select_popup_top_arrow),
                null,
                Modifier
                    .width(11.dp)
                    .height(8.dp)
                    .constrainAs(arrowRef) {
                        top.linkTo(parent.top)
                        end.linkTo(parent.end, 30.dp)
                    },
                tint = Color(0xFFD9D9D9)
            )
            Row(
                Modifier
                    .height(62.dp)
                    .clip(RoundedCornerShape(10.dp))
                    .background(Color(0xFFD9D9D9))
                    .constrainAs(contentRef) {
                        top.linkTo(arrowRef.top, 5.dp)
                        start.linkTo(parent.start)
                    }
            ) {
                MsgPopupOption(
                    painterResource(R.drawable.ic_msg_copy),
                    stringResource(R.string.copy)
                ) {
                    onClick(MsgPopupOptionType.Copy)
                }
                VerticalDivider(
                    Modifier.fillMaxHeight(),
                    0.5.dp,
                    color = Color(0xFFC7C7C7)
                )
                MsgPopupOption(
                    painterResource(R.drawable.ic_msg_reply),
                    stringResource(R.string.reply)
                ) {
                    onClick(MsgPopupOptionType.Reply)
                }
                VerticalDivider(
                    Modifier.fillMaxHeight(),
                    0.5.dp,
                    color = Color(0xFFC7C7C7)
                )
                MsgPopupOption(
                    painterResource(R.drawable.ic_msg_del),
                    stringResource(R.string.Delete)
                ) {
                    onClick(MsgPopupOptionType.Delete)
                }
            }
        }
    }
}

@Composable
fun MsgPopupOption(painter: Painter, text: String, onClick: () -> Unit) {
    Column(
        Modifier
            .width(69.dp)
            .fillMaxHeight()
            .clickable(onClick = onClick),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            painter,
            text,
            Modifier.size(24.dp),
            tint = Black
        )
        DTVerticalSpacer(2.dp)
        Text(
            text,
            color = Black,
            fontSize = 12.sp,
            fontWeight = FontWeight.W500,
            lineHeight = 16.sp,
        )
    }
}