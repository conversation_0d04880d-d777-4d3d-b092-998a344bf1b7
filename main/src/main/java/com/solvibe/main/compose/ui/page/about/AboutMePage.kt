package com.solvibe.main.compose.ui.page.about

import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.main.R
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTPage
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.components.SVTopBar
import com.solvibe.main.compose.utils.AppUtils

@Composable
fun AboutMePage(
    onBack: () -> Unit,
    viewModel: AboutViewModel = viewModel()
) {
    val context = LocalContext.current
    DTPage(
        background = R.drawable.bg_about_me,
        loading = viewModel.loading,
        onDismissLoading = {
            viewModel.showLoading(false)
        }
    ) {
        Column(
            Modifier
                .statusBarsPadding()
                .navigationBarsPadding()
                .fillMaxSize()
        ) {
            SVTopBar(null, onBack = onBack)
            DTVerticalSpacer(45.dp)
            AppIcon()
            DTVerticalSpacer(20.dp)
            AppName()
            DTVerticalSpacer(8.dp)
            AppVersion()
            DTVerticalSpacer(50.dp)
            AppDesc()
            Spacer(Modifier.weight(1f))
            CheckUpdateBtn {
                viewModel.checkUpdate()
            }
            DTVerticalSpacer(18.dp)
            Copyright()
            DTVerticalSpacer(16.dp)
        }
    }

    viewModel.versionBean?.let {
        UpdateDialog(
            versionBean = it,
            onUpdate = {
                try {
                    val uri = ("market://details?id=" + context.packageName).toUri()
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    intent.setPackage("com.android.vending")
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)
                    viewModel.showUpdateDialog(null)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            },
            onDismiss = {
                viewModel.showUpdateDialog(null)
            }
        )
    }
}

@Composable
private fun ColumnScope.AppIcon() {
    Image(
        painterResource(R.drawable.ic_launcher),
        null,
        Modifier
            .size(80.dp)
            .align(Alignment.CenterHorizontally)
    )
}

@Composable
private fun ColumnScope.AppName() {
    Text(
        stringResource(R.string.app_name),
        Modifier.align(Alignment.CenterHorizontally),
        style = MaterialTheme.typography.headlineLarge.copy(
            color = MaterialTheme.colorScheme.onPrimary
        )
    )
}

@Composable
private fun ColumnScope.AppVersion() {
    val context = LocalContext.current
    val version = buildString {
        append("${stringResource(R.string.version)} ${AppUtils.getVersion(context)}")
    }
    Text(
        version,
        Modifier.align(Alignment.CenterHorizontally),
        style = MaterialTheme.typography.bodySmall.copy(
            color = MaterialTheme.colorScheme.onPrimary
        )
    )
}

@Composable
private fun ColumnScope.AppDesc() {
    Text(
        stringResource(R.string.about_note),
        Modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.bodySmall.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            lineHeight = 20.sp,
            textAlign = TextAlign.Center
        )
    )
}

@Composable
private fun CheckUpdateBtn(onClick: () -> Unit) {
    DTButton(
        stringResource(R.string.check_for_updates),
        brush = Brush.horizontalGradient(
            listOf(
                Color(0xFFBE4EFF),
                Color(0xFFF343A1),
                Color(0xFFFF5476),
            )
        ),
        Modifier
            .padding(horizontal = 30.dp)
            .fillMaxWidth()
            .height(44.dp),
        onClick = onClick
    )
}

@Composable
private fun ColumnScope.Copyright() {
    Text(
        stringResource(R.string.all_right),
        Modifier.align(Alignment.CenterHorizontally),
        style = MaterialTheme.typography.labelMedium.copy(
            color = Color(0xFF8A8C91),
        )
    )
}