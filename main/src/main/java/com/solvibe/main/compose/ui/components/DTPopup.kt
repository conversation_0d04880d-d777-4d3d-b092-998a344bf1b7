package com.solvibe.main.compose.ui.components

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import kotlin.math.roundToInt

@Composable
fun DTPopup(
    modifier: Modifier = Modifier,
    alignment: Alignment = Alignment.TopStart,
    offsetX: Dp = 0.dp,
    offsetY: Dp = 0.dp,
    onDismissRequest: (() -> Unit)? = null,
    properties: PopupProperties = PopupProperties(),
    content: @Composable () -> Unit
) {
    DTPopup(modifier, alignment, DpOffset(offsetX, offsetY), onDismissRequest, properties, content)
}

@Composable
fun DTPopup(
    modifier: Modifier = Modifier,
    alignment: Alignment = Alignment.TopStart,
    offset: DpOffset = DpOffset(0.dp, 0.dp),
    onDismissRequest: (() -> Unit)? = null,
    properties: PopupProperties = PopupProperties(),
    content: @Composable () -> Unit
) {
    BackHandler {
        onDismissRequest?.invoke()
    }
    Box(modifier = modifier) {
        val offsetPx = with(LocalDensity.current) {
            IntOffset(offset.x.toPx().roundToInt(), offset.y.toPx().roundToInt())
        }
        Popup(alignment, offsetPx, onDismissRequest, properties, content)
    }
}