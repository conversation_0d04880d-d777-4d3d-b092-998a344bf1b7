package com.solvibe.main.compose.theme

import androidx.compose.ui.graphics.Color

val SocialPurple = Color(0xFFFD67A9)
val SocialBlue = Color(0x80FF5D8B)
val SocialPink = Color(0xFFFF62A1)
val LightPurple = Color(0xFFDECEFF)
val Purple40 = Color(0x668F64ED)
val Purple50 = Color(0x808F64ED)
val Pink40 = Color(0x66FF608D)
val Pink50 = Color(0x80FF608D)
val LightPink = Color(0xFFFFCEDC)
val LightWhite = Color(0xFFECEBED)
val Black = Color(0xFF101015)
val Black100 = Color(0xFF000000)
val Black30 = Color(0x4D000000)
val Black40 = Color(0x66000000)
val Black50 = Color(0x80000000)
val Black60 = Color(0x99000000)
val Black80 = Color(0xCC000000)
val Black10 = Color(0x1A000000)
val Black63 = Color(0xA1000000)
val Transparent = Color(0x00000000)
val White = Color(0xFFFFFFFF)
val White90 = Color(0xE6FFFFFF)
val White70 = Color(0xB3FFFFFF)
val White60 = Color(0x99FFFFFF)
val White80 = Color(0xCCFFFFFF)
val White50 = Color(0x80FFFFFF)
val White40 = Color(0x66FFFFFF)
val White35 = Color(0x59FFFFFF)
val White30 = Color(0x4DFFFFFF)
val White20 = Color(0x33FFFFFF)
val White12 = Color(0x1FFFFFFF)
val White6 = Color(0x0fFFFFFF)
val White14 = Color(0x26FFFFFF)
val White15 = Color(0x24FFFFFF)
val White16 = Color(0x29FFFFFF)
val White10 = Color(0x1AFFFFFF)
val White8 = Color(0x14FFFFFF)
val White5 = Color(0x0DFFFFFF)
val Gray = Color(0xFF323436)
val LightGray = Color(0xFF8A8C91)
val DarkBlack = Color(0xFF181A1C)