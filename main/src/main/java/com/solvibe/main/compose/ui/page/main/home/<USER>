package com.solvibe.main.compose.ui.page.main.home

import android.app.Activity
import android.content.Intent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.solvibe.main.R
import com.solvibe.main.activity.WebActivity
import com.solvibe.main.bean.UserManager
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.dialog.SVDialog
import com.solvibe.main.config.Constant
import com.solvibe.main.utils.StatisticsUtil
import kotlinx.coroutines.delay

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun PrivacyAgreementDialog() {
    var show by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        delay(1000)
        show = !UserManager.isAgreePrivacyPolicy()
    }

    val onAccept = remember {
        {
            UserManager.setAgreePrivacyPolicy()
            show = false
        }
    }


    if (show) {
        val context = LocalContext.current
        val terms = stringResource(R.string.terms) // Terms of Use
        val policy = stringResource(R.string.policy) // Privacy Policy

        val fullText = String.format(
            stringResource(R.string.privacy_agreement_dialog_content),
            terms,
            policy
        )

        // 定义三个可点击区域及对应跳转目标
        val clickableItems = listOf(
            ClickableItem(
                text = terms,
                target = WebActivity::class.java,
                color = Color(0xFFFD517B),
                paramKey = "url",
                paramValue = Constant.terms + Constant.webParams
            ),
            ClickableItem(
                text = policy,
                target = WebActivity::class.java,
                color = Color(0xFFFD517B),
                paramKey = "url",
                paramValue = Constant.privacy + Constant.webParams
            )
        )

        // 构建带注解的字符串
        val annotatedString = buildAnnotatedString {
            append(fullText)

            clickableItems.forEach { item ->
                val startIndex = fullText.indexOf(item.text)
                check(startIndex != -1) { "文本中未找到 ${item.text}" }
                val endIndex = startIndex + item.text.length

                addStyle(
                    style = SpanStyle(color = item.color),
                    start = startIndex,
                    end = endIndex
                )

                addStringAnnotation(
                    tag = "CLICKABLE",
                    annotation = "${item.target.name}|${item.paramKey}|${item.paramValue}",
                    start = startIndex,
                    end = endIndex
                )
            }
        }

        SVDialog({}) {
            var layoutResult by remember { mutableStateOf<TextLayoutResult?>(null) }
            Column(
                Modifier
                    .width(320.dp)
                    .clip(RoundedCornerShape(25.dp))
                    .background(Color(0xFF212122)),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                DTVerticalSpacer(40.dp)
                Text(
                    stringResource(R.string.welcome_to_solvibe),
                    style = MaterialTheme.typography.headlineLarge.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 24.sp,
                        lineHeight = 36.sp,
                    ),
                )
                DTVerticalSpacer(7.dp)
                Text(
                    text = annotatedString,
                    modifier = Modifier
                        .padding(horizontal = 25.dp)
                        .fillMaxWidth()
                        .pointerInput(Unit) {
                            detectTapGestures { tapOffset ->
                                layoutResult?.let { layout ->
                                    // 将点击位置转换为文本偏移量
                                    val offset = layout.getOffsetForPosition(tapOffset)

                                    // 获取所有匹配的注解
                                    annotatedString.getStringAnnotations(
                                        "CLICKABLE",
                                        offset,
                                        offset
                                    ).firstOrNull()
                                        ?.let { annotation ->
                                            val parts = annotation.item.split("|")
                                            if (parts.size == 3) {
                                                val className = parts[0]
                                                val paramKey = parts[1]
                                                val paramValue = parts[2]

                                                Intent(
                                                    context,
                                                    Class.forName(className)
                                                ).apply {
                                                    putExtra(paramKey, paramValue)
                                                    context.startActivity(this)
                                                }
                                            }
                                        }
                                }
                            }
                        },
                    onTextLayout = { layoutResult = it },
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 14.sp,
                        lineHeight = 20.sp,
                        textAlign = TextAlign.Center
                    )
                )
                DTVerticalSpacer(27.dp)
                DTButton(
                    stringResource(R.string.privacy_agreement_dialog_accept_btn_text),
                    Brush.horizontalGradient(
                        listOf(
                            Color(0xFFBE4EFF),
                            Color(0xFFF343A1),
                            Color(0xFFFF5476)
                        )
                    ),
                    Modifier
                        .padding(horizontal = 26.dp)
                        .fillMaxWidth()
                        .height(44.dp),
                    textStyle = MaterialTheme.typography.headlineSmall.copy(
                        fontSize = 14.sp,
                        lineHeight = 18.sp,
                        fontWeight = FontWeight.Bold
                    ),
                    onClick = {
                        StatisticsUtil.init(true, context)
                        onAccept()
                    }
                )
                DTVerticalSpacer(50.dp)
            }
        }
    }
}

data class ClickableItem(
    val text: String,
    val target: Class<out Activity>,
    val color: Color,
    val paramKey: String,
    val paramValue: String
)