package com.solvibe.main.compose.ui.components

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.calculateEndPadding
import androidx.compose.foundation.layout.calculateStartPadding
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

val DTTextFieldTextStyle: TextStyle
    @Composable get() {
        return MaterialTheme.typography.bodySmall.copy(
            color = MaterialTheme.colorScheme.onPrimary,
        )
    }

@Stable
data class DTTextFieldIcon(
    val painter: Painter,
    val onClick: () -> Unit,
    val tint: Color? = null,
    val size: Dp = 40.dp,
    val padding: Dp = 10.dp,
)

@Composable
fun DTTextFieldNoBorder(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    textStyle: TextStyle = DTTextFieldTextStyle,
    placeholder: String? = null,
    placeholderColor: Color = MaterialTheme.colorScheme.onTertiary,
    rightIcon: DTTextFieldIcon? = null,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = true,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    interactionSource: MutableInteractionSource? = null,
    verticalAlignment: Alignment.Vertical = Alignment.CenterVertically,
    contentPadding: PaddingValues = PaddingValues(horizontal = 16.dp)
) {
    var hasFocus by remember { mutableStateOf(false) }
    BasicTextField(
        value = value,
        modifier = modifier.onFocusChanged { focusStatus ->
            hasFocus = focusStatus.hasFocus
        },
        onValueChange = onValueChange,
        enabled = enabled,
        readOnly = readOnly,
        textStyle = textStyle,
        cursorBrush = SolidColor(MaterialTheme.colorScheme.onPrimary),
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        interactionSource = interactionSource,
        singleLine = singleLine,
        maxLines = maxLines,
        minLines = minLines,
        decorationBox = @Composable { innerTextField ->
            val dir = LocalLayoutDirection.current
            Row(
                Modifier
                    .fillMaxSize()
                    .padding(
                        start = contentPadding.calculateStartPadding(dir),
                        top = contentPadding.calculateTopPadding(),
                        end = contentPadding.calculateEndPadding(dir) -
                                (rightIcon?.padding ?: 0.dp),
                        bottom = contentPadding.calculateBottomPadding()
                    ),
                verticalAlignment = verticalAlignment
            ) {
                Box(Modifier.weight(1f)) {
                    if (value.isEmpty() && !hasFocus && !placeholder.isNullOrEmpty()) {
                        Text(
                            placeholder,
                            Modifier.fillMaxWidth(),
                            style = textStyle.copy(color = placeholderColor),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                    innerTextField()
                }
                if (rightIcon != null) {
                    Image(
                        rightIcon.painter,
                        null,
                        Modifier
                            .clip(CircleShape)
                            .clickable(onClick = rightIcon.onClick)
                            .size(rightIcon.size)
                            .padding(rightIcon.padding),
                    )
                }
            }
        }
    )
}

@Composable
fun DTTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    textFieldModifier: Modifier = Modifier.fillMaxWidth(),
    enabled: Boolean = true,
    readOnly: Boolean = false,
    textStyle: TextStyle = DTTextFieldTextStyle,
    placeholder: String? = null,
    placeholderStyle: TextStyle = DTTextFieldTextStyle.copy(color = MaterialTheme.colorScheme.onTertiary),
    rightIcon: DTTextFieldIcon? = null,
    tips: String? = null,
    tipsModifier: Modifier = Modifier,
    tipsStyle: TextStyle = DTTextFieldTextStyle.copy(
        color = Color(0xFFFF716A),
        fontSize = 10.sp,
        lineHeight = 10.sp
    ),
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = true,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    interactionSource: MutableInteractionSource? = null,
    verticalAlignment: Alignment.Vertical = Alignment.CenterVertically,
    contentPadding: PaddingValues = PaddingValues(horizontal = 16.dp)
) {
    var hasFocus by remember { mutableStateOf(false) }
    Column(modifier) {
        BasicTextField(
            value = value,
            modifier = textFieldModifier
                .onFocusChanged { focusStatus ->
                    hasFocus = focusStatus.hasFocus
                },
            onValueChange = onValueChange,
            enabled = enabled,
            readOnly = readOnly,
            textStyle = textStyle,
            cursorBrush = SolidColor(MaterialTheme.colorScheme.onPrimary),
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions,
            interactionSource = interactionSource,
            singleLine = singleLine,
            maxLines = maxLines,
            minLines = minLines,
            visualTransformation = visualTransformation,
            decorationBox = @Composable { innerTextField ->
                val dir = LocalLayoutDirection.current
                Row(
                    Modifier
                        .fillMaxSize()
                        .padding(
                            start = contentPadding.calculateStartPadding(dir),
                            top = contentPadding.calculateTopPadding(),
                            end = contentPadding.calculateEndPadding(dir) -
                                    (rightIcon?.padding ?: 0.dp),
                            bottom = contentPadding.calculateBottomPadding()
                        ),
                    verticalAlignment = verticalAlignment
                ) {
                    Box(Modifier.weight(1f)) {
                        if (value.isEmpty() && !hasFocus && !placeholder.isNullOrEmpty()) {
                            Text(
                                placeholder,
                                Modifier.fillMaxWidth(),
                                style = placeholderStyle,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                        innerTextField()
                    }
                    if (rightIcon != null) {
                        Image(
                            rightIcon.painter,
                            null,
                            Modifier
                                .clip(CircleShape)
                                .clickable(onClick = rightIcon.onClick)
                                .size(rightIcon.size)
                                .padding(rightIcon.padding),
                        )
                    }
                }
            }
        )

        if (tips != null) {
            DTVerticalSpacer(5.dp)
            Text(tips, tipsModifier, style = tipsStyle)
        }
    }
}

@Composable
fun DTTextFieldRowGroup(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    textStyle: TextStyle = DTTextFieldTextStyle,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = false,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    interactionSource: MutableInteractionSource? = null,
    cursorBrush: Brush = SolidColor(MaterialTheme.colorScheme.onPrimary),
    verticalAlignment: Alignment.Vertical = Alignment.CenterVertically,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    startContent: @Composable RowScope.() -> Unit = {},
    placeholder: @Composable () -> Unit = {},
    endContent: @Composable RowScope.() -> Unit = {}
) {
    var hasFocus by remember { mutableStateOf(false) }
    BasicTextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier.onFocusChanged { focusStatus ->
            hasFocus = focusStatus.hasFocus
        },
        enabled = enabled,
        readOnly = readOnly,
        textStyle = textStyle,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        singleLine = singleLine,
        maxLines = maxLines,
        minLines = minLines,
        visualTransformation = visualTransformation,
        onTextLayout = onTextLayout,
        interactionSource = interactionSource,
        cursorBrush = cursorBrush,
        decorationBox = @Composable { innerTextField ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(contentPadding), verticalAlignment = verticalAlignment
            ) {
                startContent()
                Box(Modifier.weight(1f)) {
                    if (value.isEmpty() && !hasFocus) {
                        placeholder()
                    }
                    Box(
                        Modifier
                            .fillMaxWidth()
                            .animateContentSize()
                    ) {
                        innerTextField()
                    }
                }
                endContent()
            }
        }
    )
}

@Composable
fun DTTextFieldRowGroup(
    value: TextFieldValue,
    onValueChange: (TextFieldValue) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    textStyle: TextStyle = DTTextFieldTextStyle,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = false,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    interactionSource: MutableInteractionSource? = null,
    cursorBrush: Brush = SolidColor(MaterialTheme.colorScheme.onPrimary),
    verticalAlignment: Alignment.Vertical = Alignment.CenterVertically,
    textPadding: PaddingValues = PaddingValues(0.dp),
    startContent: @Composable RowScope.() -> Unit = {},
    placeholder: @Composable () -> Unit = {},
    endContent: @Composable RowScope.() -> Unit = {}
) {
    var hasFocus by remember { mutableStateOf(false) }
    BasicTextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier.onFocusChanged { focusStatus ->
            hasFocus = focusStatus.hasFocus
        },
        enabled = enabled,
        readOnly = readOnly,
        textStyle = textStyle,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        singleLine = singleLine,
        maxLines = maxLines,
        minLines = minLines,
        visualTransformation = visualTransformation,
        onTextLayout = onTextLayout,
        interactionSource = interactionSource,
        cursorBrush = cursorBrush,
        decorationBox = @Composable { innerTextField ->
            Row(Modifier.fillMaxWidth(), verticalAlignment = verticalAlignment) {
                startContent()
                Box(
                    Modifier
                        .weight(1f)
                        .animateContentSize()
                        .padding(textPadding)
                ) {
                    if (value.text.isEmpty() && !hasFocus) {
                        placeholder()
                    }
                    innerTextField()
                }
                endContent()
            }
        }
    )
}

@Composable
fun DTTextFieldColumnGroup(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    textGroupModifier: Modifier = Modifier,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    textStyle: TextStyle = DTTextFieldTextStyle,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = false,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    interactionSource: MutableInteractionSource? = null,
    cursorBrush: Brush = SolidColor(MaterialTheme.colorScheme.onPrimary),
    topContent: @Composable ColumnScope.() -> Unit = {},
    placeholder: @Composable () -> Unit = {},
    bottomContent: @Composable ColumnScope.() -> Unit = {}
) {
    var hasFocus by remember { mutableStateOf(false) }
    BasicTextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier.onFocusChanged { focusStatus ->
            hasFocus = focusStatus.hasFocus
        },
        enabled = enabled,
        readOnly = readOnly,
        textStyle = textStyle,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        singleLine = singleLine,
        maxLines = maxLines,
        minLines = minLines,
        visualTransformation = visualTransformation,
        onTextLayout = onTextLayout,
        interactionSource = interactionSource,
        cursorBrush = cursorBrush,
        decorationBox = @Composable { innerTextField ->
            Column(Modifier.fillMaxWidth()) {
                topContent()
                Box(textGroupModifier.animateContentSize()) {
                    if (value.isEmpty() && !hasFocus) {
                        placeholder()
                    }
                    innerTextField()
                }
                bottomContent()
            }
        }
    )
}