package com.solvibe.main.compose.ui.page.live2d

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.snap
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalNavigationDrawer
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.LoginInvalidEvent
import com.solvibe.Membership
import com.solvibe.character.net.ws.WsData
import com.solvibe.live2d.Live2dView
import com.solvibe.main.App
import com.solvibe.main.R
import com.solvibe.main.compose.effect.ReactiveEffect
import com.solvibe.main.compose.theme.Black40
import com.solvibe.main.compose.theme.White70
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTPage
import com.solvibe.main.compose.ui.components.IntimacyHearts
import com.solvibe.main.compose.ui.dialog.SVAlertDialog
import com.solvibe.main.compose.ui.modifier.click
import com.solvibe.main.compose.ui.modifier.verticalAlphaGradient
import com.solvibe.main.compose.ui.page.main.home.PrivacyAgreementDialog
import com.solvibe.main.ext.showToast
import com.solvibe.main.state.UIMsg
import com.solvibe.utils.ext.dpi
import com.solvibe.utils.ext.dpn
import kotlinx.coroutines.launch

@Composable
fun Live2DChatPage(
    onAvatarClick: () -> Unit,
    onLoginClick: () -> Unit,
    onSignUpClick: () -> Unit,
    onMoreClick: () -> Unit,
    onGotoPay: () -> Unit,
    onReport: () -> Unit,
    onChatRecord: () -> Unit,
    viewModel: Live2DViewModel = viewModel()
) {
    val appViewModel = App.viewModel
    val scope = rememberCoroutineScope()
    val drawerState = rememberDrawerState(DrawerValue.Closed)
    val keyboardController = LocalSoftwareKeyboardController.current
    val listState = rememberLazyListState()
    val messages by appViewModel.messageState.messagesFlow.collectAsStateWithLifecycle()
    val guestInfo by viewModel.guestFlow.collectAsState()
    val user by viewModel.userFlow.collectAsState()

    LaunchedEffect(Unit) {
        viewModel.collectMsgFlow()
    }

    LaunchedEffect(Unit) {
        viewModel.collectWsDataFlow()
    }

    ReactiveEffect(messages.size) {
        scope.launch {
            listState.scrollToItem(0)
        }
    }

    LifecycleResumeEffect(Unit) {
        onPauseOrDispose {
            if (lifecycle.currentState == Lifecycle.State.RESUMED) {
                return@onPauseOrDispose
            }
            viewModel.stopAudio()
        }
    }

    if (user != null) {
        LaunchedEffect(user) {
            viewModel.refreshUserInfo()
        }
    } else {
        LaunchedEffect(guestInfo) {
            viewModel.refreshGuestInfo()
        }
    }

    LaunchedEffect(drawerState.isOpen) {
        if (drawerState.isOpen) {
            keyboardController?.hide()
        }
    }

    HandleEvent(
        onLoginInvalid = onLoginClick,
        onLoginOutByOther = onLoginClick,
        onLoginOutByChangePassword = onLoginClick
    )

    ModalNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            MainDrawer(
                drawerState = drawerState,
                onAvatarClick = onAvatarClick,
                onLoginClick = onLoginClick,
                onSignUpClick = onSignUpClick,
                onMoreClick = onMoreClick
            )
        }
    ) {
        DTPage(
            Modifier.click {
                keyboardController?.hide()
                viewModel.showChatMore(false)
            }
        ) {
            ComposeLive2DView(
                Modifier
                    .navigationBarsPadding()
                    .fillMaxSize()
            )
            Column(Modifier.fillMaxSize()) {
                TitleBar(
                    onOpenDrawer = {
                        scope.launch {
                            drawerState.open()
                        }
                    },
                    onGetBenefits = onGotoPay,
                    onShowCharacter = {
                        viewModel.showCharacterSettings(true)
                    }
                )
                MessageList(
                    listState,
                    Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    viewModel.intimacyProgressData,
                    viewModel.showWaitingMessage,
                    messages,
                    onScrollToBottom = {
                        scope.launch {
                            listState.animateScrollToItem(0)
                        }
                    },
                    onLoadMore = {
                        appViewModel.loadMoreMsg()
                    }
                )
                Live2DChatBottomBar()
            }
        }
    }

    if (viewModel.showCharacterSettings) {
        CharacterSettingsBottomSheet(
            onDismiss = {
                viewModel.showCharacterSettings(false)
            },
            onReport = {
                viewModel.showCharacterSettings(false)
                onReport()
            },
            onChatRecord = {
                viewModel.showCharacterSettings(false)
                onChatRecord()
            },
            onGotoPay = onGotoPay
        )
    }
    if (viewModel.showSelectLLMModel) {
        SelectLLMBottomSheet(
            onDismiss = {
                viewModel.showSelectLLMModel(false)
            }
        )
    }
    if (viewModel.showSelectTone) {
        SelectToneBottomSheet(
            onDismiss = {
                viewModel.showSelectTone(false)
            }
        )
    }
    viewModel.showDeleteSessionDialog?.let { sessionId ->
        SVAlertDialog(
            content = R.string.delete_session_content,
            onConfirm = {
                appViewModel.deleteSession(sessionId)
                if (messages.any { it.sessionId == sessionId }) {
                    viewModel.stopAudio()
                }
                viewModel.showDeleteSessionDialog(null)
            },
            onCancel = {
                viewModel.showDeleteSessionDialog(null)
            }
        )
    }
    if (viewModel.showResetChatDialog) {
        SVAlertDialog(
            content = R.string.reset_chat_content,
            confirmText = R.string.reset,
            onConfirm = {
                viewModel.stopAudio()
                appViewModel.resetCurrentSession()
                viewModel.showResetChatDialog(false)
                viewModel.showChatMore(false)
            },
            onCancel = {
                viewModel.showResetChatDialog(false)
            }
        )
    }
    if (viewModel.showTarotFortuneDialog) {
        TarotFortuneBottomSheet(
            onDismiss = {
                viewModel.showTarotFortuneDialog(false)
            }
        )
    }
    PrivacyAgreementDialog()
}

@Composable
fun TitleBar(
    onOpenDrawer: () -> Unit,
    onGetBenefits: () -> Unit,
    onShowCharacter: () -> Unit
) {
    Row(
        Modifier
            .statusBarsPadding()
            .fillMaxWidth()
            .height(56.dp)
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Icon(
            painterResource(R.drawable.ic_drawer),
            null,
            Modifier
                .size(19.dp, 16.dp)
                .click(onOpenDrawer),
            tint = Color.Unspecified
        )

        Row(
            Modifier
                .height(28.dp)
                .clip(RoundedCornerShape(50))
                .clickable(onClick = onGetBenefits)
                .background(Black40)
                .padding(horizontal = 14.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painterResource(R.drawable.ic_get_benefits),
                null,
                Modifier.size(16.dp),
                tint = Color.Unspecified
            )
            DTHorizontalSpacer(4.dp)
            Text(
                stringResource(R.string.get_benefits),
                style = MaterialTheme.typography.labelMedium.copy(
                    color = White70
                )
            )
        }

        Icon(
            painterResource(R.drawable.ic_character),
            null,
            Modifier
                .size(22.dp)
                .click(onShowCharacter),
            tint = Color.Unspecified
        )
    }
}

@Composable
fun MessageList(
    listState: LazyListState,
    modifier: Modifier = Modifier,
    intimacyProgressData: WsData.IntimacyData?,
    showWaitingMessage: Boolean,
    messages: List<UIMsg>,
    onScrollToBottom: () -> Unit,
    onLoadMore: () -> Unit,
) {
    val firstVisibleItemIndex by remember {
        derivedStateOf { listState.firstVisibleItemIndex }
    }

    // 追踪上一次滚动时第一个可见子项的索引
    var lastKnownFirstVisibleItemIndex by remember { mutableIntStateOf(listState.firstVisibleItemIndex) }

    val isSlidingUpward by remember {
        derivedStateOf {
            val firstVisibleItemIndex = listState.firstVisibleItemIndex
            val slidingUpward = firstVisibleItemIndex <= lastKnownFirstVisibleItemIndex
            lastKnownFirstVisibleItemIndex = firstVisibleItemIndex
            slidingUpward
        }
    }

    val alphaColors by animateColorAsState(
        if (isSlidingUpward) Color.Transparent else Color.Black,
        animationSpec = tween(300)
    )

    Box(modifier) {
        LazyColumn(
            Modifier
                .align(Alignment.BottomCenter)
                .fillMaxSize()
                .verticalAlphaGradient(
                    listOf(alphaColors, Color.Black)
                ),
            state = listState,
            reverseLayout = true,
            contentPadding = PaddingValues(start = 16.dp, top = 16.dp, end = 16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp, Alignment.Bottom),
        ) {
            item("MASSAGE_BOTTOM_OR_WAITING_MESSAGE") {
                AnimatedVisibility(
                    showWaitingMessage,
                    enter = slideInVertically { it },
                    exit = fadeOut(snap())
                ) {
                    WaitingReply()
                }
            }
            itemsIndexed(messages, key = { _, it -> it.id }) { index, msg ->
                ChatMsg(msg, index == 0)
            }
            item("LOADING_MORE") {
                LaunchedEffect(Unit) {
                    onLoadMore()
                }
            }
        }

        IntimacyProgress(intimacyProgressData)
        if (intimacyProgressData != null) {
            IntimacyHearts(
                Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f)
            )
        }

        AnimatedContent(
            firstVisibleItemIndex > 0,
            Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 6.dp)
                .size(36.dp),
            transitionSpec = {
                (fadeIn(animationSpec = tween(300)) +
                        scaleIn(initialScale = 0.8f, animationSpec = tween(300)))
                    .togetherWith(
                        fadeOut(animationSpec = tween(300)) +
                                scaleOut(tween(300), 0.8f)
                    )
            }
        ) { target ->
            if (target) {
                Icon(
                    painterResource(R.drawable.ic_scroll_to_down),
                    null,
                    Modifier
                        .fillMaxSize()
                        .clip(CircleShape)
                        .clickable(onClick = onScrollToBottom)
                        .background(Color(0xFF423F49)),
                    tint = Color.Unspecified
                )
            } else {
                Box(Modifier.fillMaxSize())
            }
        }
    }
}

@Composable
private fun HandleEvent(
    onLoginInvalid: () -> Unit,
    onLoginOutByChangePassword: () -> Unit,
    onLoginOutByOther: () -> Unit
) {
    val context = LocalContext.current
    LaunchedEffect(Membership.loginInvalidEvent) {
        Membership.loginInvalidEvent.collect {
            when (it) {
                LoginInvalidEvent.LoginInvalid -> {
                    onLoginInvalid()
                    showToast(context.getString(R.string.logout_out_toast))
                }

                LoginInvalidEvent.LoginOutByChangePassword -> {
                    onLoginOutByChangePassword()
                    showToast(context.getString(R.string.logout_out_toast))
                }

                LoginInvalidEvent.LoginOutByOther -> {
                    onLoginOutByOther()
                    showToast(context.getString(R.string.logout_out_toast))
                }
            }
        }
    }
}

@Composable
fun ComposeLive2DView(modifier: Modifier = Modifier) {
    AndroidView(
        factory = { context ->
            Live2dView(context).apply {
                // 设置模型高度，宽度自适应保持比例
                setModelHeight(600.dpi)
                // 设置初始背景
                setBackgroundImage(com.solvibe.live2d.R.drawable.bg1)
                setModelVerticalOffset(10.dpn)
            }
        },
        modifier,
        onReset = {
            it.onResume()
        },
        onRelease = {
            it.onPause()
            it.onDestroy()
        }
    )
}