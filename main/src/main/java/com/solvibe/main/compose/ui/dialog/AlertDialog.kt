package com.solvibe.main.compose.ui.dialog

import androidx.annotation.StringRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White70
import com.solvibe.main.compose.theme.White80
import com.solvibe.main.compose.ui.components.DTButton
import com.solvibe.main.compose.ui.components.DTButtonTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer

@Composable
fun SVAlertDialog(
    @StringRes content: Int,
    @StringRes confirmText: Int = R.string.confirm,
    onConfirm: () -> Unit,
    onCancel: () -> Unit
) {
    SVDialog(
        onCancel
    ) {
        Column(
            Modifier
                .width(320.dp)
                .background(Color(0xFF2F2A3B), RoundedCornerShape(25.dp))
                .padding(horizontal = 24.dp, vertical = 30.dp)
        ) {
            Text(
                stringResource(content),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = White80,
                    textAlign = TextAlign.Center
                )
            )
            DTVerticalSpacer(20.dp)
            DialogConfirmBtnGroup(onCancel, confirmText, onConfirm)
        }
    }
}

@Composable
fun DialogConfirmBtnGroup(
    onCancel: () -> Unit,
    @StringRes confirmText: Int,
    onConfirm: () -> Unit
) {
    Row(
        Modifier
            .fillMaxWidth()
            .height(44.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        OutlinedButton(
            onClick = onCancel,
            Modifier
                .width(130.dp)
                .fillMaxHeight(),
            shape = RoundedCornerShape(50),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = White70
            ),
            border = BorderStroke(1.dp, Color(0x99999999))
        ) {
            Text(
                stringResource(R.string.cancel),
                style = DTButtonTextStyle.copy(
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                )
            )
        }
        DTButton(
            stringResource(confirmText),
            brush = Brush.horizontalGradient(
                listOf(
                    Color(0xFFBE4EFF),
                    Color(0xFFF343A1),
                    Color(0xFFFF5476),
                )
            ),
            modifier = Modifier
                .width(130.dp)
                .fillMaxHeight(),
            shape = RoundedCornerShape(50),
            textStyle = DTButtonTextStyle.copy(
                fontSize = 14.sp,
            ),
            onClick = onConfirm
        )
    }
}