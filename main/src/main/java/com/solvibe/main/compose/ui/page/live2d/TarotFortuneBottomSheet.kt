package com.solvibe.main.compose.ui.page.live2d

import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White10
import com.solvibe.main.compose.theme.White35
import com.solvibe.main.compose.theme.White60
import com.solvibe.main.compose.theme.White70
import com.solvibe.main.compose.ui.components.DTHorizontalSpacer
import com.solvibe.main.compose.ui.components.DTTextFieldRowGroup
import com.solvibe.main.compose.ui.components.DTTextFieldTextStyle
import com.solvibe.main.compose.ui.components.DTVerticalSpacer
import com.solvibe.main.compose.ui.dialog.SVBottomSheet
import com.solvibe.main.compose.ui.modifier.click
import com.solvibe.main.ext.showToast
import com.solvibe.main.utils.getString

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TarotFortuneBottomSheet(
    onDismiss: () -> Unit,
    viewModel: Live2DViewModel = viewModel()
) {
    var selectedTypeIndex by remember { mutableIntStateOf(0) }
    var inputTypeText by remember { mutableStateOf("") }
    var topic by remember { mutableStateOf("") }

    SVBottomSheet(
        onDismiss,
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
        containerColor = Color(0xFF19171F),
        showGestureBar = false
    ) {
        DTVerticalSpacer(16.dp)
        TarotFortuneTitle()
        DTVerticalSpacer(20.dp)
        CellTitle(R.string.type)
        DTVerticalSpacer(4.dp)
        TypeSelection(
            selectedTypeIndex,
            inputTypeText,
            viewModel.tarotFortuneTypes
        ) { index, type ->
            selectedTypeIndex = index
            if (type != null) {
                inputTypeText = type
            }
        }
        DTVerticalSpacer(16.dp)
        CellTitle(R.string.topic)
        DTVerticalSpacer(4.dp)
        TopicInput(
            topic,
            onInputTextChange = { topic = it },
            onSend = {
                val type = if (selectedTypeIndex == viewModel.tarotFortuneTypes.lastIndex) {
                    inputTypeText
                } else {
                    viewModel.tarotFortuneTypes[selectedTypeIndex]
                }
                if (type.isEmpty()) {
                    showToast(getString(R.string.tarot_fortune_type_empty))
                    return@TopicInput
                }
                if (topic.isEmpty()) {
                    showToast(getString(R.string.tarot_fortune_topic_empty))
                    return@TopicInput
                }
                viewModel.sendTarotFortuneMessage(type, topic)
                onDismiss()
            }
        )
        DTVerticalSpacer(12.dp)
    }
}

@Composable
fun TarotFortuneTitle() {
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painterResource(R.drawable.ic_character),
            null,
            Modifier.size(18.dp),
            tint = Color.Unspecified
        )
        DTHorizontalSpacer(10.dp)
        Text(
            buildAnnotatedString {
                append(stringResource(R.string.tarot_fortune))
                withStyle(
                    SpanStyle(
                        color = White60,
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Normal
                    )
                ) {
                    append(stringResource(R.string.tarot_fortune_sub_title))
                }
            },
            style = MaterialTheme.typography.headlineLarge.copy(
                color = Color.White,
                fontSize = 14.sp,
                lineHeight = 18.sp
            )
        )
    }
}

@Composable
fun CellTitle(@StringRes text: Int) {
    Text(
        stringResource(text),
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.bodySmall.copy(
            color = Color.White,
        )
    )
}

@Composable
fun TypeSelection(
    selectedIndex: Int,
    inputTypeText: String,
    types: List<String>,
    onSelected: (index: Int, type: String?) -> Unit
) {
    Column(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
    ) {
        Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            types.forEachIndexed { index, type ->
                TypeItem(index == selectedIndex, type) {
                    onSelected(index, null)
                }
            }
        }
        AnimatedVisibility(
            selectedIndex == types.lastIndex,
            Modifier.fillMaxWidth()
        ) {
            Column {
                DTVerticalSpacer(7.dp)
                TypeInput(inputTypeText) {
                    onSelected(types.lastIndex, it)
                }
            }
        }
    }
}

@Composable
fun TypeItem(selected: Boolean, type: String, onClick: () -> Unit) {
    Text(
        type,
        Modifier
            .size(64.dp, 24.dp)
            .clip(RoundedCornerShape(50))
            .clickable(onClick = onClick)
            .background(if (selected) Color(0xFFFF5476) else White10)
            .wrapContentHeight(align = Alignment.CenterVertically),
        style = MaterialTheme.typography.labelMedium.copy(
            color = White70,
            textAlign = TextAlign.Center
        )
    )
}

@Composable
fun TypeInput(inputText: String, onInputTextChange: (String) -> Unit) {
    DTTextFieldRowGroup(
        value = inputText,
        onValueChange = onInputTextChange,
        modifier = Modifier
            .fillMaxWidth()
            .height(46.dp)
            .background(Color(0x1AE8E8E8), RoundedCornerShape(12.dp)),
        singleLine = true,
        contentPadding = PaddingValues(12.dp),
        placeholder = {},
        endContent = {}
    )
}

@Composable
fun TopicInput(
    inputText: String,
    onInputTextChange: (String) -> Unit,
    onSend: () -> Unit,
) {
    DTTextFieldRowGroup(
        value = inputText,
        onValueChange = onInputTextChange,
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .heightIn(46.dp)
            .background(Color(0x1AE8E8E8), RoundedCornerShape(12.dp)),
        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
        keyboardActions = KeyboardActions(onSend = { onSend() }),
        maxLines = 5,
        contentPadding = PaddingValues(12.dp),
        placeholder = {
            Text(
                stringResource(R.string.chat_page_input_placeholder),
                Modifier.fillMaxWidth(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = DTTextFieldTextStyle.copy(color = White35),
            )
        },
        endContent = {
            DTHorizontalSpacer(2.dp)
            Icon(
                painterResource(R.drawable.ic_send_msg_sv),
                null,
                Modifier
                    .size(22.dp)
                    .click(onSend),
                tint = Color.Unspecified
            )
        }
    )
}