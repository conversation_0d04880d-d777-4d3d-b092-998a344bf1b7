package com.solvibe.main.compose.ui.page.live2d


import com.solvibe.character.net.client.newOkHttpSSEClient
import com.solvibe.main.BuildConfig
import com.solvibe.main.bean.AskResp
import com.solvibe.utils.base.BaseApplication
import com.solvibe.utils.ext.jsonAsOrNull
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.Request
import okhttp3.Response
import okhttp3.sse.EventSource
import okhttp3.sse.EventSourceListener
import okhttp3.sse.EventSources

class StreamMsgRepo {
    private val client = newOkHttpSSEClient(BaseApplication.getInstance())
    private val eventSources = mutableListOf<EventSource>()

    fun sendMsgStream(
        msg: String,
        sessionId: Long?,
        function: String?,
        onEvent: (SSEEvent) -> Unit
    ) {
        val httpUrl = "${BuildConfig.BaseUrlSolvibe}ask".toHttpUrlOrNull()
        if (httpUrl == null) {
            return onEvent(SSEEvent.Error(IllegalArgumentException("httpUrl is null")))
        }
        val httpBuilder = httpUrl.newBuilder()
            .addQueryParameter("content", msg)
            .addQueryParameter("time", "${System.currentTimeMillis() / 1000}")
            .addQueryParameter("type", "1")
            .apply {
                sessionId?.let {
                    addQueryParameter("session_id", it.toString())
                }
                function?.let {
                    addQueryParameter("function", it)
                }
            }
            .build()

        val request = Request.Builder()
            .url(httpBuilder)
            .build()

        val eventSource = EventSources.createFactory(client)
            .newEventSource(request, object : EventSourceListener() {
                override fun onOpen(eventSource: EventSource, response: Response) {
                    onEvent(SSEEvent.Open)
                }

                override fun onEvent(
                    eventSource: EventSource, id: String?, type: String?, data: String
                ) {
                    onEvent(SSEEvent.Message(id, type, data.jsonAsOrNull()))
                }

                override fun onClosed(eventSource: EventSource) {
                    onEvent(SSEEvent.Close)
                    eventSources.remove(eventSource)
                }

                override fun onFailure(
                    eventSource: EventSource, t: Throwable?, response: Response?
                ) {
                    onEvent(SSEEvent.Error(t ?: Exception("unknown error")))
                    eventSources.remove(eventSource)
                }
            })
        eventSources.add(eventSource)
    }

    fun cancelAll() {
        eventSources.forEach { it.cancel() }
        eventSources.clear()
    }
}

sealed interface SSEEvent {
    data object Open : SSEEvent
    data class Message(val id: String?, val type: String?, val msg: AskResp?) : SSEEvent
    data object Close : SSEEvent
    data class Error(val t: Throwable) : SSEEvent
}
