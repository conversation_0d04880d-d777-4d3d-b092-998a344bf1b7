package com.solvibe.main.db

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.solvibe.main.App
import com.solvibe.main.BuildConfig
import com.solvibe.main.db.converters.StringListConverter
import com.solvibe.main.db.table.AIRole
import com.solvibe.utils.ext.logv
import java.util.concurrent.Executors

@Database(
    entities = [AIRole::class],
    version = 1
)
@TypeConverters(StringListConverter::class)
abstract class AppDatabase : RoomDatabase() {
    companion object {
        val instance by lazy {
            Room.databaseBuilder(App.getInstance(), AppDatabase::class.java, "db_app")
                .apply {
                    if (BuildConfig.DEBUG) {
                        setJournalMode(JournalMode.TRUNCATE)
                        setQueryCallback(showSqlLog, Executors.newSingleThreadExecutor())
                    }
                }
                .build()
        }
    }
}

private val showSqlLog = { sql: String, args: List<Any?> ->
    val formattedArgs = args.map { arg -> "$arg" }
    val sqlParts = sql.split("\\?".toRegex())
    val replacedQuery = buildString {
        for (i in sqlParts.indices) {
            append(sqlParts[i].lowercase())
            if (i < formattedArgs.size) {
                append(formattedArgs[i])
            }
        }
    }
    logv(replacedQuery, "sql_logging", false)
}