package com.solvibe.main.db.update

import com.solvibe.main.db.table.AIRole

fun AIRole?.merge(new: AIRole): AIRole {
    this ?: return new
    return AIRole(
        id = new.id,
        name = if (new.name.isNotEmpty()) new.name else name,
        description = if (new.description.isNotEmpty()) new.description else description,
        images = if (new.images.isNotEmpty()) new.images else images,
        messageCount = if (new.messageCount > 0) new.messageCount else messageCount,
        voiceId = if (new.voiceId != -1) new.voiceId else voiceId,
        width = if (new.width > 0) new.width else width,
        height = if (new.height > 0) new.height else height,
        isHot = if (new.isHot != -1) new.isHot else isHot,
        createdAt = if (new.createdAt.isNotEmpty()) new.createdAt else createdAt,
        updatedAt = if (new.updatedAt.isNotEmpty()) new.updatedAt else updatedAt,
    )
}