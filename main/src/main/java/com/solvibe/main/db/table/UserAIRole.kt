package com.solvibe.main.db.table

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index

@Entity(
    tableName = "user_ai_role",
    primaryKeys = [
        "user_id", "role_id"
    ],
    indices = [
        Index(value = ["user_id", "role_id"], unique = true)
    ]
)
data class UserAIRole(
    @ColumnInfo(name = "user_id")
    val userId: String,
    @ColumnInfo("role_id")
    val roleId: Long,
    @ColumnInfo(name = "session_id")
    val sessionId: Long? = null,
    @ColumnInfo(name = "last_msg")
    val lastMsg: String? = null,
    @ColumnInfo(name = "last_message_at")
    val lastMsgAt: Long? = null,
    @ColumnInfo(name = "session_top_up_at")
    val sessionTopUpAt: Long? = null,
    @ColumnInfo(name = "is_favorite")
    val isFavorite: Boolean? = null,
)
