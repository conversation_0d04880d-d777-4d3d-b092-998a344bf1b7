package com.solvibe.main.db.update

import com.solvibe.main.db.table.MsgRecord

fun MsgRecord?.merge(new: MsgRecord): MsgRecord {
    this ?: return new
    return MsgRecord(
        msgId = new.msgId,
        roleId = new.roleId,
        speakerType = new.speakerType,
        type = new.type,
        content = new.content,
        createdAt = new.createdAt,
        userId = new.userId,
        isRead = isRead,
        replyId = new.replyId,
        isLock = new.isLock,
        reply = new.reply,
        voicePath = voicePath
    )
}