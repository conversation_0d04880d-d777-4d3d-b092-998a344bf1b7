package com.solvibe.main.db.table

import androidx.compose.runtime.Stable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.solvibe.utils.utils.toDatetimeFMT

@Entity(tableName = "msg_record")
@Stable
data class MsgRecord(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo("id")
    val id: Long = 0,
    @ColumnInfo("msg_id")
    val msgId: Long? = null,
    @ColumnInfo("role_id")
    val roleId: Long,
    @ColumnInfo("speaker_type")
    val speakerType: Int,
    @ColumnInfo("type")
    val type: Int,
    @ColumnInfo("content")
    val content: String,
    @ColumnInfo("created_at")
    val createdAt: Long,
    @ColumnInfo(name = "user_id")
    val userId: String,
    @ColumnInfo(name = "is_read")
    val isRead: <PERSON><PERSON><PERSON>,
    @ColumnInfo(name = "reply_id")
    val replyId: Long,
    @ColumnInfo(name = "is_lock")
    val isLock: Int,
    @ColumnInfo(name = "reply")
    val reply: String?,
    @ColumnInfo(name = "voice_path")
    val voicePath: String? = null,//语音文件存储路径
) {
    val fromSelf: Boolean
        get() = speakerType == 2

    val msgType: MsgType?
        get() = when (type) {
            1 -> MsgType.Text
            2 -> MsgType.Image
            3 -> MsgType.Video
            4 -> MsgType.Voice
            else -> null
        }

    val isTextMsg: Boolean
        get() = msgType == MsgType.Text

    val isLocked: Boolean
        get() = isLock == 1

    val createdDatetime: String
        get() = (createdAt * 1000).toDatetimeFMT()

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as MsgRecord

        if (msgId != other.msgId) return false
        if (roleId != other.roleId) return false
        if (speakerType != other.speakerType) return false
        if (type != other.type) return false
        if (createdAt != other.createdAt) return false
        if (isRead != other.isRead) return false
        if (replyId != other.replyId) return false
        if (isLock != other.isLock) return false
        if (content != other.content) return false
        if (userId != other.userId) return false
        if (reply != other.reply) return false
        if (voicePath != other.voicePath) return false

        return true
    }

    override fun hashCode(): Int {
        var result = msgId?.hashCode() ?: 0
        result = 31 * result + roleId.hashCode()
        result = 31 * result + speakerType
        result = 31 * result + type
        result = 31 * result + createdAt.hashCode()
        result = 31 * result + isRead.hashCode()
        result = 31 * result + replyId.hashCode()
        result = 31 * result + isLock
        result = 31 * result + content.hashCode()
        result = 31 * result + userId.hashCode()
        result = 31 * result + (reply?.hashCode() ?: 0)
        result = 31 * result + (voicePath?.hashCode() ?: 0)
        return result
    }
}

enum class MsgType {
    Text, Image, Video, Voice
}

fun newMyTextMsg(
    roleId: Long,
    text: String,
    userId: String,
    replyId: Long,
    reply: String?,
): MsgRecord {
    return MsgRecord(
        msgId = null,
        roleId = roleId,
        speakerType = 2,
        type = 1,
        content = text,
        createdAt = System.currentTimeMillis() / 1000,
        userId = userId,
        isRead = true,
        replyId = replyId,
        isLock = 0,
        reply = reply
    )
}