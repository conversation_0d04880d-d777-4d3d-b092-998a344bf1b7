package com.solvibe.main.db.table

import androidx.compose.runtime.Immutable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable

@Entity(tableName = "ai_role_tag")
@Serializable
@Immutable
data class AIRoleTag(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: Long,
    @ColumnInfo(name = "name")
    val name: String,
    @ColumnInfo(name = "status")
    val status: Int,
    @ColumnInfo(name = "created_at")
    val createdAt: String,
    @ColumnInfo(name = "updated_at")
    val updatedAt: String,
)

/**
 * 角色和标签相对应的中间表
 */
@Entity(tableName = "ai_role_tag_relation", primaryKeys = ["role_id", "tag_id"])
@Serializable
@Immutable
data class AIRoleTagRelation(
    @ColumnInfo(name = "role_id")
    val roleId: Long,
    @ColumnInfo(name = "tag_id")
    val tagId: Long,
)