package com.solvibe.main.db.update

import com.solvibe.main.db.table.UserAIRole

fun UserAIRole?.merge(new: UserAIRole): UserAIRole {
    this ?: return new
    return UserAIRole(
        userId = new.userId,
        roleId = new.roleId,
        sessionId = new.sessionId ?: sessionId,
        lastMsg = new.lastMsg ?: lastMsg,
        lastMsgAt = new.lastMsgAt ?: lastMsgAt,
        sessionTopUpAt = new.sessionTopUpAt ?: sessionTopUpAt,
        isFavorite = new.isFavorite ?: isFavorite
    )
}