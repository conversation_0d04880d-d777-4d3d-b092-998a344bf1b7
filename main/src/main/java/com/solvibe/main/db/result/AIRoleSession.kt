package com.solvibe.main.db.result

import androidx.compose.runtime.Stable
import androidx.room.ColumnInfo
import com.solvibe.utils.utils.toDatetimeFMT

@Stable
data class AIRoleSession(
    @ColumnInfo(name = "id")
    val id: Long,
    @ColumnInfo(name = "name")
    val name: String,
    @ColumnInfo(name = "description")
    val description: String,
    @ColumnInfo(name = "images")
    val images: List<String>,
    @ColumnInfo(name = "session_id")
    val sessionId: Long?,
    @ColumnInfo(name = "last_msg")
    val lastMsg: String?,
    @ColumnInfo(name = "last_message_at")
    val lastMsgAt: Long?,
    @ColumnInfo(name = "session_top_up_at")
    val sessionTopUpAt: Long,
    @ColumnInfo(name = "user_id")
    val userId: String,
    @ColumnInfo(name = "unread_count")
    val unreadCount: Int,
    @ColumnInfo(name = "is_favorite")
    val isFavorite: Boolean,
    @ColumnInfo(name = "voice_id")
    val voiceId: Int,
) {
    val avatar: String
        get() {
            return if (images.isEmpty()) "" else images[0]
        }

    val isTopUp: Boolean
        get() = sessionTopUpAt > 0L

    val lastMsgDatetime: String
        get() = if (lastMsgAt == null) "" else (lastMsgAt * 1000).toDatetimeFMT()
}