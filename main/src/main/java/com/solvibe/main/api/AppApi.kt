package com.solvibe.main.api

import com.mfccgroup.android.httpclient.adapter.API
import com.solvibe.character.net.Resp
import com.solvibe.character.net.Response
import com.solvibe.main.BuildConfig
import com.solvibe.main.bean.CharacterSettings
import com.solvibe.main.bean.InChineseMainland
import com.solvibe.main.bean.ListResp
import com.solvibe.main.bean.SVMessage
import com.solvibe.main.bean.SVSession
import com.solvibe.main.bean.TextModerationBean
import com.solvibe.main.bean.TouristsData
import com.solvibe.main.bean.VersionBean
import com.solvibe.main.state.LLMModel
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

const val LacUrl = BuildConfig.LacUrl
const val IpCheckUrl = BuildConfig.IpCheck
const val AppCheckUpdateUrl = BuildConfig.AppCheckUpdate

interface AppApi {
    /**
     * # 敏感词接口
     * ```kotlin
     * val labels = PromptUtils.checkSensitive(prompt)
     * if (labels.isNotEmpty() && labels == "regional") {
     *      cb.invoke(-436)
     *      return@launch
     * }
     * ```
     */
    @POST("${LacUrl}api/utils/text-moderation")
    @FormUrlEncoded
    suspend fun checkSensitive(@Field("content") prompt: String): Response<TextModerationBean>

    /**
     * ip检查
     */
    @GET("${IpCheckUrl}ip/ipInChineseMainland")
    suspend fun getIpInfo(): Response<InChineseMainland>

    /**
     * 提交用户的性别和年龄信息
     */
    @POST("member-info")
    @FormUrlEncoded
    suspend fun submitMemberInfo(
        @Field("name") name: String,
        @Field("email") email: String,
        @Field("age") age: Int,
        @Field("gender") gender: Int,
    ): Response<String>

    /**
     * 检查app版本更新
     */
    @GET("${AppCheckUpdateUrl}v2/verinfo")
    suspend fun getVersionInfo(
        @Query("pid") pid: String,
        @Query("lang") lang: String
    ): Resp<VersionBean>

    @GET("models")
    suspend fun llmModels(): API<ListResp<LLMModel>>

    @GET("sol-vibe/sessions")
    suspend fun sessions(
        @Query("page") page: Int,
        @Query("page_size") pageSize: Int,
    ): API<ListResp<SVSession>>

    @GET("sol-vibe/messages")
    suspend fun messages(
        @Query("session_id") sessionId: Long,
        @Query("last_id") lastMsgId: Long?,
        @Query("page_size") pageSize: Int,
    ): API<ListResp<SVMessage>>

    @POST("sol-vibe/delete-session")
    @FormUrlEncoded
    suspend fun deleteSVSession(@Field("session_id") sessionId: Long): API<Any>

    @GET("user/member-info")
    suspend fun getCharacterSettings(): API<CharacterSettings>

    @POST("user/set")
    @FormUrlEncoded
    suspend fun setCharacterSettings(
        @Field("type") type: String,
        @Field("value") value: Any,
    ): API<Any>

    @POST("reset-session")
    @FormUrlEncoded
    suspend fun resetSession(@Field("session_id") sessionId: Long): API<Any>

    @POST("user/tourists-login")
    suspend fun touristsLogin(): API<TouristsData>
}