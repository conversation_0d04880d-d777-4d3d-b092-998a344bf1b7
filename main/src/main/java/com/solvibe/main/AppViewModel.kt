package com.solvibe.main

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.solvibe.Membership
import com.solvibe.character.net.runHttp
import com.solvibe.character.net.ws.WSManager
import com.solvibe.main.api.appApiFastJson
import com.solvibe.main.ext.showToast
import com.solvibe.main.state.MessageState
import com.solvibe.main.state.SessionState
import com.solvibe.main.state.SettingsState
import com.solvibe.main.utils.getString
import com.solvibe.utils.ext.logv
import com.solvibe.utils.utils.NetworkConnectivityObserver
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

class AppViewModel(application: Application) : AndroidViewModel(application) {
    val sessionState = SessionState(viewModelScope)
    val messageState = MessageState(viewModelScope, sessionState)
    val settingsState = SettingsState(viewModelScope)

    private val networkObserver = NetworkConnectivityObserver(application)

    @OptIn(FlowPreview::class)
    fun init() {
        viewModelScope.launch {
            combine(
                Membership.userStateFlow,
                Membership.touristsTokenDataStateFlow
            ) { user, touristsTokenData ->
                user to touristsTokenData
            }.collectLatest { (user, touristsTokenData) ->
                logv("用户信息变化：user: $user, touristsTokenData: $touristsTokenData")
                resetStates()
                if (user == null && touristsTokenData == null) {
                    logv("用户信息变化：用户和游客Token都为空")
                    touristsLogin()
                    return@collectLatest
                }
                // 重新连接 websocket
                WSManager.open(BuildConfig.BaseUrlSolvibe, Membership.token)
                logv("刷新用户数据：user: ${user?.email}，touristsTokenData: $touristsTokenData")
                refreshAppData()
            }
        }
        viewModelScope.launch {
            sessionState.sessionIdFlow.collectLatest { sessionId ->
                logv("sessionId changed: $sessionId")
                messageState.refresh(sessionId)
            }
        }
        viewModelScope.launch {
            messageState.uiMsgFlow.collect { (_, askResp) ->
                sessionState.updateSessionId(askResp.sessionId)
            }
        }
        viewModelScope.launch {
            messageState.messagesFlow.debounce(200).collectLatest {
                sessionState.updateSession(it.firstOrNull())
            }
        }
        collectNetworkState()
    }

    private fun touristsLogin() {
        viewModelScope.launch {
            if (!Membership.isLogin()) {
                logv("获取游客Token")
                runHttp {
                    appApiFastJson.touristsLogin().ifSuccess { data ->
                        logv("获取游客Token成功: $data")
                        Membership.setTouristsTokenData(data)
                    }
                }
            }
        }
    }

    fun refreshAppData() {
        viewModelScope.launch {
            sessionState.refresh()
        }
        viewModelScope.launch {
            settingsState.initLLMModels()
            settingsState.initSettings()
        }
    }

    private fun collectNetworkState() {
        viewModelScope.launch {
            networkObserver.observe().collectLatest { isConnected ->
                if (!isConnected) {
                    showToast(getString(R.string.network_exception))
                }
            }
        }
    }

    fun loadMoreMsg() {
        messageState.loadMore(sessionState.sessionIdFlow.value ?: return)
    }

    fun ask(input: String) {
        messageState.ask(input, sessionState.sessionIdFlow.value)
    }

    fun continueReply() {
        messageState.continueReply(sessionState.sessionIdFlow.value)
    }

    fun retry() {
        messageState.retry(sessionState.sessionIdFlow.value)
    }

    fun mysteryPuzzle() {
        ask(getString(R.string.mystery_puzzle))
    }

    fun todayNews() {
        ask(getString(R.string.today_news))
    }

    fun newChat() {
        sessionState.updateSessionId(null)
        showToast(getString(R.string.new_chat_toast))
    }

    fun resetCurrentSession() {
        val sessionId = sessionState.sessionIdFlow.value ?: return
        viewModelScope.launch {
            messageState.resetBySessionId(sessionId)
            runHttp { appApiFastJson.resetSession(sessionId) }
        }
    }

    fun deleteSession(sessionId: Long?) {
        sessionId ?: return
        sessionState.deleteSession(sessionId)
        messageState.resetBySessionId(sessionId)
    }

    fun resetStates() {
        sessionState.reset()
        messageState.reset()
        settingsState.reset()
    }
}

interface ViewModelScopeOwner {
    val viewModelScope: CoroutineScope
}