package com.solvibe.main.viewmodel

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.solvibe.AccountStateEvent
import com.solvibe.Membership
import com.solvibe.character.net.error
import com.solvibe.character.net.success
import com.solvibe.main.api.appApi
import com.solvibe.main.bean.UserManager
import com.solvibe.main.bean.VersionBean
import com.solvibe.main.compose.utils.AppUtils
import com.solvibe.main.repo.AppRepo
import com.solvibe.utils.ext.loge
import kotlinx.coroutines.launch

class MainViewModel : ViewModel() {
    private val appRepo = AppRepo()

    init {
        handleLoginEvent()
    }

    private fun handleLoginEvent() {
        viewModelScope.launch {
            Membership.accountStateEvent.collect {
                when (it) {
                    AccountStateEvent.DeleteAccountState -> {}
                    AccountStateEvent.LoggedIn -> reportUserInfo()
                    AccountStateEvent.Logout -> {}
                }
            }
        }
    }

    /**
     * 获取新版本&what's new的信息
     */
    fun getNewVersionInfo(activity: Context, dealShow: (bean: VersionBean) -> Unit) {
        viewModelScope.launch {
            val version = appRepo.getVersionInfo()
            if (version == null || version.ver.isEmpty()) {
                return@launch
            }
            if (version.ver > AppUtils.getVersion(activity)) {
                // 服务器版本比当前版本大，显示更新弹窗
                dealShow(version)
            }
        }
    }

    /**
     * 登录成功后上报用户的信息
     */
    fun reportUserInfo() {
        if (!UserManager.hasReportUserInfo()) {
            viewModelScope.launch {
                appApi.submitMemberInfo(
                    name = UserManager.getName().orEmpty(),
                    email = Membership.getEmail().orEmpty(),
                    age = UserManager.getAge(),
                    gender = UserManager.getGender()
                ).success {
                    UserManager.setHasReportUserInfo()
                }.error { i, s ->
                    loge("上报用户信息失败: $i $s")
                }
            }
        }
    }
}