package com.solvibe.main.config

import coil3.imageLoader
import com.solvibe.main.App
import com.solvibe.main.ext.imageRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext

suspend fun List<String>.loadImages() = withContext(Dispatchers.IO) {
    map { url ->
        async {
            url.loadImage()
        }
    }.forEach { it.await() }
}

suspend fun String.loadImage() = withContext(Dispatchers.IO) {
    val context = App.getInstance()
    context.imageLoader
        .execute(imageRequest(context))
}