package com.solvibe.main.config

import android.util.Log
import com.solvibe.main.BuildConfig
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean

object TimberUtil {
    val releaseNeedLogRF = AtomicBoolean(false)
    private const val TAG = "TimberUtil"

    fun d(tag: String, msg: String) {
        if (BuildConfig.DEBUG) {
            //debug环境输出日志
            Timber.tag(tag).d(msg)
        } else if (releaseNeedLogRF.get()) {
            Log.d(tag, msg)
        }
    }
}