package com.solvibe.main.config

import android.content.Context
import com.alibaba.fastjson.JSONObject
import com.solvibe.main.App
import com.solvibe.main.utils.Utils
import com.solvibe.utils.ext.logd


/**
 *
 * @Description: java类作用描述
 * @Author: Lee<PERSON>iuuu
 * @CreateDate: 2022/7/26 13:46
 */
object Constant {
    //获取商店sku真实价格的正则
    const val PURCHASE_RENEWAL_PLAN_ID_REGEX = "^g\\d+.*"

    //获取商店sku虚拟价格的正则
    const val PURCHASE_VIRTUAL_PLAN_ID_REGEX = "^s\\d+.*"
    const val UM_KEY = "6839230879267e0210750200"
    const val INFORMATION_SOURCES = "100643"

    val language: String
        get() {
            return try {
                val preferences =
                    App.getInstance().getSharedPreferences("imyfone_track", Context.MODE_PRIVATE)
                val str = preferences.getString(
                    "referrer",
                    "{\"utm_source\":\"google-play\",\"utm_medium\":\"organic\"}"
                )
                val parseObject = JSONObject.parseObject(str)
                val language = parseObject.getString("lang") ?: "EN"
                language.ifEmpty {
                    "EN"
                }
            } catch (e: Exception) {
                "EN"
            }
        }

    val fromSite: String
        get() {
            return try {
                val preferences =
                    App.getInstance()
                        .getSharedPreferences("imyfone_track", Context.MODE_PRIVATE)
                val str = preferences.getString(
                    "referrer",
                    "{\"utm_source\":\"google-play\",\"utm_medium\":\"organic\"}"
                )
                logd("getCacheTrackInfo:$str", "缓存的渠道追踪")
                val parseObject = JSONObject.parseObject(str)
                val source = parseObject.getString("utm_source") ?: "google-play"
                if (source == "not set" || source.isEmpty()) {
                    "google-play"
                } else {
                    source
                }
            } catch (e: Exception) {
                "google-play"
            }
        }

    val webParams = "?pid=$INFORMATION_SOURCES&custom=$fromSite"

    // multi language
    var terms = Utils.getTerms()
    var eula = Utils.getEula()
    var privacy = Utils.getPrivacy()
    var contactSupport = Utils.getContactSupport()

    const val GoogleChannel = "google play"
    const val Top_Banner_clicks = "Top_Banner_clicks"
    const val Search_box = "Search_box"
    const val Purchase_pop_up = "Purchase_pop_up"
    const val Purchase_page = "Purchase_page"
}