package com.solvibe.main.config

/**
 *
 * @Description: java类作用描述
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/8/4 14:16
 */
object UmConstant {
    /**
     * 通用的内容
     */
    const val Product_Version =
        "Product_Version"  //某一产品版本的使用人数 备注：打开程序记录一次用户使用的产品版本（同一用户只记一次），按照不同的产品版本进行区分（eg._Win_V1.0.0）
    const val Device_system = "Device_system"  //不同移动设备系统的使用人数 备注：打开程序记录一次用户当前移动设备的系统版本（同一用户只记一次）
    const val Feedback = "Feedback"  //记录用户反馈次数 备注：记录用户反馈成功次数
    const val System_Language =
        "System_Language"  //记录用户当前设备语言 备注：打开程序时记录一次用户设备当前所使用的系统语言，按照不同语言进行区分（eg._English(Australia)、English(Belize）

    /**
     * 购买页
     */
    const val Purchase_page = "Purchase_page"

    /**
     *聊天数据
     */
    const val Chat_data = "Chat_data"

    /**
     * 记录用户快捷消息的点击次数
     */
    const val Quick_Message = "Quick_Message"

    /**
     * 聊天时网络环境不佳Toast提示次数
     */
    const val Poor_network = "Poor_network"

    /**
     * 违禁词Toast弹出次数
     */
    const val Sensitive_word = "Sensitive_word"
}