package com.solvibe.main.view

import android.app.Activity
import com.solvibe.main.databinding.DialogForbiddenBinding

class ForbiddenDialog(val activity: Activity) : CPBaseDialog<DialogForbiddenBinding>(activity) {
    override fun getViewBinding(): DialogForbiddenBinding {
        return DialogForbiddenBinding.inflate(layoutInflater)
    }

    override fun initView() {
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        vb.tvOk.setOnClickListener {
            activity.finish()
            android.os.Process.killProcess(android.os.Process.myPid())
        }
    }
}