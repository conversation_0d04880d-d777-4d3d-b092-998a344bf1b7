package com.solvibe.main

import androidx.annotation.OptIn
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStore
import androidx.media3.common.util.UnstableApi
import coil3.ImageLoader
import coil3.PlatformContext
import coil3.SingletonImageLoader
import com.google.firebase.FirebaseApp
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.imyfone.track.IMyfoneTrack
import com.solvibe.Membership
import com.solvibe.character.net.ws.WSManager
import com.solvibe.main.bean.UserManager
import com.solvibe.main.config.Constant
import com.solvibe.main.config.coilConfig
import com.solvibe.main.utils.StatisticsUtil
import com.solvibe.main.utils.TrackUtil
import com.solvibe.utils.base.BaseApplication
import com.tencent.mmkv.MMKV
import com.umeng.commonsdk.UMConfigure
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.koin.android.ext.koin.androidContext
import org.koin.core.context.startKoin
import timber.log.Timber
import timber.log.Timber.DebugTree
import java.io.File
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

/**
 *
 * @Description: java类作用描述
 * @Author: LeeQiuuu
 * @CreateDate: 2022/7/22 16:44
 */
open class App : BaseApplication(), SingletonImageLoader.Factory {
    private val umKey = "6839230879267e0210750200" //设置友盟key
    val appViewModel by lazy {
        ViewModelProvider(
            this,
            ViewModelProvider.AndroidViewModelFactory(this)
        )[AppViewModel::class.java]
    }

    companion object {
        private lateinit var mInstance: App
        val showLog = BaseApplication.showLog

        fun getInstance(): App {
            return mInstance
        }

        val viewModel by lazy {
            getInstance().appViewModel
        }

        val scope = BaseApplication.scope

        fun launch(
            context: CoroutineContext = EmptyCoroutineContext,
            start: CoroutineStart = CoroutineStart.DEFAULT,
            block: suspend CoroutineScope.() -> Unit
        ) {
            scope.launch(context, start, block)
        }

        fun getAppDir(subDir: String? = null): File? {
            return getInstance().getExternalFilesDir(subDir)?.apply {
                if (!exists()) {
                    mkdirs()
                }
            }
        }

        fun getAppFile(subDir: String? = null, fileName: String): File? {
            val parent = getAppDir(subDir) ?: return null
            return File(parent, fileName)
        }
    }

    @OptIn(UnstableApi::class)
    override fun onCreate() {
        super.onCreate()
        mInstance = this
        Membership.initClient()
        TrackUtil.initTrack(this)
        // MMKV init
        MMKV.initialize(this)
        StatisticsUtil.init(false, this)

        if (showLog) {
            Timber.plant(DebugTree())
        }
        startKoin {
            androidContext(applicationContext)
        }
        setFirebaseCrash(true)
        if (isMainProcess(this)) {
            FirebaseApp.initializeApp(this)
            if (UserManager.isAgreePrivacyPolicy()) {
                UMConfigure.init(
                    this,
                    umKey,
                    BuildConfig.channel,
                    UMConfigure.DEVICE_TYPE_PHONE,
                    ""
                )
            }
            MainScope().launch(Dispatchers.IO) {
                IMyfoneTrack.create(this@App)
                    .enableDebug(BuildConfig.DEBUG)//设置是否启动debug模式,生产环境要设置为false;默认是false
                    .setHost(BuildConfig.BaseUrl)//TODO 修改成站点域名 @see [http://yapi.deeptalkie.info/project/241/interface/api/8718]
                    .setProduct(Constant.INFORMATION_SOURCES)//TODO 修改成当前产品的pid 字段[pid]
                    .setTrackCallBack { _, msg ->  //设置上报deeptalkie的回调,正式环境可以不用调用 [code的说明::deeptalkieTrack.ResponseCode]
                        //...
                        Timber.e(msg)
                    }
                    .setGACallBack { _, msg ->   //设置上报Google play的回调,正式环境可以不用调用  [code的说明::deeptalkieTrack.ResponseCode]
                        //...
                        Timber.e(msg)
                    }.report()//发起上报
            }
        }
        UserManager.initLanguage(this)
        appViewModel.init()
    }

    fun setFirebaseCrash(enable: Boolean) {
        Firebase.crashlytics.isCrashlyticsCollectionEnabled = enable
    }

    override fun dealLogout() {
        // 关闭 websocket
        WSManager.close()
        Membership.logout()
    }

    override fun newImageLoader(context: PlatformContext): ImageLoader {
        return coilConfig(context)
    }

    override fun getToken(): String {
        return Membership.token
    }

    override fun deviceId(): String {
        return UserManager.getDeviceId()
    }

    override fun language(): String {
        return UserManager.getLanguage().backendId
    }

    private val appViewModelStore by lazy {
        ViewModelStore()
    }

    override val viewModelStore: ViewModelStore
        get() = appViewModelStore
}