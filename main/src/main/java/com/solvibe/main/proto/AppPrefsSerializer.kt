package com.solvibe.main.proto

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.core.Serializer
import androidx.datastore.dataStore
import java.io.InputStream
import java.io.OutputStream

object AppPrefsSerializer : Serializer<AppPrefs> {
    override val defaultValue: AppPrefs = AppPrefs.getDefaultInstance()

    override suspend fun readFrom(input: InputStream): AppPrefs {
        return AppPrefs.parseFrom(input)
    }

    override suspend fun writeTo(t: AppPrefs, output: OutputStream) {
        t.writeTo(output)
    }
}

val Context.appDataStore: DataStore<AppPrefs> by dataStore(
    fileName = "app_prefs.pb",
    serializer = AppPrefsSerializer
)

fun AppPrefs?.update(
    block: AppPrefs.Builder.() -> Unit
): AppPrefs {
    return this?.toBuilder()?.apply(block)?.build() ?: AppPrefs.newBuilder().apply(block).build()
}

fun AppPrefs?.updateAppSettings(
    block: AppSettings.Builder.() -> Unit
): AppPrefs {
    return this.update {
        setAppSettings(
            appSettings.update(block)
        )
    }
}

fun AppPrefs?.updateGuestVipState(
    block: GuestVipState.Builder.() -> Unit
): AppPrefs {
    return this.update {
        setGuestVipState(
            guestVipState.update(block)
        )
    }
}

fun AppPrefs?.updateMemberVipState(
    block: MemberVipState.Builder.() -> Unit
): AppPrefs {
    return this.update {
        setMemberVipState(
            memberVipState.update(block)
        )
    }
}

fun AppSettings?.update(
    block: AppSettings.Builder.() -> Unit
): AppSettings {
    return this?.toBuilder()?.apply(block)?.build() ?: AppSettings.newBuilder().apply(block).build()
}

fun GuestVipState?.update(
    block: GuestVipState.Builder.() -> Unit
): GuestVipState {
    return this?.toBuilder()?.apply(block)?.build() ?: GuestVipState.newBuilder().apply(block)
        .build()
}

fun MemberVipState?.update(
    block: MemberVipState.Builder.() -> Unit
): MemberVipState {
    return this?.toBuilder()?.apply(block)?.build() ?: MemberVipState.newBuilder().apply(block)
        .build()
}