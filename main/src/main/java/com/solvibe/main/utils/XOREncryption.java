package com.solvibe.main.utils;

import android.text.TextUtils;
import android.util.Base64;

/**
 * @Description: java类作用描述
 * @Author: LeeQiuuu
 * @CreateDate: 2022/7/18 16:21
 */
public class XOREncryption {
    private final String mKey;

    public XOREncryption(String key) {
        mKey = EncryptUtils.INSTANCE.md5(key);
        if (TextUtils.isEmpty(mKey)) {
            throw new IllegalArgumentException("key is empty...");
        }
    }

    public String strEncrypt(final String strSource) {
        int i, j;
        StringBuilder sb = new StringBuilder();
        for (i = 0; i < strSource.length(); i++) {
            j = i % mKey.length();
            sb.append((char) (strSource.charAt(i) ^ mKey.charAt(j)));
        }
        return Base64.encodeToString(sb.toString().getBytes(), Base64.DEFAULT);
    }

    public byte[] strEncrypt1(final String strSource) {
        int i, j;
        byte[] data = strSource.getBytes();
        byte[] key = mKey.getBytes();
        byte[] result = new byte[strSource.length()];
        for (i = 0, j = 0; i < strSource.length(); i++) {
            j = i % mKey.length();
            result[i] = (byte) (data[i] ^ key[j]);
        }
        return Base64.encode(result, Base64.DEFAULT);
    }

    // 再次进行异或运算就可以解密
    public String strDecrypt(final String strSource) {
        return strEncrypt(strSource);
    }
}
