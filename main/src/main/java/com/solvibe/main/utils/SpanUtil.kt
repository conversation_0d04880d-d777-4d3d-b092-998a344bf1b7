package com.solvibe.main.utils

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import com.solvibe.main.config.TimberUtil

object SpanUtil {
    private const val TAG = "SpanUtil"
    fun getSpanText(totalText: String, color :Int,scale : Float,vararg strs: String): SpannableStringBuilder {
        val spannable = SpannableStringBuilder(totalText)
        for (str in strs) {
            val colorSpan = ForegroundColorSpan(color) // 改变颜色
            val sizeSpan = RelativeSizeSpan(scale)
            val startIndex = totalText.indexOf(str)
            val endIndex = str.length + startIndex
            TimberUtil.d(TAG,"startIndex = $startIndex,endIndex = $endIndex")
            spannable.setSpan(colorSpan, startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            spannable.setSpan(sizeSpan,startIndex,endIndex,Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }

        return spannable
    }
}
