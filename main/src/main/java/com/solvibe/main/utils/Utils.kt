package com.solvibe.main.utils

import android.content.Context
import android.content.Intent
import android.location.LocationManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.annotation.RequiresApi
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.ktx.Firebase
import com.solvibe.main.config.Constant
import com.solvibe.utils.utils.TimeUtils
import timber.log.Timber
import java.util.Locale

/**
 *
 * @Description: java类作用描述
 * @Author: LeeQiuuu
 * @CreateDate: 2022/8/2 15:58
 */
object Utils {
    /**
     * 判断GPS是否开启，GPS或者AGPS开启一个就认为是开启的
     * @param context
     * @return true 表示开启
     */
    fun isGpsOpened(context: Context): Boolean {
        val locationManager: LocationManager =
            context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        // 通过GPS卫星定位，定位级别可以精确到街（通过24颗卫星定位，在室外和空旷的地方定位准确、速度快）
        val gps: Boolean = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
        // 通过WLAN或移动网络(3G/2G)确定的位置（也称作AGPS，辅助GPS定位。主要用于在室内或遮盖物（建筑群或茂密的深林等）密集的地方定位）
        val network: Boolean = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
        return (gps || network) && result(context)
    }

    private fun checkEnableGPS(context: Context): Boolean {
        val provider: String = Settings.Secure.getString(
            context.contentResolver,
            Settings.Secure.LOCATION_PROVIDERS_ALLOWED
        ) ?: return true
        Timber.tag("provider").d("checkEnableGPS: $provider")
        return provider != ""
    }

    @RequiresApi(Build.VERSION_CODES.P)
    private fun checkEnable(context: Context): Boolean {
        val locationManager: LocationManager =
            context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        return locationManager.isLocationEnabled
    }

    private fun result(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            checkEnable(context)
        } else {
            Timber.tag("数据").d("result: ${checkEnableGPS(context)}")
            checkEnableGPS(context)
        }
    }

    /**
     * 获取手机本地语言
     */
    fun getLocaleLanguage(): String = Locale.getDefault().language

    /*
    val CN:String=Locale.SIMPLIFIED_CHINESE.language  //简体中文
    val IT:String=Locale.ITALIAN.language   //意大利
    val NL:String=Locale.forLanguageTag("nl").toLanguageTag()   //荷兰
    val MS:String=Locale.forLanguageTag("ms").toLanguageTag()   //马来
    val SV:String=Locale.forLanguageTag("sv").toLanguageTag()   //瑞典
    val ID:*/
    fun getTerms(): String {
        return "https://sites.google.com/view/solvibeai-terms-of-service/%E9%A6%96%E9%A1%B5"
    }

    fun getEula(): String {
        return "https://www.deeptalkie.com/company/license-agreement/"
    }

    fun getPrivacy(): String {
        return "https://sites.google.com/view/solvibeai-privacy-policy/%E9%A6%96%E9%A1%B5"
    }

    fun getContactSupport(): String {
        return ""
    }

    /**
     *@decription:支付成功后，需要使用firebase进行数据埋点。区分订单的来源网站  具体参数解析：https://firebase.google.com/docs/reference/android/com/google/firebase/analytics/FirebaseAnalytics.Param#CURRENCY
     *@param:AFFILIATION：渠道来源
     * @param:COUPON: 优惠卷
     * @param:CURRENCY:货币
     * @param:END_DATE:过期的日期  格式：“2015-09-14”
     * @param:ITEM_ID:sku id
     * @param:ITEM_NAME:sku 名称
     * @param:SHIPPING：运费
     * @param:START_DATE：购买日期
     * @param:TAX：收税成本
     * @param:TRANSACTION_ID:事务的唯一标识符 -》这里使用订单号
     * @param:VALUE  支付的金额
     *@return:null
     *@author:linjinhao
     *@time:2023/1/9 11:23
     */
    fun recordOrderSource(orderBean: com.solvibe.main.bean.ConfirmOrderBean, sku_id: String) {
        val startDate = TimeUtils.long2Str(System.currentTimeMillis(), TimeUtils.YMD, "")
        Firebase.analytics.logEvent(FirebaseAnalytics.Event.PURCHASE) {
            param(FirebaseAnalytics.Param.AFFILIATION, Constant.fromSite)
            param(FirebaseAnalytics.Param.COUPON, "")
            param(FirebaseAnalytics.Param.CURRENCY, orderBean.currency_code)
            param(FirebaseAnalytics.Param.END_DATE, "")
            param(FirebaseAnalytics.Param.ITEM_ID, sku_id)
            param(FirebaseAnalytics.Param.ITEM_NAME, orderBean.sku_name)
            param(FirebaseAnalytics.Param.SHIPPING, "")
            param(FirebaseAnalytics.Param.START_DATE, startDate)
            param(FirebaseAnalytics.Param.TAX, "")
            param(FirebaseAnalytics.Param.TRANSACTION_ID, orderBean.order_no)
            param(FirebaseAnalytics.Param.VALUE, orderBean.order_price.toDouble())
        }
    }

    // 根据网址跳转第三方浏览器
    fun jumpUriToBrowser(context: Context, url: String) {
        var url = url
        if (url.startsWith("www.")) url = "http://$url"
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            return
        }
        val intent = Intent()
        // 设置意图动作为打开浏览器
        intent.setAction(Intent.ACTION_VIEW)
        // 声明一个Uri
        val uri: Uri = Uri.parse(url)
        intent.data = uri
        context.startActivity(intent)
    }
}