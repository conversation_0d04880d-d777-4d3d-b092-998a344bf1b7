package com.solvibe.main.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.os.Build
import androidx.annotation.RequiresApi
import com.solvibe.main.App


/**
 * 网络监听管理器
 */
class NetWorkManager private constructor() {
    private var netType = NetType.WAP

    companion object {
        val instance: NetWorkManager by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) { NetWorkManager() }
        fun isNetworkConnectedSystem(): Boolean {
            val cm =
                App.getInstance().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            return when (cm.activeNetworkInfo?.type) {
                ConnectivityManager.TYPE_MOBILE, ConnectivityManager.TYPE_WIFI -> true
                else -> {
                    instance.netType = NetType.OFF
                    false
                }
            }
        }

        fun isWifiConnectedSystem(context: Context): Boolean {
            val cm = context
                .getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            return cm.activeNetworkInfo != null && cm.activeNetworkInfo!!.type == ConnectivityManager.TYPE_WIFI
        }
    }

    init {
        initNetWork()
    }

    private fun initNetWork() {
        netType = if (isWifiConnectedSystem(App.getInstance())) NetType.WIFI else NetType.WAP
    }

    fun onChange() {
        val networkInfo =
            (App.getInstance().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager).activeNetworkInfo
        if (networkInfo?.isAvailable == true) {
            when (networkInfo.type) {
                ConnectivityManager.TYPE_MOBILE -> {
                    NetType.WAP
                }
                ConnectivityManager.TYPE_WIFI -> {
                    NetType.WIFI
                }
                ConnectivityManager.TYPE_ETHERNET -> {
                    NetType.WIFI
                }
            }
        } else {
            //说明当前没有网络
            NetType.OFF
        }
    }

    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    fun onChange(network: Network?, networkCapabilities: NetworkCapabilities?) {
        if (networkCapabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED) == true) {
            netType = when {
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
//                    LogUtils.b("onCapabilitiesChanged: 网络类型为wifi")
                    NetType.WIFI
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                    NetType.WAP
                }
                else -> {
                    NetType.OFF
                }
            }
        }
    }

    fun isNetworkConnected() = netType != NetType.OFF
    fun isWifiConnected() = netType == NetType.WIFI
    fun isWifiState() =
        if (isWifiConnected()) "1" else "0"


}

/**
 * 网络类型
 */
enum class NetType {
    OFF, WAP, WIFI
}