package com.solvibe.main.utils

import com.solvibe.main.api.appApi
import com.solvibe.utils.ext.logv

class PromptUtils {
    companion object {
        suspend fun checkSensitive(prompt: String): String {
            val response = appApi.checkSensitive(prompt)
            if (response.code == 200) {
                logv("textModerationBean = ${response.data}", "sensitive_logging")
                return response.data?.labels ?: ""
            } else {
                return "提示词检测请求失败"
            }
        }
    }
}
