package com.solvibe.main.utils

import android.content.Context
import android.text.TextPaint
import android.text.style.ClickableSpan
import androidx.core.content.ContextCompat
import com.solvibe.main.R

abstract class GuideClickableSpan(
    private val context: Context,
    private val textColorResId: Int = R.color.colorPrimary
) : ClickableSpan() {
    override fun updateDrawState(ds: TextPaint) {
        super.updateDrawState(ds)
        ds.isUnderlineText = true
        ds.color = ContextCompat.getColor(context, textColorResId)
    }

}