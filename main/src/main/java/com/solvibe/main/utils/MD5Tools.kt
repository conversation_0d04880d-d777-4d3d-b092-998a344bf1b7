package com.solvibe.main.utils

import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

object MD5Tools {
    @Throws(NoSuchAlgorithmException::class)
    fun getEncryptDigest(paramArrayOfByte: ByteArray, algorithm: String = "MD5"): String {
        return toHexString(getEncryptBytes(paramArrayOfByte, algorithm))
    }

    fun getEncryptBytes(rawData: ByteArray, algorithm: String = "MD5"): ByteArray {
        val localMessageDigest = MessageDigest.getInstance(algorithm)
        localMessageDigest.update(rawData)
        return localMessageDigest.digest()
    }

    private fun toHexString(paramArrayOfByte: ByteArray): String {
        val localStringBuilder = StringBuilder(2 * paramArrayOfByte.size)
        var i = 0
        while (true) {
            if (i >= paramArrayOfByte.size) {
                return localStringBuilder.toString()
            }
            var str = (0xFF and paramArrayOfByte[i].toInt()).toString(16)
            if (str.length == 1) {
                str = "0$str"
            }
            localStringBuilder.append(str)
            i++
        }
    }

}