package com.solvibe.main.utils

import android.content.Context
import android.util.Log
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.ktx.Firebase
import com.solvibe.main.BuildConfig
import com.solvibe.main.config.Constant
import com.umeng.analytics.MobclickAgent
import com.umeng.commonsdk.UMConfigure

object StatisticsUtil {
    /**
     * 初始化
     * @param isSurePrivacy 是否确认过隐私
     */
    fun init(isSurePrivacy: Boolean, context: Context) {
        UMConfigure.preInit(
            context,
            Constant.UM_KEY,
            BuildConfig.channel
        )
        UMConfigure.setLogEnabled(BuildConfig.DEBUG)
        if (isSurePrivacy) {
            UMConfigure.init(
                context,
                Constant.UM_KEY,
                BuildConfig.channel,
                UMConfigure.DEVICE_TYPE_PHONE,
                ""
            )

        }
    }

    fun onEvent(context: Context, event: String, map: Map<String, String>? = null) {
        Log.v("umeng", "添加事件:$event map:$map")
        if (map == null) {
            MobclickAgent.onEvent(context, event)
            Firebase.analytics.logEvent(event) {
                param("Label", event)
            }
        } else {
            MobclickAgent.onEventObject(context, event, map)
            Firebase.analytics.logEvent(event) {
                for (entry in map.entries) {
                    param("Label", entry.value)
                    param(entry.key, entry.value)
                    Log.d("StatisticsUtil", "onEvent: value = ${entry.value}")
                }
            }
        }
    }

}
