package com.solvibe.main.utils

import android.content.Context
import com.solvibe.main.BuildConfig
import com.solvibe.main.config.Constant
import com.solvibe.main.config.TimberUtil
import com.imyfone.track.IMyfoneTrack
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

object TrackUtil {
    private const val TAG = "TrackUtil"
    private val scope by lazy {
        MainScope()
    }

    /**
     * 初始化渠道信息
     */
    fun initTrack(context: Context) {
        val host = BuildConfig.BaseUrl
        scope.launch(Dispatchers.IO) {
            try {
                IMyfoneTrack.create(context)
                    .enableDebug(BuildConfig.DEBUG)//设置是否启动debug模式,生产环境要设置为false;默认是false
                    .setHost(host)
                    .setProduct(Constant.INFORMATION_SOURCES)//TODO 修改成当前产品的pid 字段[pid]
                    .setTrackCallBack { _, msg ->  //设置上报IMyfone的回调,正式环境可以不用调用 [code的说明::IMyfoneTrack.ResponseCode]
                        //...
                        TimberUtil.d(TAG, "msg = $msg")
                    }
                    .setGACallBack { _, msg ->   //设置上报Google play的回调,正式环境可以不用调用  [code的说明::IMyfoneTrack.ResponseCode]
                        //...
                        TimberUtil.d(TAG, "msg = $msg")
                    }.report()//发起上报
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}