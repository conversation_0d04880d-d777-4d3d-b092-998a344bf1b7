package com.solvibe.main.utils

import android.content.Context
import com.umeng.analytics.MobclickAgent

/**
 *
 * @Description: java类作用描述
 * @Author: <PERSON><PERSON><PERSON>uu
 * @CreateDate: 2022/5/12 18:37
 */
object UMUtil {

    fun onEvent(context: Context,event:String){
        MobclickAgent.onEvent(context,event)
    }

    fun onEvent(context: Context,event:String,map:Map<String,String>){
        MobclickAgent.onEvent(context,event,map)
    }

}