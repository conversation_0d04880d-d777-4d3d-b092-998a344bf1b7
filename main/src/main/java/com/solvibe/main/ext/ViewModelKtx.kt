package com.solvibe.main.ext

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.solvibe.main.App
import com.solvibe.main.ViewModelScopeOwner
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.runBlocking

context(ViewModel)
fun <T> Flow<T>.stateInViewModelDefault(
    defaultValue: T,
    started: SharingStarted = SharingStarted.WhileSubscribed(5000)
): StateFlow<T> {
    return stateInViewModel(
        initialValue = runBlocking { firstOrNull() ?: defaultValue },
        started = started
    )
}

context(ViewModel)
fun <T> Flow<T>.stateInViewModel(
    started: SharingStarted = SharingStarted.WhileSubscribed(5000)
): StateFlow<T?> {
    return stateInViewModel(initialValue = runBlocking { firstOrNull() }, started = started)
}

context(ViewModel)
fun <T> Flow<T>.stateInViewModel(
    initialValue: T,
    started: SharingStarted = SharingStarted.WhileSubscribed(5000)
): StateFlow<T> {
    return stateIn(
        viewModelScope,
        started = started,
        initialValue = initialValue
    )
}

context(ViewModelScopeOwner)
fun <T> Flow<T>.stateInViewModelDefault(
    defaultValue: T,
    started: SharingStarted = SharingStarted.WhileSubscribed(5000)
): StateFlow<T> {
    return stateInViewModel(
        initialValue = runBlocking { firstOrNull() ?: defaultValue },
        started = started
    )
}

context(ViewModelScopeOwner)
fun <T> Flow<T>.stateInViewModel(
    initialValue: T,
    started: SharingStarted = SharingStarted.WhileSubscribed(5000)
): StateFlow<T> {
    return stateIn(
        viewModelScope,
        started = started,
        initialValue = initialValue
    )
}

fun <T> Flow<T>.stateInAppDefault(
    defaultValue: T,
    started: SharingStarted = SharingStarted.WhileSubscribed(5000)
): StateFlow<T> {
    return stateInApp(
        initialValue = runBlocking { firstOrNull() ?: defaultValue },
        started = started
    )
}

fun <T> Flow<T>.stateInApp(
    started: SharingStarted = SharingStarted.WhileSubscribed(5000)
): StateFlow<T?> {
    return stateInApp(initialValue = runBlocking { firstOrNull() }, started = started)
}

fun <T> Flow<T>.stateInApp(
    initialValue: T,
    started: SharingStarted = SharingStarted.WhileSubscribed(5000)
): StateFlow<T> {
    return stateIn(
        App.scope,
        started = started,
        initialValue = initialValue
    )
}

class ViewModelFactory(
    private val createViewModel: () -> ViewModel
) : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return createViewModel() as T
    }
}

fun viewModelFactory(createViewModel: () -> ViewModel) = ViewModelFactory(createViewModel)