package com.solvibe.main.ext

import android.content.Context
import coil3.request.ImageRequest
import coil3.request.SuccessResult
import com.solvibe.main.config.loadImage
import com.solvibe.utils.ext.logv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.URI

fun String.toNoQueryURI(): String {
    return try {
        val uri = URI(this)
        val key = URI(uri.scheme, uri.authority, uri.path, null, uri.fragment).toString()
        logv("uri = $this, key = $key", "uri_logging")
        key
    } catch (_: Exception) {
        this
    }
}

fun String.imageRequest(
    context: Context,
    block: ImageRequest.Builder.() -> Unit = {}
): ImageRequest {
    val key = toNoQueryURI()
    return ImageRequest.Builder(context)
        .data(this)
        .diskCacheKey(key)
        .memoryCacheKey(key)
        .apply(block)
        .build()
}

fun String.uriEquals(other: String): <PERSON><PERSON><PERSON> {
    return try {
        toNoQueryURI() == other.toNoQueryURI()
    } catch (_: Exception) {
        false
    }
}

suspend fun String.getVideoSize(): Pair<Int, Int> {
    return withContext(Dispatchers.IO) {
        val start = System.currentTimeMillis()
        try {
            var width = 1
            var height = 1

            val res = loadImage()
            if (res is SuccessResult) {
                width = res.image.width
                height = res.image.height
            }

            logv("获取宽高：url=${this@getVideoSize}, w=$width, h=$height, time=${System.currentTimeMillis() - start}")

            width to height
        } catch (e: Exception) {
            e.printStackTrace()
            1 to 1
        }
    }
}