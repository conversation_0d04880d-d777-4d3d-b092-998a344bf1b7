package com.solvibe.main.activity

import android.annotation.SuppressLint
import android.app.ComponentCaller
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.http.SslError
import android.os.Bundle
import android.view.View
import android.webkit.CookieManager
import android.webkit.JavascriptInterface
import android.webkit.SslErrorHandler
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebStorage
import android.webkit.WebView
import android.webkit.WebViewClient
import com.solvibe.Membership
import com.solvibe.main.databinding.ActivityWebBinding
import com.solvibe.utils.ext.configSystemBar
import timber.log.Timber

/**
 *
 * @Description: java类作用描述
 * @Author: LeeQiuuu
 * @CreateDate: 2022/5/23 10:48
 */
private const val EXTRA_PURCHASE_URL = "url"
private const val EXTRA_BROWSER_TYPE = "EXTRA_BROWSER_TYPE"
private const val EXTRA_PURCHASE = "EXTRA_PURCHASE"
private const val EXTRA_BROWSER = "EXTRA_BROWSER"

class WebActivity : BasicActivity<ActivityWebBinding>() {
    private var url: String = ""
    private var flag: String = ""

    companion object {
        private const val TAG = "WebActivity"

        fun startBrowser(context: Context, url: String) {
            val intent = Intent(context, WebActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.putExtra(EXTRA_PURCHASE_URL, url)
            intent.putExtra(EXTRA_BROWSER_TYPE, EXTRA_BROWSER)
            context.startActivity(intent)
        }

        fun purchase(context: Context, url: String) {
            val intent = Intent(context, WebActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.putExtra(EXTRA_PURCHASE_URL, url)
            intent.putExtra(EXTRA_BROWSER_TYPE, EXTRA_PURCHASE)
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        configSystemBar {
            statusBarBlackFont = true
            gestureNavigationTransparent = true
        }
    }

    override fun getViewBinding(): ActivityWebBinding {
        return ActivityWebBinding.inflate(layoutInflater)
    }

    override fun initView() {
        initWebView()
        //清理缓存
        clearWebCache()
        url = intent.getStringExtra(EXTRA_PURCHASE_URL) ?: ""
        flag = intent.getStringExtra(EXTRA_BROWSER_TYPE) ?: ""
        Timber.tag(TAG).d("initView: url = $url")
        mBinding.webView.loadUrl(url ?: "")
        mBinding.ivBack.setOnClickListener {
            finish()
        }
    }

    override fun onNewIntent(intent: Intent, caller: ComponentCaller) {
        super.onNewIntent(intent, caller)
        url = intent.getStringExtra(EXTRA_PURCHASE_URL) ?: return
        flag = intent.getStringExtra(EXTRA_BROWSER_TYPE) ?: return
    }


    private fun clearWebCache() {
        WebStorage.getInstance().deleteAllData()
        val cookieManager = CookieManager.getInstance()
        cookieManager.setAcceptCookie(true)
        cookieManager.removeSessionCookie()
        cookieManager.removeAllCookie()
        cookieManager.removeSessionCookies(null)
        cookieManager.removeAllCookie()
        cookieManager.flush();
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initWebView() {
        val settings: WebSettings = mBinding.webView.settings
        settings.javaScriptEnabled = true
        settings.javaScriptCanOpenWindowsAutomatically = true
        settings.domStorageEnabled = true;//开启本地缓存，适应免责声明中的web
        settings.cacheMode = WebSettings.LOAD_NO_CACHE
        mBinding.webView.clearCache(true)
        mBinding.webView.webViewClient = object : WebViewClient() {
            override fun doUpdateVisitedHistory(view: WebView?, url: String?, isReload: Boolean) {
                super.doUpdateVisitedHistory(view, url, isReload)
            }

            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
            }

            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                return super.shouldOverrideUrlLoading(view, request)
            }

            override fun onReceivedSslError(
                view: WebView?,
                handler: SslErrorHandler?,
                error: SslError?
            ) {
                //handler?.proceed()
                handler?.cancel()
            }
        }
        mBinding.webView.addJavascriptInterface(JSKit(), "app")
        mBinding.webView.webChromeClient = WebChromeClient()
        settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        mBinding.webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                mBinding.progressBar.progress = newProgress
                if (newProgress == 100) mBinding.progressBar.visibility = View.GONE
                super.onProgressChanged(view, newProgress)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        mBinding.webView.onPause()
        mBinding.webView.destroy()
        if (flag == EXTRA_PURCHASE) {
            Membership.completeICartPurchase("")
        }
    }

    inner class JSKit {
        @JavascriptInterface
        fun purchaseFinish(base64Email: String) {
            val originEmail = String(
                android.util.Base64.decode(
                    base64Email.toByteArray(),
                    android.util.Base64.DEFAULT
                )
            )
            Membership.completeICartPurchase(originEmail)
        }

        @JavascriptInterface
        fun purchaseClose() {
            <EMAIL>()
        }
    }

}