package com.solvibe.main.activity

import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.BackHandler
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.net.toUri
import androidx.lifecycle.lifecycleScope
import androidx.navigation.compose.rememberNavController
import com.solvibe.character.net.successFun
import com.solvibe.main.R
import com.solvibe.main.api.appApi
import com.solvibe.main.bean.VersionBean
import com.solvibe.main.compose.navigation.MainNavigation
import com.solvibe.main.compose.ui.page.about.UpdateDialog
import com.solvibe.main.ext.showToast
import com.solvibe.main.utils.NetWorkManager
import com.solvibe.main.view.ForbiddenDialog
import com.solvibe.main.viewmodel.MainViewModel
import com.solvibe.utils.ext.loge
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class MainActivity : BaseComposeActivity() {
    private var exitApp = false

    private val forbiddenDialog by lazy {
        ForbiddenDialog(this@MainActivity)
    }

    private val mainVm by viewModels<MainViewModel>()

    private fun checkUpdate() {
        if (!NetWorkManager.isNetworkConnectedSystem()) return
        mainVm.getNewVersionInfo(this) { version ->
            versionBean = version
        }
    }

    private fun checkIpInfo() {
        lifecycleScope.launch {
            try {
                appApi.getIpInfo()
                    .successFun { inChineseMainland ->
                        if (inChineseMainland?.inChineseMainland == true) {
                            // 显示toast提示应用不可用
                            forbiddenDialog.show()
                        }
                    }
            } catch (e: Exception) {
                loge("getIpInfo error: ${e.stackTraceToString()}")
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        checkUpdate()
    }

    private var versionBean by mutableStateOf<VersionBean?>(null)

    @Composable
    override fun ComposeContent() {
        val navController = rememberNavController()

        BackHandler {
            if (navController.previousBackStackEntry != null) {
                navController.popBackStack()
                return@BackHandler
            }
            if (exitApp) {
                finish()
            } else {
                exitApp = true
                showToast(getString(R.string.click_again_exit))
                lifecycleScope.launch {
                    delay(2000)
                    exitApp = false
                }
            }
        }
        MainNavigation()

        versionBean?.let {
            UpdateDialog(
                versionBean = it,
                onUpdate = {
                    try {
                        val uri = ("market://details?id=$packageName").toUri()
                        val intent = Intent(Intent.ACTION_VIEW, uri)
                        intent.setPackage("com.android.vending")
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        startActivity(intent)
                        versionBean = null
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                },
                onDismiss = {
                    versionBean = null
                }
            )
        }
    }


    override fun onResume() {
        super.onResume()
        checkIpInfo()
    }

    override fun onDestroy() {
        super.onDestroy()
        forbiddenDialog.dismiss()
    }
}