package com.solvibe.main.activity

import android.content.Context
import android.os.Bundle
import android.view.MotionEvent
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import androidx.viewbinding.ViewBinding
import com.solvibe.main.R
import com.solvibe.main.bean.UserManager
import com.solvibe.main.utils.ContextUtil
import com.solvibe.utils.base.BaseActivity

/**
 *
 * @Description: java类作用描述
 * @Author: LeeQiuuu
 * @CreateDate: 2022/3/31 14:13
 */
abstract class BasicActivity<VB : ViewBinding> : BaseActivity() {
    lateinit var mBinding: VB

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        getViewBinding().also { mBinding = it }
        setContentView(mBinding.root)

        val ivBack = findViewById<ImageView>(R.id.iv_back)
        ivBack?.setOnClickListener {
            finish()
        }
        initView()
    }

    protected abstract fun getViewBinding(): VB

    abstract fun initView()

    /**
     * app内手动切换语言的时候需要调用，多语言时，不需要，只需要value有对应的语言即可、
     */
    override fun attachBaseContext(newBase: Context) {
        super.attachBaseContext(
            ContextUtil.attachBaseContext(
                newBase,
                UserManager.getLanguage().locale // 获取我们存储的语言环境 比如 "en","zh",等等
            )
        )
    }

    /**
     * 分发点击事件.点击外部键盘消失
     */
    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            // 获取当前获得当前焦点所在View
            val view = currentFocus
            if (isClickEditText(view, event)) {
                // 如果不是edittext，则隐藏键盘
                val inputMethodManager =
                    getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
                inputMethodManager?.hideSoftInputFromWindow(view!!.windowToken, 0)
                whenEditLoseFocus(event)
            }
            return super.dispatchTouchEvent(event)
        }
        /**
         * 看源码可知superDispatchTouchEvent 是个抽象方法，用于自定义的Window
         * 此处目的是为了继续将事件由dispatchTouchEvent (MotionEvent event) 传递到onTouchEvent
         * (MotionEvent event) 必不可少，否则所有组件都不能触发 onTouchEvent (MotionEvent event)
         */
        return if (window.superDispatchTouchEvent(event)) {
            true
        } else onTouchEvent(event)
    }

    open fun whenEditLoseFocus(event: MotionEvent) {
    }
}