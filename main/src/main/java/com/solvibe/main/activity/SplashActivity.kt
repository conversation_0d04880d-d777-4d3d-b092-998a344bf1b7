package com.solvibe.main.activity

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import com.solvibe.main.R
import com.solvibe.main.compose.theme.White70
import com.solvibe.main.compose.ui.components.DTPage
import kotlinx.coroutines.delay

@SuppressLint("CustomSplashScreen")
class SplashActivity : BaseComposeActivity() {
    companion object {
        var fromSplash = true
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        fromSplash = true
    }

    @Composable
    override fun ComposeContent() {
        LaunchedEffect(Unit) {
            delay(2_000)
            launchApp()
        }

        DTPage(background = R.drawable.splash_bg) {
            ConstraintLayout(
                Modifier
                    .fillMaxSize()
                    .navigationBarsPadding()
            ) {
                val (logoRef, nameRef, descRef) = createRefs()

                Image(
                    painterResource(R.drawable.ic_launcher),
                    null,
                    Modifier
                        .size(80.dp)
                        .constrainAs(logoRef) {
                            top.linkTo(parent.top, 224.dp)
                            centerHorizontallyTo(parent)
                        }
                )

                Image(
                    painterResource(R.drawable.splash_logo),
                    null,
                    Modifier
                        .width(96.dp)
                        .aspectRatio(286 / 63f)
                        .constrainAs(nameRef) {
                            bottom.linkTo(descRef.top, 20.dp)
                            centerHorizontallyTo(parent)
                        }
                )

                Text(
                    stringResource(R.string.splash_page_tips),
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 18.dp)
                        .constrainAs(descRef) {
                            start.linkTo(parent.start)
                            bottom.linkTo(parent.bottom, 56.dp)
                        },
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = White70,
                        lineHeight = 14.sp,
                        textAlign = TextAlign.Center,
                    ),
                )
            }
        }
    }

    private fun launchApp() {
        startActivity(Intent(this@SplashActivity, MainActivity::class.java))
        finish()
    }
}