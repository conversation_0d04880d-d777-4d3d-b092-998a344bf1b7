package com.solvibe.main.state

import androidx.annotation.StringRes
import com.alibaba.fastjson.annotation.JSONField
import com.solvibe.character.net.runHttp
import com.solvibe.main.R
import com.solvibe.main.ViewModelScopeOwner
import com.solvibe.main.api.appApiFastJson
import com.solvibe.main.bean.CharacterSettings
import com.solvibe.main.ext.stateInViewModelDefault
import com.solvibe.utils.ext.logv
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class SettingsState(override val viewModelScope: CoroutineScope) : ViewModelScopeOwner {
    private val _llmModelsStateFlow = MutableStateFlow(emptyList<LLMModel>())
    val llmModelsStateFlow = _llmModelsStateFlow.asStateFlow()
    val toneList = Tone.all()
    private val _characterSettingsFlow = MutableStateFlow<CharacterSettings?>(null)
    val characterSettingsFlow = _characterSettingsFlow.asStateFlow()

    val currentLLMModelFlow = characterSettingsFlow.map { settings ->
        llmModelsStateFlow.value.find { it.id == settings?.modelId }
    }.stateInViewModelDefault(null)

    val currentToneFlow = characterSettingsFlow.map { settings ->
        toneList.find { it.backendValue == settings?.character }
    }.stateInViewModelDefault(null)

    suspend fun initLLMModels() {
        runHttp {
            appApiFastJson.llmModels().let {
                it.getDataOrNull()?.let { data ->
                    _llmModelsStateFlow.value = data.list
                }
            }
        }
    }

    suspend fun initSettings() {
        runHttp {
            appApiFastJson.getCharacterSettings().let {
                logv("获取角色设置成功: $it")
                _characterSettingsFlow.value = it.getDataOrNull()
            }
        }
    }

    fun selectLLMModel(modelId: Long?) {
        val modelId = modelId ?: 0
        _characterSettingsFlow.update {
            it?.copy(modelId = modelId)
        }
        setSetting(BackendSetting.ModelId(modelId))
    }

    fun selectTone(backendValue: String?) {
        val character = backendValue.orEmpty()
        _characterSettingsFlow.update {
            it?.copy(character = character)
        }
        setSetting(BackendSetting.Tone(character))
    }

    fun setAutoPlay(autoPlay: Boolean) {
        val isAutoPlay = if (autoPlay) 1 else 0
        _characterSettingsFlow.update {
            it?.copy(isAutoPlay = isAutoPlay)
        }
        setSetting(BackendSetting.AutoPlay(isAutoPlay))
    }

    fun setRecord(record: Boolean) {
        val isRecord = if (record) 1 else 0
        _characterSettingsFlow.update {
            it?.copy(isRecord = isRecord)
        }
        setSetting(BackendSetting.Record(isRecord))
    }

    fun setReasoning(reasoning: Boolean) {
        val isReasoning = if (reasoning) 1 else 0
        _characterSettingsFlow.update {
            it?.copy(isReasoning = isReasoning)
        }
        setSetting(BackendSetting.Reasoning(isReasoning))
    }

    private fun setSetting(backendSetting: BackendSetting) {
        viewModelScope.launch {
            runHttp {
                val resp =
                    appApiFastJson.setCharacterSettings(backendSetting.type, backendSetting.value)
                logv("设置$backendSetting: $resp")
            }
        }
    }

    fun reset() {
        _characterSettingsFlow.value = null
    }
}

fun CharacterSettings?.getAutoPlayEnable(): Boolean {
    return this?.isAutoPlay == 1
}

fun CharacterSettings?.getRecordEnable(): Boolean {
    return this?.isRecord == 1
}

fun CharacterSettings?.getReasoningEnable(): Boolean {
    return this?.isReasoning == 1
}

data class LLMModel(
    val id: Long,
    val name: String,
    @JSONField(name = "is_reasoning")
    val isReasoning: Int,
    val tags: String?,
) {
    val tagsList: List<String>
        get() = if (tags.isNullOrEmpty()) emptyList() else tags.split(",")
}

sealed class Tone(
    @StringRes val name: Int,
    val backendValue: String,
) {
    data object Gentle : Tone(R.string.gentle, "flat")
    data object Tsundere : Tone(R.string.tsundere, "assertive")
    data object Enthusiasm : Tone(R.string.enthusiasm, "enthusiastic")
    data object Tough : Tone(R.string.tough, "gentle")
    data object Dull : Tone(R.string.dull, "tsundere")

    companion object {
        fun all(): List<Tone> {
            return listOf(Gentle, Tsundere, Enthusiasm, Tough, Dull)
        }
    }
}

sealed class BackendSetting(
    val type: String,
    val value: Any,
) {
    data class ModelId(val id: Long) : BackendSetting("model_id", id)
    data class Tone(val backendValue: String) : BackendSetting("character", backendValue)
    data class AutoPlay(val autoPlay: Int) : BackendSetting("is_auto_play", autoPlay)
    data class Record(val record: Int) : BackendSetting("is_record", record)
    data class Reasoning(val reasoning: Int) : BackendSetting("is_reasoning", reasoning)
}