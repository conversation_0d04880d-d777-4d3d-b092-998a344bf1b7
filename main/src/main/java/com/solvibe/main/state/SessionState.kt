package com.solvibe.main.state

import com.solvibe.character.net.runHttp
import com.solvibe.main.ViewModelScopeOwner
import com.solvibe.main.api.appApiFastJson
import com.solvibe.main.bean.SVLastMessage
import com.solvibe.main.bean.SVSession
import com.solvibe.utils.ext.loge
import com.solvibe.utils.ext.logv
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

private const val PAGE_SIZE = 20

class SessionState(override val viewModelScope: CoroutineScope) : ViewModelScopeOwner {
    private val _sessionsFlow = MutableStateFlow<List<SVSession>>(emptyList())
    val sessionsFlow = _sessionsFlow.asStateFlow()
    private val _sessionIdFlow = MutableStateFlow<Long?>(null)
    val sessionIdFlow = _sessionIdFlow.asStateFlow()
    private var page = 1

    private val _hasMoreFlow = MutableStateFlow(true)
    val hasMoreFlow = _hasMoreFlow.asStateFlow()

    var refreshing = false
        private set

    var loadingMore = false
        private set

    suspend fun refresh() {
        if (refreshing || loadingMore) return
        page = 1
        refreshing = true
        _sessionsFlow.value = sessions()
        _sessionIdFlow.value = sessionsFlow.value.firstOrNull()?.id
        refreshing = false
    }

    fun updateSessionId(sessionId: Long?) {
        _sessionIdFlow.value = sessionId
    }

    private suspend fun sessions(): List<SVSession> {
        logv("请求会话列表：page: $page")
        val resp = runHttp { appApiFastJson.sessions(page, PAGE_SIZE) }
        return if (resp?.isSuccess == false) {
            emptyList()
        } else {
            val sessions = resp?.getDataOrNull()?.list
            if (sessions.isNullOrEmpty()) {
                _hasMoreFlow.value = false
            } else {
                _hasMoreFlow.value = sessions.size >= PAGE_SIZE
            }
            logv("请求会话列表成功：hasMore: ${_hasMoreFlow.value}")
            sessions.orEmpty()
        }
    }

    fun loadMore() {
        viewModelScope.launch {
            if (refreshing || loadingMore) return@launch
            if (!hasMoreFlow.value) return@launch
            page++
            loadingMore = true
            _sessionsFlow.value += sessions()
            loadingMore = false
        }
    }

    fun deleteSession(sessionId: Long) {
        viewModelScope.launch {
            if (sessionId == sessionIdFlow.value) {
                _sessionIdFlow.value = null
            }
            _sessionsFlow.update { sessions -> sessions.filter { it.id != sessionId } }
            runHttp {
                appApiFastJson.deleteSVSession(sessionId)
                    .ifSuccess {
                        logv("删除会话成功: $sessionId")
                    }
                    .ifError {
                        loge("删除会话失败: $sessionId")
                    }
            }
        }
    }

    fun updateSession(msg: UIMsg?) {
        msg ?: return
        msg.sessionId ?: return

        _sessionsFlow.update { sessions ->
            val existingSession = sessions.find { it.id == msg.sessionId }

            if (existingSession != null) {
                // 如果会话存在，更新最后一条消息
                sessions.map {
                    if (it.id == msg.sessionId) {
                        it.copy(lastMessage = msg.toSVLastMessage())
                    } else {
                        it
                    }
                }
            } else {
                // 如果会话不存在，添加到列表开头
                listOf(SVSession(msg.sessionId, msg.toSVLastMessage())) + sessions
            }
        }
    }

    private fun UIMsg.toSVLastMessage() = SVLastMessage(id, sourceContent, createdAt)

    fun reset() {
        page = 1
        _sessionsFlow.value = emptyList()
        _sessionIdFlow.value = null
        _hasMoreFlow.value = true
    }
}