package com.solvibe.main.state

import com.solvibe.character.net.runHttp
import com.solvibe.main.ViewModelScopeOwner
import com.solvibe.main.api.appApiFastJson
import com.solvibe.main.bean.AskResp
import com.solvibe.main.bean.SVMessage
import com.solvibe.main.compose.ui.page.live2d.SSEEvent
import com.solvibe.main.compose.ui.page.live2d.StreamMsgRepo
import com.solvibe.utils.ext.collectWithMinInterval
import com.solvibe.utils.ext.logv
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.commonmark.parser.Parser
import org.commonmark.renderer.html.HtmlRenderer

private const val PAGE_SIZE = 20

class MessageState(
    override val viewModelScope: CoroutineScope,
    private val sessionState: SessionState
) : ViewModelScopeOwner {
    private val streamMsgRepo = StreamMsgRepo()
    private val _messagesFlow = MutableStateFlow(emptyList<UIMsg>())
    val messagesFlow = _messagesFlow.asStateFlow()

    private val _hasMoreFlow = MutableStateFlow(true)
    val hasMoreFlow = _hasMoreFlow.asStateFlow()

    private val messageEventFlow = MutableSharedFlow<SSEEvent>()
    private val _uiMsgFlow = MutableSharedFlow<Pair<Boolean, AskResp>>()
    val uiMsgFlow = _uiMsgFlow.asSharedFlow()

    var refreshing = false
        private set

    var loadingMore = false
        private set

    init {
        withRecollectMessageEvent()
    }

    private var messageEventJob: Job? = null

    private fun withRecollectMessageEvent(onBeforeRecollect: (() -> Unit)? = null) {
        messageEventJob?.cancel()
        onBeforeRecollect?.invoke()
        messageEventJob = viewModelScope.launch {
            messageEventFlow.collectWithMinInterval(100) { event ->
                onSSEEvent(event)
            }
        }
    }

    suspend fun refresh(sessionId: Long?) {
        if (refreshing || loadingMore) return
        if (sessionId == null) {
            return _messagesFlow.update { emptyList() }
        }
        val isCurrentSession = messagesFlow.value.any { it.sessionId == sessionId }
        if (isCurrentSession) return
        refreshing = true
        _messagesFlow.update { emptyList() }
        _messagesFlow.update { messages(sessionId) }
        refreshing = false
    }

    private suspend fun messages(sessionId: Long): List<UIMsg> {
        val lastMsgId = messagesFlow.value.lastOrNull()?.id
        logv("请求历史消息：sessionId: $sessionId, lastMsgId: $lastMsgId")
        val resp = runHttp { appApiFastJson.messages(sessionId, lastMsgId, PAGE_SIZE) }
        return if (resp?.isSuccess == false) {
            logv("请求历史消息失败：${resp.msg}")
            emptyList()
        } else {
            val remoteMessages = resp?.getDataOrNull()?.list
            if (remoteMessages.isNullOrEmpty()) {
                _hasMoreFlow.value = false
            } else {
                _hasMoreFlow.value = remoteMessages.size >= PAGE_SIZE
            }
            logv("请求历史消息成功：hasMore: ${_hasMoreFlow.value}, 下一页lastMsgId: ${remoteMessages?.lastOrNull()?.id}")
            remoteMessages?.map { it.toUIMsg(sessionId) }.orEmpty()
        }
    }

    fun loadMore(sessionId: Long) {
        viewModelScope.launch {
            if (refreshing || loadingMore) return@launch
            if (!hasMoreFlow.value) return@launch
            loadingMore = true
            _messagesFlow.update { it + messages(sessionId) }
            loadingMore = false
        }
    }

    fun ask(input: String, sessionId: Long?, function: String? = null) {
        val myMsg = myMsg(input, sessionId)
        _messagesFlow.update { listOf(myMsg) + it }
        sendMsg(myMsg.sourceContent, sessionId, function)
    }

    fun tarotFortune(content: String) {
        val sessionId = sessionState.sessionIdFlow.value ?: return
        ask(content, sessionId, "tarot_addition")
    }

    fun continueReply(sessionId: Long?) {
        sendMsg("继续", sessionId, "hidden")
    }

    fun retry(sessionId: Long?) {
        sendMsg("重试", sessionId, "hidden")
    }

    private fun sendMsg(content: String, sessionId: Long?, function: String? = null) {
        withRecollectMessageEvent { streamMsgRepo.cancelAll() }
        streamMsgRepo.sendMsgStream(content, sessionId, function) { event ->
            viewModelScope.launch { messageEventFlow.emit(event) }
        }
    }

    private suspend fun onSSEEvent(event: SSEEvent) {
        when (event) {
            is SSEEvent.Message -> {
                val askResp = event.msg ?: return
                if (askResp.isNotCurrentSession) {
                    return showLog("当前会话已关闭，忽略消息响应")
                }
                if (askResp.content.isNotEmpty()) {
                    showLog("消息响应: ${askResp.currentId}-${askResp.content}")
                } else if (askResp.reasoningContent.isNotEmpty()) {
                    showLog("思考过程响应: ${askResp.currentId}-${askResp.reasoningContent}")
                }
                val isMsgStream = messagesFlow.value.any { it.id == askResp.currentId }
                // 非新消息，拼接返回内容到列表
                if (isMsgStream) {
                    onMsgStream(askResp)
                } else {
                    _messagesFlow.update {
                        // 更新第0条数据的msgId
                        listOf(askResp.toUIMsg()) + it.toMutableList().apply {
                            val firstMsg = this.firstOrNull() ?: return@apply
                            if (firstMsg.isSelf) {
                                showLog("更新第0条数据的msgId: ${firstMsg.id} -> ${askResp.id}")
                                this[0] = firstMsg.copy(id = askResp.id)
                            }
                        }
                    }
                }
                _uiMsgFlow.emit(!isMsgStream to askResp)
            }

            is SSEEvent.Error -> {
                showLog("消息响应错误: ${event.t.stackTraceToString()}")
            }

            is SSEEvent.Close -> {
                showLog("消息响应关闭")
            }

            is SSEEvent.Open -> {
                showLog("消息响应打开")
            }
        }
    }

    private val AskResp.isNotCurrentSession
        get() = sessionState.sessionsFlow.value.any { it.id == sessionId } &&
                sessionId != sessionState.sessionIdFlow.value

    private fun showLog(msg: String) {
        logv(msg, "message_logging")
    }

    private fun onMsgStream(askResp: AskResp) {
        _messagesFlow.update { messages ->
            messages.map {
                when {
                    it.id == askResp.currentId -> it.copy(
                        sessionId = askResp.sessionId,
                        reasoningContent = it.reasoningContent + askResp.reasoningContent,
                        sourceContent = it.sourceContent + askResp.content,
                        htmlContent = (it.sourceContent + askResp.content).markdownToHtml(),
                        talkSuggestion = askResp.talkSuggestion.ifEmpty { it.talkSuggestion }
                    )

                    it.sessionId == null -> it.copy(sessionId = askResp.sessionId)
                    else -> it
                }
            }
        }
    }

    private fun myMsg(content: String, sessionId: Long?): UIMsg {
        return UIMsg(
            System.currentTimeMillis(),
            sessionId,
            "",
            "",
            content,
            true,
            System.currentTimeMillis() / 1000
        )
    }

    fun resetBySessionId(sessionId: Long) {
        if (messagesFlow.value.any { it.sessionId == sessionId }) {
            reset()
        }
    }

    fun reset() {
        withRecollectMessageEvent {
            streamMsgRepo.cancelAll()
            _messagesFlow.value = emptyList()
            _hasMoreFlow.value = true
        }
    }
}

private fun SVMessage.toUIMsg(sessionId: Long) = UIMsg(
    id,
    sessionId,
    "",
    content.markdownToHtml(),
    content,
    speakerType == 2,
    createdAt
)

fun AskResp.toUIMsg(): UIMsg {
    val html = content.markdownToHtml()
    return UIMsg(
        currentId,
        sessionId,
        reasoningContent,
        html,
        content,
        isSelf,
        time
    )
}

fun String.markdownToHtml(): String {
    val parser = Parser.builder().build()
    val md = parser.parse(this)
    val htmlRenderer = HtmlRenderer.builder().build()
    return htmlRenderer.render(md).removeLastParagraphTag()
}

/**
 * 从 HTML 字符串中移除最后一对 <p> 标签。
 * @return 移除最后一对 <p> 标签后的字符串
 */
fun String.removeLastParagraphTag(): String {
    val html = this.trim()
    // 1. 如果字符串不以 </p> 结尾，则直接返回
    if (!html.endsWith("</p>")) {
        return html
    }

    // 2. 找到最后一个 <p> 标签的起始索引
    val lastPIndex = html.lastIndexOf("<p>")

    // 3. 如果找到了 <p> 标签，则执行替换
    return if (lastPIndex != -1) {
        // 移除末尾的 </p>
        val withoutClosingP = html.substring(0, html.length - 4) // 长度为4的</p>

        // 移除最后一个 <p>
        val finalString = withoutClosingP.substring(
            0,
            lastPIndex
        ) + withoutClosingP.substring(lastPIndex + 3) // 长度为3的<p>

        finalString.trim() // 清理可能的首尾空格
    } else {
        html
    }
}

data class UIMsg(
    val id: Long,
    val sessionId: Long?,
    val reasoningContent: String,
    val htmlContent: String,
    val sourceContent: String,
    val isSelf: Boolean,
    val createdAt: Long,
    val talkSuggestion: List<String> = emptyList()
)