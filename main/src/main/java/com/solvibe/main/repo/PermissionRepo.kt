package com.solvibe.main.repo

import com.imyfone.membership.api.bean.PermissionBean
import com.solvibe.Membership
import com.solvibe.main.bean.GuestVipState
import com.solvibe.main.bean.MemberVipState
import com.solvibe.main.bean.UserManager
import com.solvibe.utils.ext.logd
import kotlinx.coroutines.coroutineScope

/**
 *creater:l<PERSON><PERSON><PERSON> on 2025/6/26 10:49
 */
class PermissionRepo : BaseRepo() {

    suspend fun refreshMemberVipState() = coroutineScope {
        Membership.account.getPermissions(status = PermissionBean.STATUS_VALID).also {
            if (it.isSuccess) {
                val permissionBean = getPermissionBean(it.getDataOrNull())
                logd("refreshGuestVipState: $it")
                UserManager.setMemberVipState(
                    MemberVipState(
                        permissionBean?.permissionsName ?: "",
                        permissionBean?.skuID ?: "",
                        permissionBean?.failureTimeText ?: ""
                    )
                )
            }
        }
    }

    suspend fun refreshGuestVipState() = coroutineScope {
        Membership.guestRepo.fetchGuestPermission(status = PermissionBean.STATUS_VALID).also {
            logd("refreshGuestVipState: $it")
            val permissionBean = getPermissionBean(it)
            UserManager.setGuestVipState(
                GuestVipState(
                    permissionBean?.permissionsName ?: "",
                    permissionBean?.skuID ?: "",
                    permissionBean?.failureTimeText ?: ""
                )
            )
        }
    }


    private fun getPermissionBean(data: List<PermissionBean>?): PermissionBean? {
        return if (!data.isNullOrEmpty()) {
            val bean = data.maxByOrNull {
                it.failureTime
            }
            bean
        } else {
            null
        }
    }
}
