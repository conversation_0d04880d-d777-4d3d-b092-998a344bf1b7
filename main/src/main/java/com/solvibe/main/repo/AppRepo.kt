package com.solvibe.main.repo

import com.mfccgroup.android.httpclient.adapter.API
import com.solvibe.Membership
import com.solvibe.character.net.runHttp
import com.solvibe.main.App
import com.solvibe.main.R
import com.solvibe.main.api.appApi
import com.solvibe.main.bean.UserManager
import com.solvibe.main.bean.VersionBean
import com.solvibe.main.config.Constant

class AppRepo : BaseRepo() {
    suspend fun getVersionInfo(): VersionBean? {
        return runHttp {
            val pid = Constant.INFORMATION_SOURCES
            val lang = UserManager.getLanguage().backendId

            appApi.getVersionInfo(pid, lang).data
        }
    }

    suspend fun submitFeedback(email: String, title: String, content: String): API<String>? {
        return runHttp {
            Membership.membershipClient.cbs.postFeedback(
                email,
                title,
                content,
                null,
                "${App.getInstance().getString(R.string.app_name)}android",
                "feedback"
            )
        }
    }
}