package com.solvibe.main.bean

import androidx.annotation.Keep
import com.imyfone.membership.api.bean.SKUBean
import java.util.Locale

/**
 *creater:l<PERSON><PERSON><PERSON> on 2025/9/18 10:59
 */
/**
 * license_id ： 1='1-Month Plan',2='1-Year Plan',3='Lifetime Plan',4='1-Quarter Plan',5='1-DAY Plan',"6" =1-week plan
 *
 */
@Keep
class ProductBean(
    val isDiscount: Boolean = false,
    val licenseName: String,
    val currencyCode: String,
    val localPercent: Float = 0f,
    val discountPercent: Float = 0f,
    val licenseId: String,
    val virtualPrice: String,
    val actualPrice: String,
    val actualPriceAmount: Float,
    val virtualPriceAmount: Float,
    val offerToken: String,
    val skuBean: SKUBean
) {
    fun isWeekSubscribe(): Boolean {
        return "6" == licenseId
    }

    fun isMonthSubscribe(): Boolean {
        return "1" == licenseId
    }

    fun isYearSubscribe(): Boolean {
        return "2" == licenseId
    }

    fun isQuarterSubscribe(): Boolean {
        return "4" == licenseId
    }

    fun isDaySubscribe(): Boolean {
        return "5" == licenseId
    }

    fun isOneTimeSubscribe(): Boolean {
        return "3" == licenseId
    }

    fun getAveragePrice(): String {
        when {
            isDaySubscribe() -> {
                return "$currencyCode$actualPriceAmount"
            }

            isWeekSubscribe() -> {
                val price = actualPriceAmount.div(7)
                val value = String.format(Locale.US, "%.2f", price)
                return "$currencyCode$value"
            }

            isMonthSubscribe() -> {
                val price = actualPriceAmount.div(30)
                val value = String.format(Locale.US, "%.2f", price)
                return "$currencyCode$value"
            }

            isQuarterSubscribe() -> {
                val price = actualPriceAmount.div(90)
                val value = String.format(Locale.US, "%.2f", price)
                return "$currencyCode$value"
            }

            isYearSubscribe() -> {
                val price = actualPriceAmount.div(365)
                val value = String.format(Locale.US, "%.2f", price)
                return "$currencyCode$value"
            }

            isOneTimeSubscribe() -> {
                return "$currencyCode$actualPriceAmount"
            }

            else -> {
                return "$currencyCode$actualPriceAmount"
            }
        }
    }


    fun getMonthlyPrice(): String {
        when {
            isDaySubscribe() -> {
                return "$currencyCode$actualPriceAmount"
            }

            isWeekSubscribe() -> {
                return "$currencyCode$actualPriceAmount"
            }

            isMonthSubscribe() -> {
                return "$currencyCode$actualPriceAmount"
            }

            isQuarterSubscribe() -> {
                val price = actualPriceAmount.div(3)
                val value = String.format(Locale.US, "%.2f", price)
                return "$currencyCode$value"
            }

            isYearSubscribe() -> {
                val price = actualPriceAmount.div(12)
                val value = String.format(Locale.US, "%.2f", price)
                return "$currencyCode$value"
            }

            isOneTimeSubscribe() -> {
                return "$currencyCode$actualPriceAmount"
            }

            else -> {
                return "$currencyCode$actualPriceAmount"
            }
        }
    }
}