package com.solvibe.main.bean;

public class TextModerationBean {
    /*
    命中标签，英文逗号隔开，没有命中的话为空字符串：violence 暴恐、contraband 违禁品、sexuality 色情、profanity 亵渎辱骂、
    pullinTraffic 广告引流、regional 地域对立、C_customized 用户库命中
     */
    private String labels;
    private TextModerationReason reason;

    public TextModerationBean(String labels, TextModerationReason reason) {
        this.labels = labels;
        this.reason = reason;
    }

    public TextModerationBean() {
    }

    public String getLabels() {
        return labels;
    }

    public void setLabels(String labels) {
        this.labels = labels;
    }

    public TextModerationReason getReason() {
        return reason;
    }

    public void setReason(TextModerationReason reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return "TextModerationBean{" +
                "labels='" + labels + '\'' +
                ", reason=" + reason +
                '}';
    }
}
