package com.solvibe.main.bean

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.provider.Settings
import com.solvibe.main.App
import com.solvibe.main.compose.ui.page.main.language.AppSupportedLanguage
import com.solvibe.main.ext.stateInAppDefault
import com.solvibe.main.proto.appDataStore
import com.solvibe.main.proto.updateAppSettings
import com.solvibe.main.proto.updateGuestVipState
import com.solvibe.main.proto.updateMemberVipState
import com.solvibe.utils.data.store.CGStore
import com.solvibe.utils.data.store.createStore
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import java.util.Random
import java.util.UUID

/**
 *
 * @Description: java类作用描述
 * @Author: LeeQiuuu
 * @CreateDate: 2022/7/28 11:41
 */
object UserManager {
    private const val USER_INFO = "UserInfo"
    private val datastore = App.getInstance().appDataStore

    private val userInfoStore: CGStore by lazy {
        createStore(USER_INFO)
    }

    /**
     * 游客购买的sku和过期时间
     */
    val guestVipState = datastore.data.map {
        GuestVipState(
            it.guestVipState.licenseName,
            it.guestVipState.skuId,
            it.guestVipState.vipFailureTimeText
        )
    }.distinctUntilChanged()

    /**
     * 会员购买的sku和过期时间
     */
    fun setGuestVipState(guestVipState: GuestVipState?) {
        App.launch {
            datastore.updateData {
                it.updateGuestVipState {
                    this.licenseName = guestVipState?.licenseName ?: ""
                    this.skuId = guestVipState?.skuId ?: ""
                    this.vipFailureTimeText = guestVipState?.vipFailureTimeText ?: ""
                }
            }
        }
    }


    /**
     * 游客购买的sku和过期时间
     */
    val memberVipState = datastore.data.map {
        MemberVipState(
            it.memberVipState.licenseName,
            it.memberVipState.skuId,
            it.memberVipState.vipFailureTimeText
        )
    }.distinctUntilChanged()

    fun setMemberVipState(memberVipState: MemberVipState?) {
        App.launch {
            datastore.updateData {
                it.updateMemberVipState {
                    this.licenseName = memberVipState?.licenseName
                    this.skuId = memberVipState?.skuId
                    this.vipFailureTimeText = memberVipState?.vipFailureTimeText
                }
            }
        }
    }

    /**
     * 注销账号发送验证码的时间
     */
    val sendVerificationCodeTime =
        datastore.data.map { it.appSettings.sendVerificationCodeTime }.distinctUntilChanged()

    fun setSendVerificationCodeTime(value: Long) {
        App.launch {
            datastore.updateData {
                it.updateAppSettings {
                    setSendVerificationCodeTime(value)
                }
            }
        }
    }

    fun isFirstTimeLaunchApp(): Boolean {
        return userInfoStore.getBoolean("is_first_time_launch_app", true)
    }

    fun setIsFirstTimeLaunchAppFalse() {
        userInfoStore.putBoolean("is_first_time_launch_app", false)
    }

    fun isShowNewProductPage(): Boolean {
        return userInfoStore.getBoolean("is_show_new_product_page", false)
    }

    fun setIsShowNewProductPage() {
        userInfoStore.putBoolean("is_show_new_product_page", true)
    }

    fun isAgreePrivacyPolicy(): Boolean =
        userInfoStore.getBoolean("KEY_HAS_AGREE_PRIVACY_POLICY", false)

    fun setAgreePrivacyPolicy() {
        userInfoStore.putBoolean("KEY_HAS_AGREE_PRIVACY_POLICY", true)
    }

    private const val NAME_KEY = "nameKey"

    fun setName(name: String) {
        userInfoStore.putString(NAME_KEY, name)
    }

    fun getName(): String? {
        return userInfoStore.getString(NAME_KEY)
    }

    private const val AGE_KEY = "ageKey"

    fun setAge(age: Int) {
        userInfoStore.putInt(AGE_KEY, age)
    }

    fun getAge(): Int {
        return userInfoStore.getInt(AGE_KEY)
    }

    private const val GENDER_KEY = "genderKey"

    fun setGender(gender: Int) {
        userInfoStore.putInt(GENDER_KEY, gender)
    }

    fun getGender(): Int {
        return userInfoStore.getInt(GENDER_KEY)
    }

    fun hasReportUserInfo(): Boolean {
        return userInfoStore.getBoolean("submitUserInfo", false)
    }

    fun setHasReportUserInfo() {
        userInfoStore.putBoolean("submitUserInfo", true)
    }

    private const val DEVICE_ID_KEY = "DEVICE_ID_KEY"

    fun getDeviceId(): String {
        val deviceId = userInfoStore.getString(DEVICE_ID_KEY, null)
        if (deviceId == null) {
            val newDeviceId = newDeviceId()
            setDeviceId(newDeviceId)
            return newDeviceId
        }
        return deviceId
    }

    fun setDeviceId(deviceId: String) {
        userInfoStore.putString(DEVICE_ID_KEY, deviceId)
    }

    @SuppressLint("HardwareIds")
    private fun newDeviceId(): String {
        val androidId =
            Settings.Secure.getString(App.getInstance().contentResolver, Settings.Secure.ANDROID_ID)
        if (androidId == null) {
            return getNativeDeviceId(createNonceStr())
        }
        return androidId
    }

    @Suppress("DEPRECATION")
    private fun getNativeDeviceId(salt: String): String {
        val mDeviceIdShort = StringBuilder()
            .append("FamiGuardCN_Parent")
            .append(Build.BOARD)
            .append(Build.CPU_ABI)
            .append(Build.DEVICE)
            .append(Build.MANUFACTURER)
            .append(Build.MODEL)
            .append(Build.PRODUCT).toString()
        var serial: String?
        try {
            serial = Build::class.java.getField("SERIAL").get(null)?.toString()
            if ("unknown" == serial) {
                serial = salt
            }
            return UUID(mDeviceIdShort.hashCode().toLong(), serial.hashCode().toLong()).toString()
        } catch (_: Exception) {
            serial = salt
        }
        return UUID(mDeviceIdShort.hashCode().toLong(), serial.hashCode().toLong()).toString()
    }

    private fun createNonceStr(): String {
        val builder = StringBuilder()
        val randomStr = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        val count = randomStr.length - 1
        val random = Random()
        (0..7).forEach { i ->
            val id = random.nextInt(count)
            val s = randomStr.substring(id, id + 1)
            builder.append(s)
        }
        return builder.toString()
    }

    val languageFlow = datastore.data.map {
        val languageId = it.appSettings.language
        AppSupportedLanguage.entries.find { lang -> lang.id == languageId }
            ?: AppSupportedLanguage.ENGLISH
    }.stateInAppDefault(AppSupportedLanguage.ENGLISH)

    fun initLanguage(context: Context) {
        // 没设置过语言，就尝试使用手机的语言
        if (getLanguageId() != null) return
        val phoneLanguage = context.resources.configuration.locales.get(0)
        val language = AppSupportedLanguage.entries.find {
            if (it == AppSupportedLanguage.CHINESE_TW || it == AppSupportedLanguage.CHINESE_CN) {
                it.locale == phoneLanguage || (it.locale.language == phoneLanguage.language && it.locale.country == phoneLanguage.country)
            } else {
                it.locale.language == phoneLanguage.language
            }
        }
            ?: AppSupportedLanguage.ENGLISH
        setLanguageId(language.id)
    }

    const val LANGUAGE_ID = "languageId"

    fun setLanguageId(id: String) {
        userInfoStore.putString(LANGUAGE_ID, id)
        App.launch {
            datastore.updateData {
                it.updateAppSettings {
                    setLanguage(id)
                }
            }
        }
    }

    fun getLanguageId(): String? {
        return userInfoStore.getString(LANGUAGE_ID)
    }

    fun getLanguage(): AppSupportedLanguage {
        return AppSupportedLanguage.entries.find { it.id == getLanguageId() }
            ?: AppSupportedLanguage.ENGLISH
    }
}