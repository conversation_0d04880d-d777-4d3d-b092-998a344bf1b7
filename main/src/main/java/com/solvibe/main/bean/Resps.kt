package com.solvibe.main.bean

import com.alibaba.fastjson.annotation.JSONField
import com.google.gson.annotations.SerializedName
import com.solvibe.utils.utils.TimeUtils.YMD
import com.solvibe.utils.utils.newTimeFMTStr

data class AskResp(
    val id: Long,
    @SerializedName("current_id")
    val currentId: Long,
    val content: String,
    @SerializedName("reasoning_content")
    val reasoningContent: String,
    val audio: String,
    val time: Long,
    val type: Int,
    @SerializedName("session_id")
    val sessionId: Long,
    val isSelf: Boolean = false,
    @SerializedName("talk_suggestion")
    val talkSuggestion: List<String> = emptyList(),
)

data class ListResp<T>(
    val list: List<T>
)

data class SVSession(
    val id: Long,
    @JSONField(name = "last_message")
    val lastMessage: SVLastMessage?
) {
    val dateStr: String
        get() {
            return if (lastMessage == null) {
                newTimeFMTStr(format = YMD)
            } else {
                newTimeFMTStr(lastMessage.createdAt * 1000, format = YMD)
            }
        }
}

data class SVLastMessage(
    val id: Long,
    val content: String,
    @JSONField(name = "created_at")
    val createdAt: Long,
)

/**
 * @param speakerType 1-角色，2-用户
 * @param type 1-文本，2-图片，3-视频，4-语音
 */
data class SVMessage(
    val id: Long,
    @JSONField(name = "speaker_type")
    val speakerType: Int,
    val type: Int,
    val content: String,
    @JSONField(name = "created_at")
    val createdAt: Long,
)

data class CharacterSettings(
    val id: Long,
    @JSONField(name = "member_id")
    val memberId: Long,
    val email: String,
    @JSONField(name = "member_type")
    val memberType: Int,
    @JSONField(name = "model_id")
    val modelId: Long,
    val character: String,
    @JSONField(name = "is_auto_play")
    val isAutoPlay: Int,
    @JSONField(name = "is_record")
    val isRecord: Int,
    @JSONField(name = "is_reasoning")
    val isReasoning: Int,
)

data class TouristsData(
    val token: String,
    @JSONField(name = "is_bound")
    val isBound: Int,
)