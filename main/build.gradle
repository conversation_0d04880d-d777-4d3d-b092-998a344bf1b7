plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.devtools.ksp'
    id 'org.jetbrains.kotlin.plugin.compose'
    id 'org.jetbrains.kotlin.plugin.serialization'
    id 'kotlin-parcelize'
    id 'com.google.protobuf'
}

apply from: "$rootProject.projectDir/common.gradle"

android {
    namespace "com.solvibe.main"

    defaultConfig {
        ndk {
            // 设置支持的SO库架构
            abiFilters 'armeabi', 'x86', 'armeabi-v7a', 'x86_64', 'arm64-v8a'
        }

        ksp {
            arg("room.schemaLocation", "$projectDir/schemas")
        }

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation": "$projectDir/schemas"]
            }
        }
    }

    buildFeatures {
        buildConfig true
        compose true
    }
//deeptalkie-api.irocketx.com", "deeptalkie-api.ifonelab.net","lac-api.deeptalkie.com
    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "BaseUrl", "\"https://account-api-lumeflow.ifonelab.net/\""
            buildConfigField "String", "BaseUrlICart", "\"https://order-api-lumeflow.ifonelab.net\""
            buildConfigField "String", "LacUrl", "\"https://lac-api-solvibe.ifonelab.net/\""
            buildConfigField "String", "IpCheck", "\"https://ut-api-solvibeai.ifonelab.net/\""
            buildConfigField "String", "AppCheckUpdate", "\"https://apipdm.lumeflow.ai/\""
            buildConfigField "String", "BaseUrlSolvibe", "\"https://solvibe-api.ifonelab.net/\""
            buildConfigField "String", "CBSUrl", "\"https://cbs-api.lumeflow.ai/\""
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "BaseUrl", "\"https://account-api.lumeflow.ai/\""
            buildConfigField "String", "BaseUrlICart", "\"https://order-api.lumeflow.ai\""
            buildConfigField "String", "LacUrl", "\"https://lac-api.solvibeai.com/\"" //lac url
            buildConfigField "String", "IpCheck", "\"https://ut-api.solvibeai.com/\""
            buildConfigField "String", "AppCheckUpdate", "\"https://apipdm.lumeflow.ai/\""
            buildConfigField "String", "BaseUrlSolvibe", "\"https://solvibe-api.irocketx.com/\""
            buildConfigField "String", "CBSUrl", "\"https://solvibe-api.irocketx.com/\""
            buildConfigField "String", "CBSUrl", "\"https://cbs-api.lumeflow.ai/\""
        }
        rc {
            initWith release
        }
    }

    viewBinding {
        enabled = true
    }
}

apply from: "$rootProject.projectDir/channel.gradle"

composeCompiler {
    reportsDestination = layout.buildDirectory.dir("compose_compiler")
    stabilityConfigurationFile = rootProject.layout.projectDirectory.file("stability_config.conf")
}

dependencies {
    api project(':utils')
    implementation project(':net')
    implementation project(':live2d')

    implementation fileTree(include: ['*.aar', '*.jar'], dir: 'libs')
    // Import the Firebase BoM
    implementation platform(libs.firebase.bom)
    // When using the BoM, don't specify versions in Firebase dependencies
    implementation libs.firebase.analytics.ktx

    //渠道追踪依赖
    implementation libs.track
    implementation libs.localbroadcastmanager
    implementation libs.work.runtime.ktx

    implementation libs.androidx.core.ktx
    implementation libs.app.compat
    implementation libs.constraintlayout
    implementation libs.constraintlayout.compose
    implementation libs.androidx.savedstate.ktx

    implementation libs.lottie // 加载json动画框架
    implementation libs.lottie.compose // 加载json动画框架

    //apk下载
    implementation libs.room.runtime
    implementation libs.room.ktx
    implementation libs.androidx.activity
    ksp libs.room.compiler
    implementation libs.datastore

    implementation libs.kotlinx.serialization.json

    // Koin Core features
    implementation libs.koin.core
    api libs.koin.android
    // Koin Test features
    testImplementation libs.koin.test

    implementation platform(libs.androidx.compose.bom)
    implementation libs.androidx.activity.compose
    implementation libs.fragment
    implementation libs.fragment.ktx
    implementation libs.fragment.compose
    implementation libs.androidx.ui
    implementation libs.androidx.ui.graphics
    implementation libs.androidx.ui.tooling.preview
    implementation libs.androidx.material3
    implementation libs.navigation.compose
    implementation libs.material3.navigation.suite
    api libs.coil.compose
    api libs.coil.network.okhttp
    api libs.coil.gif
    api libs.coil.video
    api libs.landscapist.coil3
    api libs.landscapist.transformation
    api libs.zoomable.coil3

    implementation libs.media3.exoplayer
    implementation libs.media3.ui
    implementation libs.media3.common

    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
    androidTestImplementation platform(libs.androidx.compose.bom)
    androidTestImplementation libs.androidx.ui.test.junit4
    debugImplementation libs.androidx.ui.tooling
    debugImplementation libs.androidx.ui.test.manifest

    implementation libs.play.services.auth
    implementation platform(libs.firebase.bom)
    api libs.firebase.analytics.ktx
    api libs.firebase.config.ktx
    api libs.firebase.crashlytics.ktx
    implementation libs.firebase.auth.ktx

    implementation libs.protobuf.javalite

    //会员系统的组件
    releaseApi libs.membership.release
    rcApi libs.membership.release
    debugApi libs.membership.dev
    //网络环境
    implementation libs.httpclient
    implementation(libs.membership.ext.googlelogin)

    implementation(libs.commonmark)
    implementation libs.billing.ktx

    implementation(libs.paging.runtime)
    implementation(libs.paging.compose)
}

protobuf {
    protoc {
        artifact = 'com.google.protobuf:protoc:3.19.1'
    }
    generateProtoTasks {
        all().configureEach { task ->
            task.builtins {
                java {
                    option "lite"
                }
            }
        }
    }
}