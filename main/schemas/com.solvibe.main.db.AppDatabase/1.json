{"formatVersion": 1, "database": {"version": 1, "identityHash": "9f1a3a513ada238474a8063155aafe52", "entities": [{"tableName": "ai_role", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '9f1a3a513ada238474a8063155aafe52')"]}}