[versions]
agp = "8.12.2"

# app
billingKtx = "6.0.1"
coreKtx = "1.17.0"
appcompat = "1.7.1"
activity = "1.11.0"
fragment = "1.8.9"
work = "2.10.4"
savedstate = "1.3.3"
constraint = "2.2.1"
exoplayer = "1.8.0"
localbroadcastmanager = "1.1.0"

# kotlin
kotlin = "2.1.10"
ksp = "2.1.10-1.0.31"
kotlinxSerializationJson = "1.9.0"
koin = "4.1.0"

# compose
composeBom = "2025.09.00"
activityCompose = "1.11.0"
constraintlayoutCompose = "1.1.1"

# lifecycle & navigation
lifecycle = "2.9.4"
lifecycleExtensions = "2.2.0"
navigation = "2.9.4"

# strorage & paging
room = "2.7.2"
datastore = "1.1.7"
protobufGradlePlugin = "0.9.5"
protobufJavalite = "4.32.0"
# mmkv使用1.3.x版本，防止32位cpu架构手机崩溃
mmkv = "1.3.14"
paging = "3.3.6"

# image & animate
coil = "3.3.0"
landscapist = "2.5.2"
telephoto = "0.17.0"
lottie = "6.6.7"

# network & aws-s3 & 序列化
retrofit = "3.0.0"
okhttp3 = "5.1.0"
aws-s3 = "1.5.30"
gson = "2.13.2"
moshi = "1.15.2"
#noinspection NewerVersionAvailable 会员系统组件只能使用1.xxx的版本
fastjson = "1.2.83"

# gms
firebaseCrashlyticsGradle = "2.8.1"
playServices = "21.3.0"
playReview = "2.0.2"
googleServices = "4.3.13"
firebaseBom = "33.10.0"
playServicesAds = "24.0.0"
# billing don't update
#noinspection GradleDependency
billing = "7.1.1"

# imyfone
#noinspection GradleDependency
membership_dev = "1.4.2-devRelease"
membership_release = "1.4.2"
httpclient = "1.2.0"
membershipExtGooglelogin = "*******"
mfccgroupPlugin = "1.1.4"
track = "0.9.0-beta"

# logger
timber = "5.0.1"

# third library
codelocator = "2.0.4"
commonmark = "0.26.0"

# test
junit = "4.13.2"
junitVersion = "1.3.0"
espressoCore = "3.7.0"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
app-compat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
agp = { module = "com.android.tools.build:gradle", version.ref = "agp" }
codelocator-core = { module = "com.bytedance.tools.codelocator:codelocator-core", version.ref = "codelocator" }
androidx-savedstate-ktx = { module = "androidx.savedstate:savedstate-ktx", version.ref = "savedstate" }
codelocator-lancet-all = { module = "com.bytedance.tools.codelocator:codelocator-lancet-all", version.ref = "codelocator" }
com-google-devtools-ksp-gradle-plugin = { module = "com.google.devtools.ksp:com.google.devtools.ksp.gradle.plugin", version.ref = "ksp" }
commonmark = { module = "org.commonmark:commonmark", version.ref = "commonmark" }
firebase-crashlytics-gradle = { module = "com.google.firebase:firebase-crashlytics-gradle", version.ref = "firebaseCrashlyticsGradle" }
google-services = { module = "com.google.gms:google-services", version.ref = "googleServices" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
material3-navigation-suite = { module = "androidx.compose.material3:material3-adaptive-navigation-suite" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }
membership-ext-googlelogin = { module = "com.mfccgroup.android:membership-ext-googlelogin", version.ref = "membershipExtGooglelogin" }
navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigation" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraint" }
constraintlayout-compose = { group = "androidx.constraintlayout", name = "constraintlayout-compose", version.ref = "constraintlayoutCompose" }
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }
lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycle" }
lifecycle-extensions = { group = "androidx.lifecycle", name = "lifecycle-extensions", version.ref = "lifecycleExtensions" }
lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
mfccgroup-plugin = { module = "com.mfccgroup.android:plugin", version.ref = "mfccgroupPlugin" }
kotlin-compose-plugin = { module = "org.jetbrains.kotlin.plugin.compose:org.jetbrains.kotlin.plugin.compose.gradle.plugin", version.ref = "kotlin" }
kotlin-serialization-plugin = { module = "org.jetbrains.kotlin.plugin.serialization:org.jetbrains.kotlin.plugin.serialization.gradle.plugin", version.ref = "kotlin" }
protobuf-gradle-plugin = { module = "com.google.protobuf:protobuf-gradle-plugin", version.ref = "protobufGradlePlugin" }
protobuf-javalite = { module = "com.google.protobuf:protobuf-javalite", version.ref = "protobufJavalite" }
paging-runtime = { module = "androidx.paging:paging-runtime", version.ref = "paging" }
paging-compose = { module = "androidx.paging:paging-compose", version.ref = "paging" }
localbroadcastmanager = { group = "androidx.localbroadcastmanager", name = "localbroadcastmanager", version.ref = "localbroadcastmanager" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }
work-runtime-ktx = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "work" }
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
fastjson = { group = "com.alibaba", name = "fastjson", version.ref = "fastjson" }
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebaseBom" }
firebase-crashlytics-ktx = { group = "com.google.firebase", name = "firebase-crashlytics-ktx" }
firebase-analytics-ktx = { group = "com.google.firebase", name = "firebase-analytics-ktx" }
firebase-config-ktx = { group = "com.google.firebase", name = "firebase-config-ktx" }
firebase-auth-ktx = { module = "com.google.firebase:firebase-auth-ktx" }
play-services-ads = { group = "com.google.android.gms", name = "play-services-ads", version.ref = "playServicesAds" }
play-review = { group = "com.google.android.play", name = "review", version.ref = "playReview" }
play-review-ktx = { group = "com.google.android.play", name = "review-ktx", version.ref = "playReview" }
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
okhttp3 = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp3" }
okhttp3-sse = { group = "com.squareup.okhttp3", name = "okhttp-sse", version.ref = "okhttp3" }
okhttp3-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp3" }
moshi = { group = "com.squareup.moshi", name = "moshi-kotlin", version.ref = "moshi" }
moshi-kotlin-codegen = { group = "com.squareup.moshi", name = "moshi-kotlin-codegen", version.ref = "moshi" }
retrofit-converter-moshi = { group = "com.squareup.retrofit2", name = "converter-moshi", version.ref = "retrofit" }
track = { group = "com.mfccgroup.android", name = "track", version.ref = "track" }
lottie = { group = "com.airbnb.android", name = "lottie", version.ref = "lottie" }
lottie-compose = { module = "com.airbnb.android:lottie-compose", version.ref = "lottie" }
timber = { group = "com.jakewharton.timber", name = "timber", version.ref = "timber" }
koin-core = { group = "io.insert-koin", name = "koin-core", version.ref = "koin" }
koin-android = { group = "io.insert-koin", name = "koin-android", version.ref = "koin" }
koin-test = { group = "io.insert-koin", name = "koin-test", version.ref = "koin" }
billing = { group = "com.android.billingclient", name = "billing", version.ref = "billing" }
billing-ktx = { group = "com.android.billingclient", name = "billing-ktx", version.ref = "billing" }
coil-compose = { group = "io.coil-kt.coil3", name = "coil-compose", version.ref = "coil" }
coil-network-okhttp = { group = "io.coil-kt.coil3", name = "coil-network-okhttp", version.ref = "coil" }
coil-gif = { group = "io.coil-kt.coil3", name = "coil-gif", version.ref = "coil" }
coil-video = { group = "io.coil-kt.coil3", name = "coil-video", version.ref = "coil" }
landscapist-coil3 = { group = "com.github.skydoves", name = "landscapist-coil3", version.ref = "landscapist" }
landscapist-transformation = { group = "com.github.skydoves", name = "landscapist-transformation", version.ref = "landscapist" }
zoomable-coil3 = { group = "me.saket.telephoto", name = "zoomable-image-coil3", version.ref = "telephoto" }
mmkv = { group = "com.tencent", name = "mmkv", version.ref = "mmkv" }
androidx-activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
media3-exoplayer = { group = "androidx.media3", name = "media3-exoplayer", version.ref = "exoplayer" }
media3-ui = { group = "androidx.media3", name = "media3-ui", version.ref = "exoplayer" }
media3-common = { group = "androidx.media3", name = "media3-common", version.ref = "exoplayer" }
play-services-auth = { group = "com.google.android.gms", name = "play-services-auth", version.ref = "playServices" }
fragment = { module = "androidx.fragment:fragment", version.ref = "fragment" }
fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "fragment" }
fragment-compose = { module = "androidx.fragment:fragment-compose", version.ref = "fragment" }
datastore = { module = "androidx.datastore:datastore", version.ref = "datastore" }
#noinspection SimilarGradleDependency
membership-release = { module = "com.mfccgroup.android:membership", version.ref = "membership_release" }
#noinspection SimilarGradleDependency
membership-dev = { module = "com.mfccgroup.android:membership", version.ref = "membership_dev" }
httpclient = { module = "com.mfccgroup.android:httpclient", version.ref = "httpclient" }
[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }