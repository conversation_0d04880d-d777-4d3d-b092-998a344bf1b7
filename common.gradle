android {
    compileSdk compile_sdk as int
    buildToolsVersion build_tools_version

    defaultConfig {
        minSdk min_sdk as int
        targetSdk target_sdk as int

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = jvm_target
        freeCompilerArgs = ["-Xstring-concat=inline", "-Xcontext-receivers"]
    }

    sourceSets {
        main {
            jniLibs.srcDir 'libs'
            jni.srcDirs = []    //disable automatic ndk-build
        }
    }
}
