package com.solvibe.character.net.interceptor

import com.solvibe.character.net.interceptor.SignConfig.SIGN_LAC_DEBUG_DOMAIN
import com.solvibe.character.net.interceptor.SignConfig.SIGN_LAC_RELEASE_DOMAIN
import com.solvibe.character.net.interceptor.SignConfig.SIGN_SOLVIBE_BACKSTAGE_DEBUG_DOMAIN
import com.solvibe.character.net.interceptor.SignConfig.SIGN_SOLVIBE_BACKSTAGE_RELEASE_DOMAIN
import com.solvibe.utils.base.BaseApplication
import com.solvibe.utils.ext.logd
import com.solvibe.utils.ext.logv
import com.solvibe.utils.utils.MD5Utils
import okhttp3.FormBody
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okio.Buffer
import org.json.JSONArray
import org.json.JSONObject
import java.util.TreeMap

class RequestInterceptor(private val application: BaseApplication) : Interceptor {
    /**
     * 根据传进来的map生成string
     * map内的值按照key的字母顺序排序
     * 将map的key和value用=连接，多个键值对之间用&连接
     * 最后在末尾拼接上key=351f4b0d34e21efede761c88b74b9783feb6aae4
     * 对拼接后的字符串进行md5加密，得到签名
     */
    fun coinSystemSign(map: TreeMap<String, String?>): String {
        val queryString = map.run {
            map {
                "${it.key}=${it.value}"
            }.joinToString("&", postfix = "&key=351f4b0d34e21efede761c88b74b9783feb6aae4")
        }
        logv("签名前的queryString: $queryString", "http_param_sign")
        return MD5Utils.getStringMD5(queryString).uppercase()
    }

    private fun isFunctionUrl(url: String): Boolean {
        return setOf(
            SIGN_LAC_DEBUG_DOMAIN,
            SIGN_LAC_RELEASE_DOMAIN,
            SIGN_SOLVIBE_BACKSTAGE_DEBUG_DOMAIN,
            SIGN_SOLVIBE_BACKSTAGE_RELEASE_DOMAIN
        ).find { url.contains(it, true) } != null
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val oldHttpUrl = request.url
        //添加查询参数
        val method = request.method
        val oldHttpBuilder = oldHttpUrl.newBuilder()
        val builder = request.newBuilder()
        val timestamp = "${System.currentTimeMillis() / 1000}"
        if (isFunctionUrl(oldHttpUrl.toString())) {
            if (method.equals("get", true)) {
                // 含有这个域名的get方法才加
                val map = TreeMap<String, String?>()
                for (queryParameterName in oldHttpUrl.queryParameterNames) {
                    map[queryParameterName] = "${oldHttpUrl.queryParameter(queryParameterName)}"
                }
                oldHttpBuilder.setQueryParameter("timestamp", timestamp)
                map["timestamp"] = timestamp
                val sign = coinSystemSign(map)
                oldHttpBuilder.addQueryParameter("sign", sign)
            } else if (method.equals("post", true)) {
                val body = request.body
                if (body is FormBody) {
                    val newFormBodyBuilder = FormBody.Builder()
                    val map = TreeMap<String, String?>()
                    // 将所有 POST 参数添加到 map 中
                    for (i in 0 until body.size) {
                        val name = body.name(i)
                        val value = body.value(i)
                        // 检查参数是否为 JSON 字符串
                        if (isJsonString(value)) {
                            // 如果是 JSON 字符串，直接添加，不进行加密
                            newFormBodyBuilder.add(name, value)
                        } else {
                            // 对于普通参数，先添加到 map 中以便后续生成签名
                            map[name] = value
                            newFormBodyBuilder.add(name, value)
                        }
                    }

                    map["timestamp"] = timestamp
                    newFormBodyBuilder.add("timestamp", timestamp)

                    // 生成签名
                    val sign = coinSystemSign(map)

                    // 添加签名到 POST 参数中
                    newFormBodyBuilder.add("sign", sign)
                    val newFormBody = newFormBodyBuilder.build()

                    // 构建新的请求体
                    val newRequestBuilder = request.newBuilder()
                        .url(oldHttpUrl)
                        .method(request.method, newFormBody)

                    newRequestBuilder.addDefaultHeaders()
                    return chain.proceed(newRequestBuilder.build())
                } else if (body is RequestBody) {
                    val buffer = Buffer()
                    body.writeTo(buffer)
                    val requestBodyString = buffer.readUtf8()

                    logd("Original Request Body: $requestBodyString")

                    // 你可以选择性地对整个请求体进行加密处理，或对其中的某些字段进行加密。
                    // 比如，假设请求体是一个 JSON 对象，可以在此解析并加密某些字段。
                    val modifiedRequestBodyString =
                        processAndSignRequestBody(requestBodyString, timestamp)

                    logd("Modified Request Body: $modifiedRequestBodyString")

                    val newRequestBody = modifiedRequestBodyString.toRequestBody(body.contentType())

                    // 创建新的请求
                    val newRequestBuilder = request.newBuilder()
                        .url(oldHttpUrl)
                        .method(request.method, newRequestBody)

                    newRequestBuilder.addDefaultHeaders()
                    return chain.proceed(newRequestBuilder.build())
                }
            }
        }

        builder.addDefaultHeaders()
        val build = oldHttpBuilder.build()
        return chain.proceed(builder.url(build).build())
    }

    private fun Request.Builder.addDefaultHeaders() {
        header("Device-Code", application.deviceId())
        header("User-Agent", "android")
        header("Platform", "android")
        header("Token", getToken())
        header("Language", application.language())
    }

    private fun isJsonString(value: String?): Boolean {
        return value != null && (value.trim().startsWith("{") && value.trim().endsWith("}")
                || value.trim().startsWith("[") && value.trim().endsWith("]"))
    }

    fun processAndSignRequestBody(body: String, timestamp: String): String {
        val jsonObject = JSONObject(body.ifEmpty { "{}" })
        val tempMap = mutableMapOf<String, Any?>()
        // 动态遍历所有键值对并处理
        val map = TreeMap<String, String?>()
        jsonObject.keys().forEach { key ->
            val value = jsonObject.get(key)
            when (value) {
                is JSONObject, is JSONArray -> {
                    // 如果值是 JSON 对象或数组，则跳过加密，直接放入 tempMap
                    tempMap[key] = value
                }

                else -> {
                    // 如果是普通类型，对其进行加密
                    map[key] = value.toString()  // 加密后的值加入到用于签名的 map 中
                    tempMap[key] = value.toString()  // 将加密后的值存入临时 map
                }
            }
        }
        tempMap.forEach { (key, value) ->
            jsonObject.put(key, value)
        }
        map["timestamp"] = timestamp
        jsonObject.put("timestamp", timestamp)
        // 生成签名并添加到请求体
        val sign = coinSystemSign(map)
        jsonObject.put("sign", sign)

        return jsonObject.toString()
    }

    private fun getToken(): String {
        return application.getToken()
    }
}
