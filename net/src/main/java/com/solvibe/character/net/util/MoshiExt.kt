package com.solvibe.character.net.util

import com.solvibe.character.net.client.IgnoreStringForObjectAdapter
import com.solvibe.character.net.client.SingleToArrayAdapter
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory

//moshi json库 kotlin 辅助类
val sMoshi: Moshi = Moshi.Builder()
    .add(KotlinJsonAdapterFactory())
    .add(SingleToArrayAdapter.INSTANCE)
    .add(IgnoreStringForObjectAdapter.INSTANCE)
    .build()

//将json string转化为object list
inline fun <reified T> String.parseToArray(): List<T> {
    if (this.isBlank()) return emptyList()
    val type = Types.newParameterizedType(MutableList::class.java, T::class.java)
    val a: JsonAdapter<List<T>> = sMoshi.adapter(type)
    return a.fromJson(this) ?: emptyList()
}

//将json string转化为object
inline fun <reified T> String.parseToObject(): T? {
    val a: JsonAdapter<T> = sMoshi.adapter(T::class.java)
    return a.fromJson(this)
}

//将object转化为string
inline fun <reified T> T.toJsonString(): String {
    val a: JsonAdapter<T> = sMoshi.adapter(T::class.java)
    return a.toJson(this) ?: ""
}
