package com.solvibe.character.net

import com.alibaba.fastjson.JSON
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.ResponseBody
import retrofit2.Converter
import retrofit2.Retrofit
import java.lang.reflect.Type
import java.nio.charset.Charset

class FastJsonConverterFactory<T> : Converter.Factory() {

    override fun requestBodyConverter(
        type: Type,
        parameterAnnotations: Array<Annotation>,
        methodAnnotations: Array<Annotation>,
        retrofit: Retrofit
    ): Converter<T, RequestBody>? {
        return Converter { value: T ->
            JSON.toJSONString(value).toByteArray(UTF_8).toRequestBody(MEDIA_TYPE)
        }
    }

    override fun responseBodyConverter(
        type: Type,
        annotations: Array<Annotation>,
        retrofit: Retrofit
    ): Converter<ResponseBody, T>? {
        return if (getRawType(type) == String::class.java) {
            null
        } else {
            Converter { value: ResponseBody ->
                JSON.parseObject(value.string(), type)
            }
        }
    }

    companion object {
        private val MEDIA_TYPE: MediaType = "application/json;charset=UTF-8".toMediaType()

        private val UTF_8 = Charset.forName("utf-8")
        fun create(): FastJsonConverterFactory<*> {
            return FastJsonConverterFactory<Any?>()
        }
    }
}