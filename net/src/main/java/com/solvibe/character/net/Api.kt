package com.solvibe.character.net

import com.solvibe.utils.ext.loge

fun <T> Response<T>.successFun(invoke: (T?) -> Unit): Response<T> {
    if (200 == this.code) {
        invoke(this.data)
    }
    return this
}

fun <T> Response<T>.success(invoke: (T?) -> Unit): Response<T> {
    if (200 == this.code) {
        invoke(this.data)
    }
    return this
}

fun <T> Response<T>.getSuccessData(): T? {
    var respData: T? = null
    if (200 == this.code) {
        respData = data
    }
    return respData
}

suspend fun <T> Response<T>.asyncSuccess(invoke: suspend (T?) -> Unit): Response<T> {
    if (200 == this.code) {
        invoke(this.data)
    }
    return this
}

suspend fun <T> ListResponse<T>.asyncSuccess(invoke: suspend (T) -> Unit): ListResponse<T> {
    if (200 == this.code) {
        this.data?.apply {
            invoke(this)
        }
    }
    return this
}

fun <T> ListResponse<T>.getSuccessData(): T? {
    var respData: T? = null
    if (200 == this.code) {
        respData = data
    }
    return respData
}

fun <T> ListResponse<T>.success(invoke: (T) -> Unit): ListResponse<T> {
    if (200 == this.code) {
        this.data?.apply {
            invoke(this)
        }
    }
    return this
}

fun <T> ListResponse<T>.error(invoke: (Int, String) -> Unit): ListResponse<T> {
    if (200 != this.code) {
        invoke(this.code, this.msg)
    }
    return this
}

fun <T> Response<T>.error(invoke: (Int, String) -> Unit): Response<T> {
    if (200 != this.code) {
        invoke(this.code ?: 0, this.msg ?: "")
    }
    return this
}

suspend fun <T> Response<T>.asyncError(invoke: suspend (Int, String) -> Unit): Response<T> {
    if (200 != this.code) {
        invoke(this.code ?: 0, this.msg ?: "")
    }
    return this
}

fun <T> Response<T>.error(invoke: (Int, String?, T?) -> Unit): Response<T> {
    if (200 != this.code) {
        invoke(this.code ?: 0, this.msg, this.data)
    }
    return this
}

suspend fun <T> runHttp(block: suspend () -> T): T? {
    return try {
        block()
    } catch (e: Exception) {
        loge(e.stackTraceToString(), "http_error")
        null
    }
}

fun <T> Resp<T>.success(invoke: (T?) -> Unit): Resp<T> {
    if (1 == this.code) {
        invoke(this.data)
    }
    return this
}

fun <T> Resp<T>.error(invoke: (Int, String) -> Unit): Resp<T> {
    if (1 != this.code) {
        invoke(this.code ?: 0, this.message ?: "")
    }
    return this
}