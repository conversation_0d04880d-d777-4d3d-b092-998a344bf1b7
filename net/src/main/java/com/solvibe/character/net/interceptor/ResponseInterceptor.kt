package com.solvibe.character.net.interceptor

import android.app.Application
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.text.TextUtils
import com.solvibe.character.net.Resp
import com.solvibe.character.net.error.DefaultErrorResponse
import com.solvibe.character.net.error.ErrorResponseInterface
import com.solvibe.character.net.error.NetError
import com.solvibe.utils.base.BaseApplication
import com.solvibe.utils.ext.jsonAsOrNull
import com.solvibe.utils.ext.logv
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import java.nio.charset.Charset

/**
 * <AUTHOR>
 * 统一处理异常，所有错误会通过Response返回 Response json，包括无网络，服务器异常等
 */
class ResponseInterceptor(
    private val application: BaseApplication,
    private val errorBuilder: ErrorResponseInterface = DefaultErrorResponse(application)
) : Interceptor {
    private val mNetworkUtil = Network(application)

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var response: Response
        try {
            response = chain.proceed(request)
        } catch (_: IOException) {
            return if (mNetworkUtil.isConnected()) {
                errorBuilder.buildErrorResp(request, NetError.NETWORK_ERROR)
            } else {
                errorBuilder.buildErrorResp(request, NetError.NOT_NETWORK)
            }
        }

        //请求错误
        if (!response.isSuccessful) {
            return errorBuilder.reBuildErrorResp(response, response.code)
        }

        val body = bufferBody(response)

        if (TextUtils.isEmpty(body) || "null".equals(body, ignoreCase = true)) {
            return errorBuilder.reBuildErrorResp(response, NetError.NULL_DATA)
        }
        if (body.startsWith("data:")) {
            logv("流式消息响应")
        } else {
            val resp = body.jsonAsOrNull<Resp<*>>()
            if (resp?.code == 50004) {
                application.dealLogout()
            }
        }
        return response
    }

    private fun bufferBody(response: Response): String {
        val source = response.body.source()
        source.request(Long.MAX_VALUE)
        val buffer = source.buffer
        return buffer.clone().readString(Charset.forName("UTF-8"))
    }

    class Network(private val context: Application) {
        fun isConnected(): Boolean {
            val info = getNetworkInfo()
            return info != null && info.isConnected
        }

        fun getNetworkInfo(): NetworkInfo? {
            val cm = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            return cm.activeNetworkInfo
        }
    }
}
