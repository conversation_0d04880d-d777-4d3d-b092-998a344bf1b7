package com.solvibe.character.net

import okhttp3.ResponseBody
import retrofit2.Converter
import retrofit2.Retrofit
import java.lang.reflect.Type

class StringConverterFactory : Converter.Factory() {

    override fun responseBodyConverter(
        type: Type,
        annotations: Array<out Annotation>,
        retrofit: Retrofit
    ): Converter<ResponseBody, String>? {
        if (getRawType(type) != String::class.java) {
            return null
        }
        return Converter<ResponseBody, String> { value ->
            value.string()
        }
    }
}