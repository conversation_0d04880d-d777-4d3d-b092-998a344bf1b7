package com.solvibe.character.net

import com.solvibe.character.net.client.IgnoreStringForObject
import com.solvibe.character.net.client.SingleToArray
import com.squareup.moshi.JsonClass

//普通响应体返回
@JsonClass(generateAdapter = true)
data class Response<T>(
    val code: Int?,
    @IgnoreStringForObject val `data`: T?,
    val msg: String?
)

/**
 * List兼容模式（后台存在脏数据，data本应返回list，在异常情况下又会返回object，导致解析异常），
 * 这里使用自定义jsonAdapter，将data强制解析为list
 */
@JsonClass(generateAdapter = true)
data class ListResponse<T>(
    val code: Int,
    @SingleToArray val `data`: T,
    val msg: String
)

/**
 *@decription:code==1时代表成功，message而不是msg数据模式
 *@param:
 *@return:
 *@author:lin<PERSON><PERSON>
 *@time:2023/2/8 14:13
 */
@JsonClass(generateAdapter = true)
data class Resp<T>(
    val code: Int?,
    val `data`: T?,
    val message: String?
)
