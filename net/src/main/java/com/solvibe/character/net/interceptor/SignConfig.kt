package com.solvibe.character.net.interceptor

/**
 *creater:l<PERSON><PERSON><PERSON> on 2025/5/30 11:44
 */
object SignConfig {
    //lac
    const val SIGN_LAC_DEBUG_DOMAIN = "lac-api-solvibe.ifonelab.net"
    const val SIGN_LAC_RELEASE_DOMAIN = "lac-api.solvibeai.com"

    //solvibe 后台接口
    const val SIGN_SOLVIBE_BACKSTAGE_DEBUG_DOMAIN = "solvibe-api.ifonelab.net"
    const val SIGN_SOLVIBE_BACKSTAGE_RELEASE_DOMAIN = "solvibe-api.irocketx.com"
}