package com.solvibe.character.net.ssl

import java.security.KeyStore
import java.security.KeyStoreException
import java.security.NoSuchAlgorithmException
import java.security.SecureRandom
import java.security.cert.CertificateException
import java.security.cert.X509Certificate
import javax.net.ssl.HostnameVerifier
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManagerFactory
import javax.net.ssl.X509TrustManager

/**
 * @Filed trustSubjects 信任的证书拥有者列表
 * @Filed trustIssuers 信任的证书颁发者列表
 * @Filed trustHostNames 信任的主机名列表
 */
class DefaultHttpsConfig(
    private val trustSubjects: List<String>,
    private val trustIssuers: List<String>,
    private val trustHostNames: List<String>
) : HttpsConfigInterface {
    override fun providerTrustManager() = object : X509TrustManager {
        override fun checkClientTrusted(chain: Array<out X509Certificate>?, authType: String?) {
        }

        //检查服务器证书
        override fun checkServerTrusted(chain: Array<out X509Certificate>?, authType: String?) {
            //检查所有证书
            try {
                val factory: TrustManagerFactory = TrustManagerFactory.getInstance("X509")
                factory.init(null as KeyStore?)
                for (trustManager in factory.trustManagers) {
                    (trustManager as X509TrustManager).checkServerTrusted(chain, authType)
                }
            } catch (e: NoSuchAlgorithmException) {
                e.printStackTrace()
            } catch (e: KeyStoreException) {
                e.printStackTrace()
            }

            //获取网络中的证书信息
            val certificate = chain!![0]
            // 证书拥有者
            val subject = certificate.subjectDN.name
            // 证书颁发者
            val issuer = certificate.issuerDN.name

            if (!trustSubjects.contains(subject) || !trustIssuers.contains(issuer)) {
                throw CertificateException()
            }
        }

        override fun getAcceptedIssuers(): Array<X509Certificate?> {
            return arrayOfNulls(0)
        }
    }

    override fun providerHostNameVerifier() = HostnameVerifier { hostname, session ->
        if (trustHostNames.contains(hostname)) {
            return@HostnameVerifier true
        }
        return@HostnameVerifier false
    }

    override fun providerSSLSocketFactory(): SSLSocketFactory {
        val sslContext = SSLContext.getInstance("TLS")
        sslContext.init(null, arrayOf(providerTrustManager()), SecureRandom())
        return sslContext.socketFactory
    }
}