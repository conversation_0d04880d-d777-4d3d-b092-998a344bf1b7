package com.solvibe.character.net.client

import com.solvibe.character.net.FastJsonConverterFactory
import com.solvibe.character.net.StringConverterFactory
import com.solvibe.character.net.interceptor.RequestInterceptor
import com.solvibe.character.net.interceptor.ResponseInterceptor
import com.solvibe.character.net.ssl.HttpsConfigInterface
import com.solvibe.character.net.ssl.IgnoreHttpsConfig
import com.solvibe.character.net.util.sMoshi
import com.solvibe.utils.base.BaseApplication
import com.solvibe.utils.ext.logv
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Converter
import retrofit2.Retrofit
import retrofit2.converter.moshi.MoshiConverterFactory
import java.util.concurrent.TimeUnit

private fun newOkHttpClient(
    application: BaseApplication,
    config: HttpsConfigInterface = IgnoreHttpsConfig(),
    timeout: Long = 180_000L,
): OkHttpClient {
    return OkHttpClient.Builder()
        .sslSocketFactory(config.providerSSLSocketFactory(), config.providerTrustManager())
        .hostnameVerifier(config.providerHostNameVerifier())
        .connectTimeout(timeout, TimeUnit.MILLISECONDS)
        .callTimeout(timeout, TimeUnit.MILLISECONDS)
        .readTimeout(timeout, TimeUnit.MILLISECONDS)
        .writeTimeout(timeout, TimeUnit.MILLISECONDS)
        .addInterceptor(RequestInterceptor(application))
        .addInterceptor(ResponseInterceptor(application))
        .apply {
            if (BaseApplication.showLog) {
                val logging = HttpLoggingInterceptor(object : HttpLoggingInterceptor.Logger {
                    override fun log(message: String) {
                        logv(message, "http_logging")
                    }
                })
                logging.level = HttpLoggingInterceptor.Level.BODY
                addInterceptor(logging)
            }
        }
        .build()
}

fun newOkHttpSSEClient(
    application: BaseApplication,
    config: HttpsConfigInterface = IgnoreHttpsConfig(),
    timeout: Long = 180_000L,
): OkHttpClient {
    return OkHttpClient.Builder()
        .sslSocketFactory(config.providerSSLSocketFactory(), config.providerTrustManager())
        .hostnameVerifier(config.providerHostNameVerifier())
        .connectTimeout(timeout, TimeUnit.MILLISECONDS)
        .callTimeout(timeout, TimeUnit.MILLISECONDS)
        .readTimeout(timeout, TimeUnit.MILLISECONDS)
        .writeTimeout(timeout, TimeUnit.MILLISECONDS)
        .addInterceptor(RequestInterceptor(application))
        .apply {
            if (BaseApplication.showLog) {
                val logging = HttpLoggingInterceptor(object : HttpLoggingInterceptor.Logger {
                    override fun log(message: String) {
                        logv(message, "http_logging")
                    }
                })
                logging.level = HttpLoggingInterceptor.Level.BASIC
                addInterceptor(logging)
            }
        }
        .build()
}

private fun newRetrofit(
    baseUrl: String,
    application: BaseApplication,
    converter: Converter.Factory = MoshiConverterFactory.create(),
): Retrofit {
    return Retrofit.Builder()
        .baseUrl(baseUrl)
        .client(newOkHttpClient(application))
        .addConverterFactory(converter)
        .addConverterFactory(StringConverterFactory())
        .build()
}

fun newMoshiRetrofit(application: BaseApplication, baseUrl: String): Retrofit {
    return newRetrofit(baseUrl, application, MoshiConverterFactory.create(sMoshi).asLenient())
}

fun newFastJsonRetrofit(application: BaseApplication, baseUrl: String): Retrofit {
    return newRetrofit(baseUrl, application, FastJsonConverterFactory.create())
}

inline fun <reified T> newMoshiRetrofitApi(baseUrl: String): T {
    return newMoshiRetrofit(BaseApplication.getInstance(), baseUrl)
        .create(T::class.java)
}

inline fun <reified T> newFastJsonRetrofitApi(baseUrl: String): T {
    return newFastJsonRetrofit(BaseApplication.getInstance(), baseUrl)
        .create(T::class.java)
}