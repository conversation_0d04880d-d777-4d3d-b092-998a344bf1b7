package com.solvibe.character.net.ws

import com.google.gson.annotations.SerializedName
import com.solvibe.utils.ext.jsonAsOrNull
import com.solvibe.utils.ext.logv
import com.solvibe.utils.ext.toJsonOrNull
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import okio.ByteString
import java.util.concurrent.TimeUnit

object WSManager {
    private val scope = CoroutineScope(Dispatchers.IO)

    private val okHttpClient by lazy {
        OkHttpClient.Builder()
            .pingInterval(10, TimeUnit.SECONDS) // 设置 PING 帧发送间隔
            .build()
    }

    private var ws: WebSocket? = null
    private val _wsDataFlow = MutableSharedFlow<WsData>()
    val wsDataFlow = _wsDataFlow.asSharedFlow()

    private var lastDomain: String? = null
    private var lastToken: String? = null

    fun open(domain: String, token: String) {
        if (lastToken == token) {
            return
        }

        close()
        lastDomain = domain
        lastToken = token

        val request = Request.Builder()
            .url("${domain}ws?token=${token}")
            .build()
        showLog("websocket 连接中: ${request.url}")
        ws = okHttpClient.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                super.onOpen(webSocket, response)
                showLog("websocket 已连接")
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                super.onMessage(webSocket, text)
                dispatchMessage(text)
            }

            override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
                super.onMessage(webSocket, bytes)
                dispatchMessage(bytes.utf8())
            }

            private fun dispatchMessage(msg: String) {
                showLog("websocket 收到消息：$msg")
                scope.launch {
                    _wsDataFlow.emit(parseMessage(msg) ?: return@launch)
                }
            }

            private fun parseMessage(msg: String): WsData? {
                val msgMap = msg.jsonAsOrNull<Map<String, Any>>()
                return when (msgMap?.get("type")) {
                    "score" -> msgMap["data"]?.toJsonOrNull().jsonAsOrNull<WsData.IntimacyData>()
                    else -> null
                }
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                super.onFailure(webSocket, t, response)
                showLog("websocket onFailure：${t.stackTraceToString()}")
                reconnect()
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosing(webSocket, code, reason)
                showLog("websocket 关闭中")
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosed(webSocket, code, reason)
                showLog("websocket 已关闭")
            }
        })
    }

    private fun showLog(log: String) {
        logv(log, "websocket_logging")
    }

    private fun reconnect() {
        val domain = lastDomain ?: return
        val token = lastToken ?: return
        close()
        open(domain, token)
    }

    fun close() {
        ws?.close(1000, null)
        ws = null
        lastToken = null
    }
}

sealed interface WsData {
    data class IntimacyData(
        @SerializedName("raw_level")
        val rawLevel: Int,
        @SerializedName("final_level")
        val finalLevel: Int,
        val score: Int,
        @SerializedName("final_score")
        val finalScore: Int,
    ) : WsData
}