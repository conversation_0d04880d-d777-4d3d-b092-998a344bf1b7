package com.solvibe.character.net.client

import com.squareup.moshi.*

import java.lang.reflect.Type

/**
 * moshi自定义解析器，为处理后台返回格式时而是string，时而是对象的问题
 */
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.FIELD)
@JsonQualifier
annotation class IgnoreStringForObject

class IgnoreStringForObjectAdapter(
    private val delegateAdapter: JsonAdapter<Any>
) : JsonAdapter<Any>() {

    companion object {
        val INSTANCE = IgnoreStringForObjectAdapterFactory()
    }

    override fun from<PERSON><PERSON>(reader: JsonReader): Any? {
        val peek = reader.peek()
        if (peek != JsonReader.Token.BEGIN_OBJECT) {
            reader.skipValue()
            return null
        }
        return delegateAdapter.fromJson(reader)
    }


    override fun toJson(writer: JsonWriter, value: Any?) =
        throw UnsupportedOperationException("SingleToArrayAdapter is only used to deserialize objects")

    class IgnoreStringForObjectAdapterFactory : Factory {
        override fun create(
            type: Type,
            annotations: Set<Annotation>,
            moshi: <PERSON><PERSON>
        ): JsonAdapter<Any>? {
            val delegateAnnotations =
                Types.nextAnnotations(annotations, IgnoreStringForObject::class.java) ?: return null
            val delegateAdapter: JsonAdapter<Any> = moshi.adapter(type, delegateAnnotations)
            return IgnoreStringForObjectAdapter(delegateAdapter)
        }
    }
}